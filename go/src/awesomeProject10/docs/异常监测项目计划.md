| 功能分类         | 模块/接口                     | 功能名称                        | 功能描述             | 输入参数                                  | 输出结果                      | Go替代可行性   |
| ---------------- | ----------------------------- | ------------------------------- | -------------------- | ----------------------------------------- | ----------------------------- | -------------- |
| **核心检测**     | `detect.py`                   | `value_predict()`               | 量值异常检测         | 时间序列数据(dataA/B/C)、窗口大小、任务ID | 检测结果(0异常/1正常)、概率值 | ✅ 可行         |
| **核心检测**     | `detect.py`                   | `rate_predict()`                | 率值异常检测         | 时间序列数据(dataA/B/C)                   | 检测结果(0异常/1正常)、概率值 | ✅ 可行         |
| **统计算法**     | `statistic.py`                | `predict()`                     | 3σ原则统计检测       | 时间序列数据                              | 检测结果(0异常/1正常)         | ✅ 可行         |
| **EWMA算法**     | `ewma.py`                     | `predict()`                     | 指数加权移动平均检测 | 时间序列数据                              | 检测结果(0异常/1正常)         | ✅ 可行         |
| **多项式算法**   | `polynomial_interpolation.py` | `predict()`                     | 多项式回归检测       | 时间序列数据、窗口大小                    | 检测结果(0异常/1正常)         | ✅ 可行         |
| **孤立森林**     | `isolation_forest.py`         | `predict()`                     | 孤立森林无监督检测   | 时间序列数据、窗口大小                    | 检测结果(0异常/1正常)         | ✅ 可行         |
| **组合算法**     | `ewma_and_polynomial.py`      | `predict()`                     | EWMA和多项式组合检测 | 时间序列数据、窗口大小                    | 检测结果(0异常/1正常)         | ✅ 可行         |
| **XGBoost训练**  | `xgboosting.py`               | `xgb_train()`                   | XGBoost模型训练      | 训练数据集、任务ID、迭代轮数              | 训练状态码、错误信息          | ⚠️ 需要第三方库 |
| **XGBoost预测**  | `xgboosting.py`               | `predict()`                     | XGBoost模型预测      | 时间序列数据、窗口大小、模型名称          | 预测结果、概率值              | ⚠️ 需要第三方库 |
| **GBDT训练**     | `gbdt.py`                     | `gbdt_train()`                  | GBDT模型训练         | 训练数据集、任务ID、窗口大小              | 训练状态码、错误信息          | ⚠️ 需要第三方库 |
| **GBDT预测**     | `gbdt.py`                     | `predict()`                     | GBDT模型预测         | 时间序列数据、窗口大小、模型名称          | 预测结果、概率值              | ⚠️ 需要第三方库 |
| **特征提取**     | `feature_service.py`          | `extract_features()`            | 提取时间序列特征     | 时间序列数据、窗口大小                    | 特征向量列表                  | ✅ 可行         |
| **统计特征**     | `statistical_features.py`     | `get_statistical_features()`    | 提取统计特征         | 时间序列数据                              | 统计特征列表                  | ✅ 可行         |
| **拟合特征**     | `fitting_features.py`         | `get_fitting_features()`        | 提取拟合特征         | 时间序列数据列表                          | 拟合特征列表                  | ✅ 可行         |
| **分类特征**     | `classification_features.py`  | `get_classification_features()` | 提取分类特征         | 时间序列数据                              | 分类特征列表                  | ✅ 可行         |
| **模型训练服务** | `detect_service.py`           | `process_train()`               | 处理模型训练请求     | 训练参数(时间范围、数据源等)              | 训练任务状态                  | ✅ 可行         |
| **训练任务管理** | `task_service.py`             | `query_train()`                 | 查询训练任务         | 查询条件                                  | 训练任务列表                  | ✅ 可行         |
| **训练任务管理** | `task_service.py`             | `delete_train()`                | 删除训练任务         | 任务ID                                    | 删除结果                      | ✅ 可行         |
| **样本管理**     | `sample_service.py`           | `import_sample()`               | 导入训练样本         | 样本数据                                  | 导入结果                      | ✅ 可行         |
| **样本管理**     | `sample_service.py`           | `query_sample()`                | 查询样本数据         | 查询条件                                  | 样本列表                      | ✅ 可行         |
| **样本管理**     | `sample_service.py`           | `update_sample()`               | 更新样本标签         | 样本ID和新标签                            | 更新结果                      | ✅ 可行         |
| **样本管理**     | `sample_service.py`           | `delete_sample()`               | 删除样本数据         | 样本ID                                    | 删除结果                      | ✅ 可行         |
| **样本管理**     | `sample_service.py`           | `count_sample()`                | 统计样本数量         | 查询条件                                  | 样本统计信息                  | ✅ 可行         |
| **样本管理**     | `sample_service.py`           | `query_sample_source()`         | 查询样本来源         | 无                                        | 样本来源列表                  | ✅ 可行         |
| **样本管理**     | `sample_service.py`           | `sample_download()`             | 下载样本数据         | 样本ID列表                                | CSV文件                       | ✅ 可行         |
| **异常管理**     | `anomaly_service.py`          | `query_anomaly()`               | 查询异常记录         | 查询条件                                  | 异常记录列表                  | ✅ 可行         |
| **异常管理**     | `anomaly_service.py`          | `update_anomaly()`              | 更新异常状态         | 异常ID和状态                              | 更新结果                      | ✅ 可行         |
| **HTTP API**     | `/PredictValue`               | `predict_value()`               | HTTP量值检测接口     | POST请求体                                | JSON响应                      | ✅ 可行         |
| **HTTP API**     | `/PredictRate`                | `predict_rate()`                | HTTP率值检测接口     | POST请求体                                | JSON响应                      | ✅ 可行         |
| **HTTP API**     | `/Train`                      | `train()`                       | HTTP模型训练接口     | POST请求体                                | JSON响应                      | ✅ 可行         |
| **HTTP API**     | `/SearchAnomaly`              | `search_anomaly()`              | HTTP异常查询接口     | POST请求体                                | JSON响应                      | ✅ 可行         |
| **HTTP API**     | `/ImportSample`               | `import_sample()`               | HTTP样本导入接口     | 文件上传                                  | JSON响应                      | ✅ 可行         |
| **HTTP API**     | `/UpdateSample`               | `update_sample()`               | HTTP样本更新接口     | POST请求体                                | JSON响应                      | ✅ 可行         |
| **HTTP API**     | `/QuerySample`                | `query_sample()`                | HTTP样本查询接口     | POST请求体                                | JSON响应                      | ✅ 可行         |
| **HTTP API**     | `/DeleteSample`               | `delete_sample()`               | HTTP样本删除接口     | POST请求体                                | JSON响应                      | ✅ 可行         |
| **HTTP API**     | `/CountSample`                | `count_sample()`                | HTTP样本统计接口     | POST请求体                                | JSON响应                      | ✅ 可行         |
| **HTTP API**     | `/UpdateAnomaly`              | `update_anomaly()`              | HTTP异常更新接口     | POST请求体                                | JSON响应                      | ✅ 可行         |
| **HTTP API**     | `/DownloadSample`             | `download_sample()`             | HTTP样本下载接口     | GET请求                                   | 文件响应                      | ✅ 可行         |
| **HTTP API**     | `/QueryTrain`                 | `query_train_task()`            | HTTP训练任务查询接口 | POST请求体                                | JSON响应                      | ✅ 可行         |
| **HTTP API**     | `/QueryTrainSource`           | `query_train_source()`          | HTTP训练来源查询接口 | POST请求体                                | JSON响应                      | ✅ 可行         |
| **HTTP API**     | `/DeleteTrain`                | `delete_train_task()`           | HTTP训练任务删除接口 | POST请求体                                | JSON响应                      | ✅ 可行         |
| **C/C++ API**    | `detect.h`                    | `load_model()`                  | 加载模型             | 模型文件路径                              | 模型句柄                      | ✅ 可行         |
| **C/C++ API**    | `detect.h`                    | `value_predict()`               | C接口量值检测        | ValueData结构体                           | 检测结果、概率值              | ✅ 可行         |
| **C/C++ API**    | `detect.h`                    | `rate_predict()`                | C接口率值检测        | RateData结构体                            | 检测结果、概率值              | ✅ 可行         |
| **数据访问**     | `sample_op.py`                | `import_sample()`               | 样本数据入库         | 样本数据                                  | 插入结果                      | ✅ 可行         |
| **数据访问**     | `sample_op.py`                | `query_sample()`                | 查询样本数据         | 查询条件                                  | 样本列表                      | ✅ 可行         |
| **数据访问**     | `sample_op.py`                | `update_sample()`               | 更新样本数据         | 样本数据                                  | 更新结果                      | ✅ 可行         |
| **数据访问**     | `sample_op.py`                | `delete_sample()`               | 删除样本数据         | 样本ID                                    | 删除结果                      | ✅ 可行         |
| **数据访问**     | `anomaly_op.py`               | `insert_anomaly()`              | 异常记录入库         | 异常数据                                  | 插入结果                      | ✅ 可行         |
| **数据访问**     | `anomaly_op.py`               | `get_anomaly()`                 | 查询异常记录         | 查询条件                                  | 异常记录列表                  | ✅ 可行         |
| **数据访问**     | `anomaly_op.py`               | `update_anomaly()`              | 更新异常记录         | 异常数据                                  | 更新结果                      | ✅ 可行         |
| **数据访问**     | `train_op.py`                 | `insert_train_info()`           | 训练任务信息入库     | 训练参数                                  | 插入结果                      | ✅ 可行         |
| **数据访问**     | `train_op.py`                 | `query_train()`                 | 查询训练任务         | 查询条件                                  | 训练任务列表                  | ✅ 可行         |
| **数据访问**     | `train_op.py`                 | `delete_train()`                | 删除训练任务         | 任务ID                                    | 删除结果                      | ✅ 可行         |
|                  |                               |                                 |                      |                                           |                               |                |

