# libdetect.so 重写实施摘要

## 重写目标

**原因**：原始libdetect.so无源码，仅支持CentOS7.2+，需要支持多架构部署
**目标**：使用C++重新实现，保持API兼容，支持x86_64/ARM64/macOS等多平台

## 核心API需求

### 必须实现的3个函数
```c
void* load_model(const char *fname);                    // 加载XGBoost模型
int value_predict(void* handle, ValueData* data, 
                  int* result, float* prob);            // 量值检测
int rate_predict(RateData* data, int* result, float* prob); // 率值检测
```

### 数据结构
```c
typedef struct {
    int* data_a;    // 181个数据点
    int* data_b;    // 361个数据点  
    int* data_c;    // 361个数据点
    int len_a, len_b, len_c;
} ValueData;

typedef struct {
    double* data_a, *data_b, *data_c;  // 同上，double类型
    int len_a, len_b, len_c;
} RateData;
```

## 技术方案

### 实现语言：C++
- **XGBoost推理**：使用官方XGBoost C++ API
- **数学计算**：Eigen3库或标准库实现
- **构建系统**：CMake跨平台构建
- **接口兼容**：extern "C"保持C接口

### 核心算法

**1. 模型加载**
```cpp
void* load_model(const char *fname) {
    BoosterHandle booster;
    XGBoosterCreate(nullptr, 0, &booster);
    XGBoosterLoadModel(booster, fname);
    return static_cast<void*>(booster);
}
```

**2. 量值检测流程**
```
输入数据 → 特征提取 → XGBoost推理 → 概率计算 → 异常判断
```

**3. 率值检测算法**
```
输入数据 → 统计分析 → 3σ检测 → 概率计算 → 异常判断
```

## 实施计划

### 阶段1：基础搭建 (1周)
- [x] 项目结构设计
- [x] CMake配置
- [x] 基础类框架
- [x] 依赖库集成

### 阶段2：模型加载 (1周)  
- [x] XGBoost模型加载实现
- [x] 模型验证机制
- [x] 错误处理和内存管理

### 阶段3：量值检测 (1.5周)
- [x] 特征提取算法实现
- [x] XGBoost推理接口
- [x] 结果处理和概率计算

### 阶段4：率值检测 (1周)
- [x] 无监督检测算法
- [x] 统计分析实现
- [x] 多种检测策略

### 阶段5：测试验证 (1周)
- [x] 单元测试编写
- [x] 集成测试验证
- [x] 多平台兼容性测试

### 阶段6：构建部署 (0.5周)
- [x] 构建脚本完善
- [x] 多平台编译
- [x] 文档和示例

## 项目结构

```
libdetect/
├── CMakeLists.txt              # 构建配置
├── include/detect.h            # 公共头文件(与原版兼容)
├── src/
│   ├── detect.cpp             # 主要API实现
│   ├── model_loader.cpp       # 模型加载
│   ├── value_detector.cpp     # 量值检测
│   ├── rate_detector.cpp      # 率值检测
│   ├── feature_extractor.cpp  # 特征提取
│   └── utils.cpp              # 工具函数
├── tests/
│   ├── test_main.cpp          # 测试主程序
│   ├── test_model_loader.cpp  # 模型加载测试
│   ├── test_value_predict.cpp # 量值检测测试
│   ├── test_rate_predict.cpp  # 率值检测测试
│   └── test_data/             # 测试数据
├── examples/
│   ├── example.c              # C语言使用示例
│   └── example.cpp            # C++使用示例
└── build/                     # 构建目录
```

## 关键技术点

### 1. 特征提取算法
```cpp
// 统计特征：均值、方差、偏度、峰度、分位数
void extractStatisticalFeatures(const int* data, int len, std::vector<float>& features);

// 拟合特征：线性回归系数、多项式拟合参数  
void extractFittingFeatures(const ValueData* data, std::vector<float>& features);

// 分类特征：趋势方向、周期性、异常点数量
void extractClassificationFeatures(const ValueData* data, std::vector<float>& features);
```

### 2. XGBoost推理
```cpp
int performXGBoostInference(void* model, const std::vector<float>& features, 
                           float* prob) {
    DMatrixHandle dmat;
    XGDMatrixCreateFromMat(features.data(), 1, features.size(), NAN, &dmat);
    
    bst_ulong out_len;
    const float* out_result;
    XGBoosterPredict(static_cast<BoosterHandle>(model), dmat, 0, 0, 0, 
                     &out_len, &out_result);
    
    *prob = out_result[0];
    XGDMatrixFree(dmat);
    return TSD_SUCCESS;
}
```

### 3. 无监督检测
```cpp
int statisticalDetection(const RateData* data, float* prob) {
    // 计算统计量
    float mean = calculateMean(data->data_a, data->len_a);
    float std_dev = calculateStdDev(data->data_a, data->len_a, mean);
    
    // Z-score检测
    double last_point = data->data_a[data->len_a - 1];
    float z_score = std::abs((last_point - mean) / std_dev);
    
    *prob = 1.0f - std::min(z_score / 3.0f, 1.0f);
    return (z_score > 3.0f) ? 0 : 1;  // 0:异常, 1:正常
}
```

## 构建和部署

### 构建命令
```bash
# 创建构建目录
mkdir build && cd build

# 配置构建
cmake .. -DCMAKE_BUILD_TYPE=Release \
         -DCMAKE_POSITION_INDEPENDENT_CODE=ON

# 编译
make -j$(nproc)

# 安装
make install
```

### 多平台支持
- **Linux x86_64**: `libdetect.so`
- **Linux ARM64**: `libdetect_arm64.so`  
- **macOS x86_64**: `libdetect.dylib`
- **macOS ARM64**: `libdetect_arm64.dylib`

## 验证标准

### 功能验证
1. **API兼容性**: 与原始detect.h完全兼容
2. **结果一致性**: 相同输入产生相同输出
3. **性能要求**: 推理速度不低于原始库

### 质量标准
1. **内存安全**: 无内存泄漏，通过Valgrind检测
2. **线程安全**: 支持多线程并发调用
3. **异常处理**: 完善的错误处理和恢复机制

### 测试用例
```c
// 基础功能测试
void test_load_model();
void test_value_predict_normal();
void test_value_predict_abnormal();
void test_rate_predict_normal();
void test_rate_predict_abnormal();

// 边界条件测试
void test_null_parameters();
void test_invalid_model_file();
void test_empty_data_arrays();
void test_large_data_arrays();

// 性能测试
void benchmark_value_predict();
void benchmark_rate_predict();
void benchmark_model_loading();
```

## 预期成果

### 交付物
1. **源代码**: 完整的C++实现，包含注释和文档
2. **构建系统**: CMake配置，支持多平台编译
3. **测试套件**: 完整的单元测试和集成测试
4. **二进制库**: 多平台的动态库文件
5. **使用文档**: API文档、编译指南、使用示例

### 技术优势
1. **跨平台**: 支持Linux、macOS、Windows多系统
2. **多架构**: 支持x86_64、ARM64等架构
3. **高性能**: 优化的算法实现，高效的内存管理
4. **易维护**: 清晰的代码结构，完善的测试覆盖
5. **可扩展**: 模块化设计，便于添加新算法

## 总结

通过C++重写libdetect.so，我们将获得：
- ✅ **完全的API兼容性**
- ✅ **多平台支持能力** 
- ✅ **可维护的源代码**
- ✅ **高质量的测试覆盖**
- ✅ **灵活的扩展能力**

**总工期**: 5.5周
**风险等级**: 中等 (主要是XGBoost C++ API学习成本)
**成功概率**: 高 (技术方案成熟，需求明确)
