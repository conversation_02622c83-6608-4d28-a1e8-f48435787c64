

##  函数功能对照表

| 函数名 | 功能描述 | 输入参数 | 输出结果 |
|--------|----------|----------|----------|
| **HTTP API** |
| `POST /PredictValue` | 量值异常检测 | `viewId`, `viewName`, `attrId`, `attrName`, `taskId`(可选), `window`, `time`, `dataC`, `dataB`, `dataA` | `{"code":0, "msg":"操作成功", "data":{"ret":0, "p":"0.05"}}` |
| `POST /PredictRate` | 率值异常检测 | `viewId`, `viewName`, `attrId`, `attrName`, `window`, `time`, `dataC`, `dataB`, `dataA` | `{"code":0, "msg":"操作成功", "data":{"ret":0, "p":"0"}}` |
| **Python API** |
| `detect.Detect().value_predict()` | Python量值检测 | `{"window":180, "dataC":"...", "dataB":"...", "dataA":"...", "taskId":"..."}` | `code, {"ret":0, "p":"0.05"}` |
| `detect.Detect().rate_predict()` | Python率值检测 | `{"dataC":"...", "dataB":"...", "dataA":"..."}` | `code, {"ret":0, "p":"0"}` |
| **C库API** |
| `load_model()` | 加载XGBoost模型 | `const char *fname` (模型文件路径) | `void*` (模型句柄，NULL表示失败) |
| `value_predict()` | C库量值检测 | `void *mhandle`, `ValueData* data`, `int* sample_result`, `float* prob` | `int` (0成功，<0失败) |
| `rate_predict()` | C库率值检测 | `RateData* data`, `int* sample_result`, `float* prob` | `int` (0成功，<0失败) |

### 参数详细说明

#### 通用输入参数：
- **dataA**: 待检测点+前180个数据点 (181个点)
- **dataB**: 昨日同比数据 (361个点)  
- **dataC**: 一周前同比数据 (361个点)
- **window**: 固定值180
- **taskId**: 自定义模型ID (仅量值检测)

#### 通用输出结果：
- **ret**: 0=异常，1=正常
- **p**: 概率值 (越小越异常)
- **code**: 0=成功，非0=失败


