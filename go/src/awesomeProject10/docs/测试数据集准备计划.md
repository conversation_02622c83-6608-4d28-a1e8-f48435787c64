# libdetect.so 测试数据集准备计划

## 数据集需求分析

### 数据集用途
1. **训练数据集**: 用于Python训练XGBoost模型
2. **验证数据集**: 验证C++实现与Python实现的一致性
3. **测试用例**: 单元测试和集成测试
4. **基准数据**: 性能测试和压力测试

### 数据格式要求

**时间序列数据格式**:
- **dataA**: 181个数据点 (待检测点+前180个数据)
- **dataB**: 361个数据点 (昨日同时刻点+前后各180个数据)  
- **dataC**: 361个数据点 (一周前同时刻点+前后各180个数据)
- **标签**: 0(异常) / 1(正常)

## 数据集结构设计

```
test_data/
├── training_data/                    # 训练数据集
│   ├── kpi_normal_samples.csv       # KPI正常样本 (1000条)
│   ├── kpi_abnormal_samples.csv     # KPI异常样本 (500条)
│   ├── rate_normal_samples.csv      # 成功率正常样本 (800条)
│   ├── rate_abnormal_samples.csv    # 成功率异常样本 (400条)
│   └── mixed_training_set.csv       # 混合训练集 (2700条)
├── validation_data/                  # 验证数据集
│   ├── python_validation.csv        # Python验证集 (300条)
│   └── cpp_validation.csv           # C++验证集 (300条)
├── test_cases/                       # 测试用例
│   ├── value_predict/               # 量值检测测试
│   │   ├── normal_cases.json       # 正常情况 (20个用例)
│   │   ├── abnormal_cases.json     # 异常情况 (20个用例)
│   │   ├── boundary_cases.json     # 边界情况 (10个用例)
│   │   └── edge_cases.json         # 极端情况 (10个用例)
│   ├── rate_predict/               # 率值检测测试
│   │   ├── normal_cases.json       # 正常情况 (15个用例)
│   │   ├── abnormal_cases.json     # 异常情况 (15个用例)
│   │   └── boundary_cases.json     # 边界情况 (10个用例)
│   └── model_loading/              # 模型加载测试
│       ├── valid_model.xgb         # 有效模型文件
│       ├── invalid_model.txt       # 无效模型文件
│       ├── corrupted_model.xgb     # 损坏模型文件
│       └── empty_model.xgb         # 空模型文件
├── benchmark_data/                  # 性能基准数据
│   ├── performance_test.csv        # 性能测试数据 (1000条)
│   ├── stress_test.csv             # 压力测试数据 (10000条)
│   └── memory_test.csv             # 内存测试数据 (100条)
├── reference_results/              # 参考结果
│   ├── python_results.json        # Python实现结果
│   ├── original_lib_results.json  # 原始库结果
│   └── expected_outputs.json      # 期望输出结果
└── data_generation/                # 数据生成脚本
    ├── generate_training_data.py   # 生成训练数据
    ├── generate_test_cases.py      # 生成测试用例
    ├── data_validator.py           # 数据验证
    └── config/
        ├── data_config.yaml        # 数据生成配置
        └── pattern_config.yaml     # 模式配置
```

## 数据生成策略

### 1. 正常数据生成

**KPI指标正常模式**:
```python
def generate_normal_kpi_data():
    """生成正常KPI数据"""
    patterns = [
        'stable',      # 稳定模式
        'trending',    # 趋势模式  
        'seasonal',    # 季节性模式
        'cyclic'       # 周期性模式
    ]
    
    for pattern in patterns:
        data = generate_pattern_data(pattern, noise_level=0.1)
        yield format_time_series_data(data)

def generate_stable_pattern(length=181, base_value=100):
    """生成稳定模式数据"""
    noise = np.random.normal(0, base_value * 0.05, length)
    return base_value + noise

def generate_trending_pattern(length=181, base_value=100, trend=0.1):
    """生成趋势模式数据"""
    trend_component = np.linspace(0, trend * base_value, length)
    noise = np.random.normal(0, base_value * 0.03, length)
    return base_value + trend_component + noise

def generate_seasonal_pattern(length=181, base_value=100, amplitude=20):
    """生成季节性模式数据"""
    x = np.linspace(0, 4 * np.pi, length)
    seasonal = amplitude * np.sin(x)
    noise = np.random.normal(0, base_value * 0.02, length)
    return base_value + seasonal + noise
```

**成功率正常模式**:
```python
def generate_normal_rate_data():
    """生成正常成功率数据"""
    base_rate = np.random.uniform(95.0, 99.9)  # 基础成功率
    
    # 小幅波动
    fluctuation = np.random.normal(0, 0.5, 181)
    rate_data = np.clip(base_rate + fluctuation, 0, 100)
    
    return rate_data
```

### 2. 异常数据生成

**异常模式类型**:
```python
def generate_abnormal_data():
    """生成异常数据"""
    anomaly_types = [
        'spike',           # 尖峰异常
        'dip',            # 骤降异常
        'level_shift',    # 水平偏移
        'trend_change',   # 趋势变化
        'variance_change', # 方差变化
        'outliers'        # 离群点
    ]
    
    for anomaly_type in anomaly_types:
        normal_data = generate_normal_data()
        abnormal_data = inject_anomaly(normal_data, anomaly_type)
        yield abnormal_data

def inject_spike_anomaly(data, position=-1, magnitude=3.0):
    """注入尖峰异常"""
    if position == -1:
        position = len(data) - 1  # 最后一个点
    
    mean_val = np.mean(data[:position])
    std_val = np.std(data[:position])
    
    data[position] = mean_val + magnitude * std_val
    return data

def inject_level_shift_anomaly(data, shift_point=150, shift_magnitude=2.0):
    """注入水平偏移异常"""
    mean_val = np.mean(data[:shift_point])
    std_val = np.std(data[:shift_point])
    
    shift_value = shift_magnitude * std_val
    data[shift_point:] += shift_value
    return data
```

### 3. 边界和极端情况

```python
def generate_boundary_cases():
    """生成边界测试用例"""
    cases = []
    
    # 极小值
    cases.append({
        'data_a': [0.001] * 181,
        'data_b': [0.001] * 361,
        'data_c': [0.001] * 361,
        'expected': {'result': 1, 'probability': 0.9}
    })
    
    # 极大值
    cases.append({
        'data_a': [1e6] * 181,
        'data_b': [1e6] * 361, 
        'data_c': [1e6] * 361,
        'expected': {'result': 1, 'probability': 0.8}
    })
    
    # 全零值
    cases.append({
        'data_a': [0] * 181,
        'data_b': [0] * 361,
        'data_c': [0] * 361,
        'expected': {'result': 1, 'probability': 0.5}
    })
    
    # 负值
    cases.append({
        'data_a': [-100] * 181,
        'data_b': [-100] * 361,
        'data_c': [-100] * 361,
        'expected': {'result': 1, 'probability': 0.7}
    })
    
    return cases
```

## 数据标注和验证

### 自动标注策略
```python
def auto_label_data(data_a, data_b, data_c):
    """自动标注数据"""
    # 使用多种统计方法判断异常
    methods = [
        statistical_test,    # 统计检验
        isolation_forest,    # 孤立森林
        local_outlier,      # 局部异常因子
        zscore_test         # Z-score检验
    ]
    
    votes = []
    for method in methods:
        result = method(data_a, data_b, data_c)
        votes.append(result)
    
    # 多数投票
    label = 1 if sum(votes) > len(votes) / 2 else 0
    confidence = sum(votes) / len(votes)
    
    return label, confidence
```

### 人工验证流程
1. **专家标注**: 领域专家对关键样本进行标注
2. **交叉验证**: 多人标注结果对比
3. **一致性检查**: 标注结果一致性验证
4. **质量评估**: 标注质量评分

## 数据质量保证

### 数据验证检查
```python
def validate_dataset(dataset_path):
    """验证数据集质量"""
    checks = [
        check_data_format,      # 格式检查
        check_data_range,       # 数值范围检查
        check_label_balance,    # 标签平衡检查
        check_data_diversity,   # 数据多样性检查
        check_anomaly_quality   # 异常质量检查
    ]
    
    results = {}
    for check in checks:
        results[check.__name__] = check(dataset_path)
    
    return results

def check_label_balance(dataset):
    """检查标签平衡性"""
    labels = dataset['label'].value_counts()
    balance_ratio = min(labels) / max(labels)
    
    return {
        'balance_ratio': balance_ratio,
        'is_balanced': balance_ratio > 0.3,  # 至少30%的少数类
        'label_distribution': labels.to_dict()
    }
```

## 参考结果生成

### Python参考实现
```python
def generate_reference_results():
    """生成参考结果"""
    # 使用现有Python实现生成结果
    from time_series_detector import detect
    
    detector = detect.Detect()
    results = []
    
    test_cases = load_test_cases()
    for case in test_cases:
        data = {
            'dataA': case['data_a'],
            'dataB': case['data_b'], 
            'dataC': case['data_c'],
            'window': 180
        }
        
        # 量值检测
        if case['type'] == 'value':
            code, result = detector.value_predict(data)
            
        # 率值检测  
        elif case['type'] == 'rate':
            code, result = detector.rate_predict(data)
            
        results.append({
            'case_id': case['id'],
            'input': data,
            'output': result,
            'return_code': code
        })
    
    save_reference_results(results)
```

## 实施时间安排

### 第1周：数据生成框架
- [x] 数据生成脚本开发
- [x] 正常模式数据生成
- [x] 异常模式数据生成

### 第2周：测试用例创建
- [x] 单元测试用例生成
- [x] 边界条件用例创建
- [x] 性能测试数据准备

### 第3周：数据验证和标注
- [x] 自动标注算法实现
- [x] 人工验证流程
- [x] 数据质量检查

### 第4周：参考结果生成
- [x] Python参考实现运行
- [x] 原始库结果收集
- [x] 期望输出定义

这个测试数据集将为libdetect.so的重写提供全面的验证基础，确保新实现的正确性和可靠性。
