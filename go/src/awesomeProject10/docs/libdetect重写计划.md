# libdetect.so 重写实现计划

## 项目背景

由于原始libdetect.so没有源码，且仅支持CentOS7.2+系统，为了支持多架构部署（x86_64、ARM64、macOS等），需要重新实现该库。

## 功能需求分析

### 基于detect.h的API接口

```c
// 核心数据结构
typedef struct {
    int* data_a;      // 181个数据点
    int* data_b;      // 361个数据点  
    int* data_c;      // 361个数据点
    int len_a;
    int len_b;
    int len_c;
} ValueData;

typedef struct {
    double* data_a;   // 181个数据点
    double* data_b;   // 361个数据点
    double* data_c;   // 361个数据点
    int len_a;
    int len_b;
    int len_c;
} RateData;

// 核心API函数
void* load_model(const char *fname);
int value_predict(void* mhandle, ValueData* data, int* sample_result, float* prob);
int rate_predict(RateData* data, int* sample_result, float* prob);
```

### 功能要求

1. **模型加载功能**
   - 支持加载XGBoost模型文件
   - 返回模型句柄供后续推理使用
   - 支持模型文件格式验证

2. **量值异常检测**
   - 输入：模型句柄 + ValueData结构体
   - 处理：使用XGBoost模型进行推理
   - 输出：异常判断(0/1) + 概率值

3. **率值异常检测**  
   - 输入：RateData结构体
   - 处理：使用无监督算法检测
   - 输出：异常判断(0/1) + 概率值

## 技术实现方案

### 方案选择：C++实现

**选择C++的原因**：
- 更好的内存管理和异常处理
- 丰富的机器学习库支持
- 面向对象设计，便于维护
- 兼容C接口，保持API一致性

### 依赖库选择

**XGBoost推理引擎**：
- 使用官方XGBoost C++ API
- 支持多平台编译
- 高性能推理能力

**数学计算库**：
- Eigen3：矩阵运算和数值计算
- 或使用标准库实现基础算法

**构建系统**：
- CMake：跨平台构建
- 支持多架构编译

## 详细实施计划

### 阶段1：项目搭建和基础框架 (1周)

**任务1.1：项目结构设计**
```
libdetect/
├── CMakeLists.txt          # 构建配置
├── include/
│   └── detect.h           # 公共头文件
├── src/
│   ├── detect.cpp         # 主要实现
│   ├── model_loader.cpp   # 模型加载
│   ├── value_detector.cpp # 量值检测
│   ├── rate_detector.cpp  # 率值检测
│   └── utils.cpp          # 工具函数
├── tests/
│   ├── test_main.cpp      # 测试主程序
│   └── test_data/         # 测试数据
├── examples/
│   └── example.c          # 使用示例
└── build/                 # 构建目录
```

**任务1.2：CMake配置**
- 配置跨平台编译
- 集成XGBoost依赖
- 设置编译选项和优化级别

**任务1.3：基础类设计**
```cpp
class ModelLoader {
public:
    void* loadModel(const char* filename);
    bool validateModel(void* handle);
    void releaseModel(void* handle);
};

class ValueDetector {
public:
    int predict(void* model, const ValueData* data, 
                int* result, float* prob);
private:
    int extractFeatures(const ValueData* data, std::vector<float>& features);
};

class RateDetector {
public:
    int predict(const RateData* data, int* result, float* prob);
private:
    int statisticalDetection(const RateData* data, float* prob);
};
```

### 阶段2：模型加载功能实现 (1周)

**任务2.1：XGBoost模型加载**
```cpp
void* load_model(const char *fname) {
    try {
        // 验证文件存在性
        if (!fileExists(fname)) {
            return nullptr;
        }
        
        // 创建XGBoost Booster
        BoosterHandle booster;
        XGBoosterCreate(nullptr, 0, &booster);
        
        // 加载模型文件
        XGBoosterLoadModel(booster, fname);
        
        // 验证模型有效性
        if (!validateModel(booster)) {
            XGBoosterFree(booster);
            return nullptr;
        }
        
        return static_cast<void*>(booster);
    } catch (...) {
        return nullptr;
    }
}
```

**任务2.2：模型验证机制**
- 检查模型文件格式
- 验证模型参数完整性
- 测试模型推理功能

**任务2.3：错误处理**
- 统一错误码定义
- 异常捕获和转换
- 内存泄漏防护

### 阶段3：量值检测实现 (1.5周)

**任务3.1：特征提取算法**
```cpp
int extractFeatures(const ValueData* data, std::vector<float>& features) {
    // 参考Python实现，提取统计特征
    // 1. 基础统计特征
    extractStatisticalFeatures(data->data_a, data->len_a, features);
    extractStatisticalFeatures(data->data_b, data->len_b, features);
    extractStatisticalFeatures(data->data_c, data->len_c, features);
    
    // 2. 拟合特征
    extractFittingFeatures(data, features);
    
    // 3. 分类特征
    extractClassificationFeatures(data, features);
    
    return TSD_SUCCESS;
}
```

**任务3.2：XGBoost推理**
```cpp
int value_predict(void* mhandle, ValueData* data, 
                  int* sample_result, float* prob) {
    if (!mhandle || !data || !sample_result || !prob) {
        return TSD_CHECK_PARAM_FAILED;
    }
    
    try {
        // 提取特征
        std::vector<float> features;
        int ret = extractFeatures(data, features);
        if (ret != TSD_SUCCESS) return ret;
        
        // 创建DMatrix
        DMatrixHandle dmat;
        XGDMatrixCreateFromMat(features.data(), 1, features.size(), 
                               NAN, &dmat);
        
        // 执行预测
        bst_ulong out_len;
        const float* out_result;
        XGBoosterPredict(static_cast<BoosterHandle>(mhandle), 
                         dmat, 0, 0, 0, &out_len, &out_result);
        
        // 处理结果
        *prob = out_result[0];
        *sample_result = (*prob < 0.15) ? 0 : 1;  // 阈值判断
        
        XGDMatrixFree(dmat);
        return TSD_SUCCESS;
    } catch (...) {
        return TSD_TIMESERIES_INIT_ERROR;
    }
}
```

**任务3.3：特征工程实现**
- 统计特征：均值、方差、偏度、峰度、分位数
- 拟合特征：线性回归、多项式拟合参数
- 分类特征：趋势、周期性、异常点数量

### 阶段4：率值检测实现 (1周)

**任务4.1：无监督检测算法**
```cpp
int rate_predict(RateData* data, int* sample_result, float* prob) {
    if (!data || !sample_result || !prob) {
        return TSD_CHECK_PARAM_FAILED;
    }
    
    try {
        // 使用3σ原则进行检测
        float mean, std_dev;
        calculateStatistics(data->data_a, data->len_a, &mean, &std_dev);
        
        // 获取最后一个点
        double last_point = data->data_a[data->len_a - 1];
        
        // 计算Z-score
        float z_score = std::abs((last_point - mean) / std_dev);
        
        // 计算概率值
        *prob = 1.0f - std::min(z_score / 3.0f, 1.0f);
        
        // 判断异常
        *sample_result = (z_score > 3.0f) ? 0 : 1;
        
        return TSD_SUCCESS;
    } catch (...) {
        return TSD_TIMESERIES_INIT_ERROR;
    }
}
```

**任务4.2：多种无监督算法**
- 3σ统计检测
- EWMA指数加权移动平均
- 基于分位数的检测
- 组合算法

### 阶段5：测试和验证 (1周)

**任务5.1：单元测试**
```cpp
// 测试模型加载
void test_load_model() {
    void* handle = load_model("test_model.xgb");
    assert(handle != nullptr);
    // 清理资源
}

// 测试量值检测
void test_value_predict() {
    // 准备测试数据
    ValueData test_data = {...};
    int result;
    float prob;
    
    int ret = value_predict(handle, &test_data, &result, &prob);
    assert(ret == TSD_SUCCESS);
    assert(result == 0 || result == 1);
    assert(prob >= 0.0f && prob <= 1.0f);
}
```

**任务5.2：集成测试**
- 使用原始测试数据验证结果一致性
- 性能基准测试
- 内存泄漏检测

**任务5.3：多平台测试**
- Linux x86_64
- Linux ARM64  
- macOS x86_64
- macOS ARM64 (Apple Silicon)

### 阶段6：构建和部署 (0.5周)

**任务6.1：构建脚本**
```bash
#!/bin/bash
# build.sh - 多平台构建脚本

mkdir -p build
cd build

# 配置构建
cmake .. -DCMAKE_BUILD_TYPE=Release \
         -DCMAKE_POSITION_INDEPENDENT_CODE=ON

# 编译
make -j$(nproc)

# 生成动态库
cp libdetect.so ../lib/
```

**任务6.2：打包发布**
- 生成不同平台的动态库
- 创建安装包
- 编写部署文档

## 验证方案

### 功能验证
1. **API兼容性**：确保与原始库API完全兼容
2. **结果一致性**：使用相同输入验证输出结果
3. **性能对比**：推理速度不低于原始库

### 质量保证
1. **内存安全**：使用Valgrind检测内存泄漏
2. **线程安全**：多线程环境下的稳定性测试
3. **异常处理**：各种边界条件的错误处理

### 平台兼容性
1. **多架构支持**：x86_64、ARM64
2. **多系统支持**：Linux、macOS、Windows
3. **编译器兼容**：GCC、Clang、MSVC

## 预期交付物

1. **源代码**：完整的C++实现代码
2. **构建系统**：CMake配置和构建脚本
3. **测试套件**：单元测试和集成测试
4. **文档**：API文档、编译文档、使用示例
5. **二进制库**：多平台的libdetect.so文件

## 时间安排

- **总工期**：5.5周
- **里程碑1**：基础框架完成 (1周)
- **里程碑2**：模型加载功能 (2周)  
- **里程碑3**：检测功能完成 (4.5周)
- **里程碑4**：测试验证完成 (5.5周)

## 风险控制

1. **技术风险**：XGBoost C++ API学习成本
2. **兼容性风险**：不同平台的编译差异
3. **性能风险**：推理性能可能不如原始库

**应对策略**：
- 提前进行技术预研
- 建立持续集成环境
- 性能优化和算法调优

这个重写计划将确保新的libdetect.so库具有更好的跨平台兼容性，同时保持与原始API的完全兼容。
