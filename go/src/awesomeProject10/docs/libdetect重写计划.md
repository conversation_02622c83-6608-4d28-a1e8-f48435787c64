# libdetect.so 重写计划

## 重写目标
原始libdetect.so无源码，仅支持CentOS7.2+，需要重写支持多架构（x86_64、ARM64、macOS等）

## 需要实现的API
```c
void* load_model(const char *fname);                    // 加载XGBoost模型
int value_predict(void* handle, ValueData* data, 
                  int* result, float* prob);            // 量值检测
int rate_predict(RateData* data, int* result, float* prob); // 率值检测
```

## 实施顺序

### 第1步：项目搭建 (3天)
**任务**:
1. 创建项目目录结构
2. 配置CMake构建系统
3. 集成XGBoost依赖库
4. 编写基础头文件

### 第2步：Conda Python环境搭建 (2天)
**任务**:
1. 创建专用conda环境
2. 安装Metis项目依赖
3. 配置Python训练环境
4. 验证环境可用性

**环境搭建**:
```bash
# 创建conda环境
conda create -n libdetect-train python=3.7
conda activate libdetect-train

# 安装依赖 (基于Metis requirements.txt)
pip install numpy==1.15.2
pip install scikit-learn==0.20.0
pip install xgboost==0.80
pip install pandas
pip install tsfresh==0.11.1

# 设置Python路径
export PYTHONPATH=/Users/<USER>/go/src/awesomeProject10/Metis:$PYTHONPATH
```

### 第3步：测试数据准备 (3天)
**任务**:
1. 收集现有测试样本
2. 生成正常和异常样本
3. 准备训练数据集
4. 创建测试用例

### 第4步：Python训练脚本 (4天)
**任务**:
1. 复用Metis的特征提取代码
2. 实现XGBoost模型训练
3. 生成C++兼容的模型文件
4. 验证模型有效性

**训练脚本**:
```python
# train_model.py
import sys
sys.path.append('/Users/<USER>/go/src/awesomeProject10/Metis')

from time_series_detector.feature import feature_service
from time_series_detector.algorithm.xgboosting import XGBoosting
import pandas as pd
import numpy as np

def extract_features(data_a, data_b, data_c, window=180):
    """使用Metis的特征提取"""
    # 构造时间序列
    time_series = pd.Series(data_a + data_b + data_c)
    return feature_service.extract_features(time_series, window)

def train_model(training_data, task_id="libdetect_model"):
    """训练XGBoost模型"""
    xgb_detector = XGBoosting(
        threshold=0.15,
        max_depth=10,
        eta=0.05,
        gamma=0.1
    )
    ret_code, ret_msg = xgb_detector.xgb_train(training_data, task_id, num_round=300)
    return ret_code, ret_msg
```

### 第5步：模型加载功能 (4天)
**任务**:
1. 实现XGBoost模型文件加载
2. 添加模型文件验证
3. 实现错误处理机制
4. 使用训练好的模型测试

### 第6步：特征提取算法 (5天)
**任务**:
1. 将Metis的Python特征提取转换为C++
2. 实现统计特征、拟合特征、分类特征
3. 验证与Python特征提取一致性
4. 优化C++实现性能

### 第7步：量值检测实现 (5天)
**任务**:
1. 集成特征提取到量值检测
2. 实现XGBoost推理调用
3. 实现结果处理和概率计算
4. 使用训练好的模型测试

### 第8步：率值检测实现 (3天)
**任务**:
1. 实现3σ统计检测算法
2. 实现概率值计算
3. 添加边界条件处理
4. 编写率值检测测试

### 第9步：完整测试验证 (4天)
**任务**:
1. 运行所有单元测试
2. 执行集成测试
3. 进行性能基准测试
4. 验证结果一致性

### 第10步：多平台构建 (3天)
**任务**:
1. 配置Linux x86_64构建
2. 配置Linux ARM64构建
3. 配置macOS构建
4. 生成发布包

### 第11步：文档和示例 (2天)
**任务**:
1. 编写API使用文档
2. 创建C语言使用示例
3. 编写编译部署指南
4. 整理发布说明

## 项目结构
```
libdetect/
├── CMakeLists.txt
├── include/detect.h
├── src/
│   ├── detect.cpp
│   ├── model_loader.cpp
│   ├── feature_extractor.cpp
│   ├── value_detector.cpp
│   └── rate_detector.cpp
├── python_training/
│   ├── environment.yml          # conda环境配置
│   ├── train_model.py           # 训练脚本
│   ├── metis_adapter.py         # Metis代码适配
│   └── test_data/               # 测试数据
├── tests/
│   ├── test_main.cpp
│   └── test_data/
└── build/
```

## Conda环境配置
```yaml
# environment.yml
name: libdetect-train
channels:
  - defaults
  - conda-forge
dependencies:
  - python=3.7
  - numpy=1.15.2
  - scikit-learn=0.20.0
  - pandas
  - pip
  - pip:
    - xgboost==0.80
    - tsfresh==0.11.1
```

## 验证标准
1. **API兼容**: 与原始detect.h完全兼容
2. **功能正确**: 通过所有测试用例
3. **性能要求**: 推理速度不低于原始库
4. **平台支持**: Linux、macOS多架构

## 交付物
1. libdetect.so源代码
2. 多平台编译的动态库
3. conda训练环境配置
4. 测试套件和测试数据
5. 使用文档和示例

## 总工期
**38天** (约7.6周)

每个步骤完成后进行验证，确保质量后再进入下一步。
