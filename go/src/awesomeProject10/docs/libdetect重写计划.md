# libdetect.so 重写实现计划

## 项目背景

由于原始libdetect.so没有源码，且仅支持CentOS7.2+系统，为了支持多架构部署（x86_64、ARM64、macOS等），需要重新实现该库。

## 功能需求分析

### 基于detect.h的API接口

```c
// 核心数据结构
typedef struct {
    int* data_a;      // 181个数据点
    int* data_b;      // 361个数据点  
    int* data_c;      // 361个数据点
    int len_a;
    int len_b;
    int len_c;
} ValueData;

typedef struct {
    double* data_a;   // 181个数据点
    double* data_b;   // 361个数据点
    double* data_c;   // 361个数据点
    int len_a;
    int len_b;
    int len_c;
} RateData;

// 核心API函数
void* load_model(const char *fname);
int value_predict(void* mhandle, ValueData* data, int* sample_result, float* prob);
int rate_predict(RateData* data, int* sample_result, float* prob);
```

### 功能要求

1. **模型加载功能**
   - 支持加载XGBoost模型文件
   - 返回模型句柄供后续推理使用
   - 支持模型文件格式验证

2. **量值异常检测**
   - 输入：模型句柄 + ValueData结构体
   - 处理：使用XGBoost模型进行推理
   - 输出：异常判断(0/1) + 概率值

3. **率值异常检测**  
   - 输入：RateData结构体
   - 处理：使用无监督算法检测
   - 输出：异常判断(0/1) + 概率值

## 技术实现方案

### 方案选择：C++实现

**选择C++的原因**：
- 更好的内存管理和异常处理
- 丰富的机器学习库支持
- 面向对象设计，便于维护
- 兼容C接口，保持API一致性

### 依赖库选择

**XGBoost推理引擎**：
- 使用官方XGBoost C++ API
- 支持多平台编译
- 高性能推理能力

**数学计算库**：
- Eigen3：矩阵运算和数值计算
- 或使用标准库实现基础算法

**构建系统**：
- CMake：跨平台构建
- 支持多架构编译

## 详细实施计划

### 阶段1：项目搭建和基础框架 (1周)

**任务1.1：项目结构设计**
```
libdetect/
├── CMakeLists.txt          # 构建配置
├── include/
│   └── detect.h           # 公共头文件
├── src/
│   ├── detect.cpp         # 主要实现
│   ├── model_loader.cpp   # 模型加载
│   ├── value_detector.cpp # 量值检测
│   ├── rate_detector.cpp  # 率值检测
│   └── utils.cpp          # 工具函数
├── tests/
│   ├── test_main.cpp      # 测试主程序
│   └── test_data/         # 测试数据
├── examples/
│   └── example.c          # 使用示例
└── build/                 # 构建目录
```

**任务1.2：CMake配置**
- 配置跨平台编译
- 集成XGBoost依赖
- 设置编译选项和优化级别

**任务1.3：基础类设计**
```cpp
class ModelLoader {
public:
    void* loadModel(const char* filename);
    bool validateModel(void* handle);
    void releaseModel(void* handle);
};

class ValueDetector {
public:
    int predict(void* model, const ValueData* data, 
                int* result, float* prob);
private:
    int extractFeatures(const ValueData* data, std::vector<float>& features);
};

class RateDetector {
public:
    int predict(const RateData* data, int* result, float* prob);
private:
    int statisticalDetection(const RateData* data, float* prob);
};
```

### 阶段2：模型加载功能实现 (1周)

**任务2.1：XGBoost模型加载**
```cpp
void* load_model(const char *fname) {
    try {
        // 验证文件存在性
        if (!fileExists(fname)) {
            return nullptr;
        }
        
        // 创建XGBoost Booster
        BoosterHandle booster;
        XGBoosterCreate(nullptr, 0, &booster);
        
        // 加载模型文件
        XGBoosterLoadModel(booster, fname);
        
        // 验证模型有效性
        if (!validateModel(booster)) {
            XGBoosterFree(booster);
            return nullptr;
        }
        
        return static_cast<void*>(booster);
    } catch (...) {
        return nullptr;
    }
}
```

**任务2.2：模型验证机制**
- 检查模型文件格式
- 验证模型参数完整性
- 测试模型推理功能

**任务2.3：错误处理**
- 统一错误码定义
- 异常捕获和转换
- 内存泄漏防护

### 阶段3：量值检测实现 (1.5周)

**任务3.1：特征提取算法**
```cpp
int extractFeatures(const ValueData* data, std::vector<float>& features) {
    // 参考Python实现，提取统计特征
    // 1. 基础统计特征
    extractStatisticalFeatures(data->data_a, data->len_a, features);
    extractStatisticalFeatures(data->data_b, data->len_b, features);
    extractStatisticalFeatures(data->data_c, data->len_c, features);
    
    // 2. 拟合特征
    extractFittingFeatures(data, features);
    
    // 3. 分类特征
    extractClassificationFeatures(data, features);
    
    return TSD_SUCCESS;
}
```

**任务3.2：XGBoost推理**
```cpp
int value_predict(void* mhandle, ValueData* data, 
                  int* sample_result, float* prob) {
    if (!mhandle || !data || !sample_result || !prob) {
        return TSD_CHECK_PARAM_FAILED;
    }
    
    try {
        // 提取特征
        std::vector<float> features;
        int ret = extractFeatures(data, features);
        if (ret != TSD_SUCCESS) return ret;
        
        // 创建DMatrix
        DMatrixHandle dmat;
        XGDMatrixCreateFromMat(features.data(), 1, features.size(), 
                               NAN, &dmat);
        
        // 执行预测
        bst_ulong out_len;
        const float* out_result;
        XGBoosterPredict(static_cast<BoosterHandle>(mhandle), 
                         dmat, 0, 0, 0, &out_len, &out_result);
        
        // 处理结果
        *prob = out_result[0];
        *sample_result = (*prob < 0.15) ? 0 : 1;  // 阈值判断
        
        XGDMatrixFree(dmat);
        return TSD_SUCCESS;
    } catch (...) {
        return TSD_TIMESERIES_INIT_ERROR;
    }
}
```

**任务3.3：特征工程实现**
- 统计特征：均值、方差、偏度、峰度、分位数
- 拟合特征：线性回归、多项式拟合参数
- 分类特征：趋势、周期性、异常点数量

### 阶段4：率值检测实现 (1周)

**任务4.1：无监督检测算法**
```cpp
int rate_predict(RateData* data, int* sample_result, float* prob) {
    if (!data || !sample_result || !prob) {
        return TSD_CHECK_PARAM_FAILED;
    }
    
    try {
        // 使用3σ原则进行检测
        float mean, std_dev;
        calculateStatistics(data->data_a, data->len_a, &mean, &std_dev);
        
        // 获取最后一个点
        double last_point = data->data_a[data->len_a - 1];
        
        // 计算Z-score
        float z_score = std::abs((last_point - mean) / std_dev);
        
        // 计算概率值
        *prob = 1.0f - std::min(z_score / 3.0f, 1.0f);
        
        // 判断异常
        *sample_result = (z_score > 3.0f) ? 0 : 1;
        
        return TSD_SUCCESS;
    } catch (...) {
        return TSD_TIMESERIES_INIT_ERROR;
    }
}
```

**任务4.2：多种无监督算法**
- 3σ统计检测
- EWMA指数加权移动平均
- 基于分位数的检测
- 组合算法

### 阶段5：测试和验证 (1周)

**任务5.1：单元测试**
```cpp
// 测试模型加载
void test_load_model() {
    void* handle = load_model("test_model.xgb");
    assert(handle != nullptr);
    // 清理资源
}

// 测试量值检测
void test_value_predict() {
    // 准备测试数据
    ValueData test_data = {...};
    int result;
    float prob;
    
    int ret = value_predict(handle, &test_data, &result, &prob);
    assert(ret == TSD_SUCCESS);
    assert(result == 0 || result == 1);
    assert(prob >= 0.0f && prob <= 1.0f);
}
```

**任务5.2：集成测试**
- 使用原始测试数据验证结果一致性
- 性能基准测试
- 内存泄漏检测

**任务5.3：多平台测试**
- Linux x86_64
- Linux ARM64  
- macOS x86_64
- macOS ARM64 (Apple Silicon)

### 阶段6：构建和部署 (0.5周)

**任务6.1：构建脚本**
```bash
#!/bin/bash
# build.sh - 多平台构建脚本

mkdir -p build
cd build

# 配置构建
cmake .. -DCMAKE_BUILD_TYPE=Release \
         -DCMAKE_POSITION_INDEPENDENT_CODE=ON

# 编译
make -j$(nproc)

# 生成动态库
cp libdetect.so ../lib/
```

**任务6.2：打包发布**
- 生成不同平台的动态库
- 创建安装包
- 编写部署文档

## 验证方案

### 功能验证
1. **API兼容性**：确保与原始库API完全兼容
2. **结果一致性**：使用相同输入验证输出结果
3. **性能对比**：推理速度不低于原始库

### 质量保证
1. **内存安全**：使用Valgrind检测内存泄漏
2. **线程安全**：多线程环境下的稳定性测试
3. **异常处理**：各种边界条件的错误处理

### 平台兼容性
1. **多架构支持**：x86_64、ARM64
2. **多系统支持**：Linux、macOS、Windows
3. **编译器兼容**：GCC、Clang、MSVC

## 预期交付物

1. **源代码**：完整的C++实现代码
2. **构建系统**：CMake配置和构建脚本
3. **测试套件**：单元测试和集成测试
4. **文档**：API文档、编译文档、使用示例
5. **二进制库**：多平台的libdetect.so文件

## 时间安排

- **总工期**：5.5周
- **里程碑1**：基础框架完成 (1周)
- **里程碑2**：模型加载功能 (2周)  
- **里程碑3**：检测功能完成 (4.5周)
- **里程碑4**：测试验证完成 (5.5周)

## 测试数据集和训练环境

### 测试数据集准备

**数据集结构**：
```
test_data/
├── training_data/              # 训练数据
│   ├── normal_samples.csv      # 正常样本
│   ├── abnormal_samples.csv    # 异常样本
│   └── mixed_samples.csv       # 混合样本
├── test_cases/                 # 测试用例数据
│   ├── value_predict/          # 量值检测测试
│   │   ├── case_001_normal.json
│   │   ├── case_002_abnormal.json
│   │   └── case_003_boundary.json
│   ├── rate_predict/           # 率值检测测试
│   │   ├── case_001_normal.json
│   │   ├── case_002_abnormal.json
│   │   └── case_003_edge.json
│   └── model_loading/          # 模型加载测试
│       ├── valid_model.xgb
│       ├── invalid_model.txt
│       └── corrupted_model.xgb
├── benchmark_data/             # 性能基准测试
│   ├── large_dataset.csv       # 大数据集
│   └── stress_test.csv         # 压力测试数据
└── reference_results/          # 参考结果
    ├── python_results.json     # Python版本结果
    └── original_lib_results.json # 原始库结果
```

**测试数据格式**：
```json
{
  "test_case_id": "value_predict_001",
  "description": "正常KPI指标检测",
  "input": {
    "data_a": [9, 10, 152, 255, 458],
    "data_b": [9, 10, 152, 255, 18],
    "data_c": [9, 10, 152, 255, 16],
    "len_a": 181,
    "len_b": 361,
    "len_c": 361
  },
  "expected_output": {
    "result": 1,
    "probability": 0.85,
    "tolerance": 0.05
  }
}
```

### Python训练环境搭建

**训练脚本结构**：
```
python_training/
├── requirements.txt            # Python依赖
├── train_model.py             # 主训练脚本
├── data_processor.py          # 数据预处理
├── feature_extractor.py       # 特征提取
├── model_trainer.py           # 模型训练
├── model_validator.py         # 模型验证
├── export_model.py            # 模型导出
└── config/
    ├── training_config.yaml   # 训练配置
    └── feature_config.yaml    # 特征配置
```

**训练脚本实现**：
```python
# train_model.py
import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.model_selection import train_test_split
from feature_extractor import FeatureExtractor

class ModelTrainer:
    def __init__(self, config_path):
        self.config = self.load_config(config_path)
        self.feature_extractor = FeatureExtractor()

    def prepare_training_data(self, data_path):
        """准备训练数据"""
        df = pd.read_csv(data_path)

        features = []
        labels = []

        for _, row in df.iterrows():
            # 解析时间序列数据
            data_a = np.array(row['data_a'].split(','), dtype=float)
            data_b = np.array(row['data_b'].split(','), dtype=float)
            data_c = np.array(row['data_c'].split(','), dtype=float)

            # 提取特征
            feature_vector = self.feature_extractor.extract_features(
                data_a, data_b, data_c
            )

            features.append(feature_vector)
            labels.append(row['label'])  # 0:异常, 1:正常

        return np.array(features), np.array(labels)

    def train_xgboost_model(self, X, y):
        """训练XGBoost模型"""
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )

        # 创建DMatrix
        dtrain = xgb.DMatrix(X_train, label=y_train)
        dtest = xgb.DMatrix(X_test, label=y_test)

        # 训练参数
        params = {
            'objective': 'binary:logistic',
            'eval_metric': 'auc',
            'max_depth': 10,
            'eta': 0.05,
            'gamma': 0.1,
            'min_child_weight': 1,
            'subsample': 0.8,
            'colsample_bytree': 1,
            'silent': 1
        }

        # 训练模型
        model = xgb.train(
            params,
            dtrain,
            num_boost_round=300,
            evals=[(dtrain, 'train'), (dtest, 'test')],
            early_stopping_rounds=50,
            verbose_eval=False
        )

        return model

    def export_model(self, model, output_path):
        """导出模型为C++可用格式"""
        model.save_model(output_path)
        print(f"模型已保存到: {output_path}")

# feature_extractor.py
class FeatureExtractor:
    def extract_features(self, data_a, data_b, data_c):
        """提取特征向量"""
        features = []

        # 统计特征
        features.extend(self.extract_statistical_features(data_a))
        features.extend(self.extract_statistical_features(data_b))
        features.extend(self.extract_statistical_features(data_c))

        # 拟合特征
        features.extend(self.extract_fitting_features(data_a, data_b, data_c))

        # 分类特征
        features.extend(self.extract_classification_features(data_a, data_b, data_c))

        return features

    def extract_statistical_features(self, data):
        """提取统计特征"""
        return [
            np.mean(data),           # 均值
            np.std(data),            # 标准差
            np.var(data),            # 方差
            np.min(data),            # 最小值
            np.max(data),            # 最大值
            np.median(data),         # 中位数
            np.percentile(data, 25), # 25分位数
            np.percentile(data, 75), # 75分位数
            len(data)                # 数据长度
        ]

    def extract_fitting_features(self, data_a, data_b, data_c):
        """提取拟合特征"""
        features = []

        # 线性回归系数
        x = np.arange(len(data_a))
        coeffs = np.polyfit(x, data_a, 1)
        features.extend(coeffs)

        # 多项式拟合
        poly_coeffs = np.polyfit(x, data_a, 2)
        features.extend(poly_coeffs)

        return features

    def extract_classification_features(self, data_a, data_b, data_c):
        """提取分类特征"""
        features = []

        # 趋势特征
        trend = 1 if data_a[-1] > data_a[0] else 0
        features.append(trend)

        # 波动性特征
        volatility = np.std(np.diff(data_a))
        features.append(volatility)

        # 异常点数量
        mean_val = np.mean(data_a)
        std_val = np.std(data_a)
        outliers = np.sum(np.abs(data_a - mean_val) > 2 * std_val)
        features.append(outliers)

        return features
```

## 完整测试案例

### 单元测试扩展

**测试框架选择**：Google Test (gtest)

```cpp
// test_comprehensive.cpp
#include <gtest/gtest.h>
#include "detect.h"
#include "test_data_loader.h"

class LibDetectTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 加载测试模型
        model_handle = load_model("test_data/model_loading/valid_model.xgb");
        ASSERT_NE(model_handle, nullptr);

        // 加载测试数据
        test_data_loader.loadTestCases("test_data/test_cases/");
    }

    void TearDown() override {
        if (model_handle) {
            // 清理模型资源
            // 注意：需要实现模型释放函数
        }
    }

    void* model_handle;
    TestDataLoader test_data_loader;
};

// 模型加载测试
TEST_F(LibDetectTest, ModelLoadingTests) {
    // 测试有效模型加载
    void* valid_handle = load_model("test_data/model_loading/valid_model.xgb");
    EXPECT_NE(valid_handle, nullptr);

    // 测试无效模型文件
    void* invalid_handle = load_model("test_data/model_loading/invalid_model.txt");
    EXPECT_EQ(invalid_handle, nullptr);

    // 测试不存在的文件
    void* missing_handle = load_model("non_existent_model.xgb");
    EXPECT_EQ(missing_handle, nullptr);

    // 测试损坏的模型文件
    void* corrupted_handle = load_model("test_data/model_loading/corrupted_model.xgb");
    EXPECT_EQ(corrupted_handle, nullptr);
}

// 量值检测测试
TEST_F(LibDetectTest, ValuePredictTests) {
    auto test_cases = test_data_loader.getValuePredictCases();

    for (const auto& test_case : test_cases) {
        ValueData data;
        data.data_a = test_case.input.data_a.data();
        data.data_b = test_case.input.data_b.data();
        data.data_c = test_case.input.data_c.data();
        data.len_a = test_case.input.data_a.size();
        data.len_b = test_case.input.data_b.size();
        data.len_c = test_case.input.data_c.size();

        int result;
        float prob;
        int ret = value_predict(model_handle, &data, &result, &prob);

        EXPECT_EQ(ret, TSD_SUCCESS) << "Test case: " << test_case.id;
        EXPECT_EQ(result, test_case.expected.result) << "Test case: " << test_case.id;
        EXPECT_NEAR(prob, test_case.expected.probability, test_case.expected.tolerance)
            << "Test case: " << test_case.id;
    }
}

// 率值检测测试
TEST_F(LibDetectTest, RatePredictTests) {
    auto test_cases = test_data_loader.getRatePredictCases();

    for (const auto& test_case : test_cases) {
        RateData data;
        data.data_a = test_case.input.data_a.data();
        data.data_b = test_case.input.data_b.data();
        data.data_c = test_case.input.data_c.data();
        data.len_a = test_case.input.data_a.size();
        data.len_b = test_case.input.data_b.size();
        data.len_c = test_case.input.data_c.size();

        int result;
        float prob;
        int ret = rate_predict(&data, &result, &prob);

        EXPECT_EQ(ret, TSD_SUCCESS) << "Test case: " << test_case.id;
        EXPECT_EQ(result, test_case.expected.result) << "Test case: " << test_case.id;
        EXPECT_NEAR(prob, test_case.expected.probability, test_case.expected.tolerance)
            << "Test case: " << test_case.id;
    }
}

// 边界条件测试
TEST_F(LibDetectTest, BoundaryTests) {
    // 空指针测试
    EXPECT_EQ(value_predict(nullptr, nullptr, nullptr, nullptr), TSD_CHECK_PARAM_FAILED);
    EXPECT_EQ(rate_predict(nullptr, nullptr, nullptr), TSD_CHECK_PARAM_FAILED);

    // 空数据测试
    ValueData empty_value_data = {nullptr, nullptr, nullptr, 0, 0, 0};
    int result;
    float prob;
    EXPECT_NE(value_predict(model_handle, &empty_value_data, &result, &prob), TSD_SUCCESS);

    // 超大数据测试
    std::vector<int> large_data(10000, 100);
    ValueData large_value_data = {
        large_data.data(), large_data.data(), large_data.data(),
        static_cast<int>(large_data.size()),
        static_cast<int>(large_data.size()),
        static_cast<int>(large_data.size())
    };
    EXPECT_EQ(value_predict(model_handle, &large_value_data, &result, &prob), TSD_SUCCESS);
}

// 性能基准测试
TEST_F(LibDetectTest, PerformanceTests) {
    auto benchmark_data = test_data_loader.getBenchmarkData();

    auto start_time = std::chrono::high_resolution_clock::now();

    for (int i = 0; i < 1000; ++i) {
        int result;
        float prob;
        value_predict(model_handle, &benchmark_data.value_data, &result, &prob);
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);

    // 平均每次调用应该小于1ms
    EXPECT_LT(duration.count() / 1000.0, 1000.0) << "Performance requirement not met";
}

// 内存泄漏测试
TEST_F(LibDetectTest, MemoryLeakTests) {
    // 重复加载和释放模型
    for (int i = 0; i < 100; ++i) {
        void* handle = load_model("test_data/model_loading/valid_model.xgb");
        ASSERT_NE(handle, nullptr);
        // 注意：需要实现模型释放函数
        // release_model(handle);
    }

    // 使用Valgrind或AddressSanitizer检测内存泄漏
}

// 多线程安全测试
TEST_F(LibDetectTest, ThreadSafetyTests) {
    const int num_threads = 10;
    const int calls_per_thread = 100;

    std::vector<std::thread> threads;
    std::atomic<int> success_count(0);

    auto test_data = test_data_loader.getBenchmarkData();

    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([&]() {
            for (int j = 0; j < calls_per_thread; ++j) {
                int result;
                float prob;
                if (value_predict(model_handle, &test_data.value_data, &result, &prob) == TSD_SUCCESS) {
                    success_count++;
                }
            }
        });
    }

    for (auto& thread : threads) {
        thread.join();
    }

    EXPECT_EQ(success_count.load(), num_threads * calls_per_thread);
}
```

### 集成测试脚本

```bash
#!/bin/bash
# integration_test.sh

echo "开始libdetect.so集成测试..."

# 1. 编译测试
echo "1. 编译测试..."
mkdir -p build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Debug -DENABLE_TESTING=ON
make -j$(nproc)

if [ $? -ne 0 ]; then
    echo "编译失败!"
    exit 1
fi

# 2. 运行单元测试
echo "2. 运行单元测试..."
./tests/libdetect_tests --gtest_output=xml:test_results.xml

# 3. 内存泄漏检测
echo "3. 内存泄漏检测..."
valgrind --leak-check=full --show-leak-kinds=all ./tests/libdetect_tests

# 4. 性能基准测试
echo "4. 性能基准测试..."
./tests/benchmark_tests

# 5. Python训练和验证
echo "5. Python训练和验证..."
cd ../python_training
python train_model.py --config config/training_config.yaml
python validate_model.py --model ../build/test_model.xgb

# 6. 跨平台兼容性测试
echo "6. 跨平台兼容性测试..."
# 在不同平台上运行相同的测试用例

echo "集成测试完成!"
```

## 风险控制

1. **技术风险**：XGBoost C++ API学习成本
2. **兼容性风险**：不同平台的编译差异
3. **性能风险**：推理性能可能不如原始库
4. **数据风险**：测试数据集的完整性和准确性
5. **训练风险**：Python训练环境的一致性

**应对策略**：
- 提前进行技术预研和原型验证
- 建立持续集成环境，自动化多平台测试
- 性能优化和算法调优
- 建立完整的测试数据集和验证流程
- 标准化Python训练环境和流程

这个重写计划将确保新的libdetect.so库具有更好的跨平台兼容性，同时保持与原始API的完全兼容，并通过完整的测试体系保证质量。
