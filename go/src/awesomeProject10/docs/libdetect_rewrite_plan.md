# libdetect.so 重写计划

## 重写目标
原始libdetect.so无源码，仅支持CentOS7.2+，需要重写支持多架构（x86_64、ARM64、macOS等）

## 需要实现的API
```c
void* load_model(const char *fname);                    // 加载XGBoost模型
int value_predict(void* handle, ValueData* data, 
                  int* result, float* prob);            // 量值检测
int rate_predict(RateData* data, int* result, float* prob); // 率值检测
```

## 实施顺序

### 第1步：项目搭建 (3天)
**目标**: 建立基础开发环境
**任务**:
1. 创建项目目录结构
2. 配置CMake构建系统
3. 集成XGBoost依赖库
4. 编写基础头文件

**项目结构**:
```
libdetect/
├── CMakeLists.txt
├── include/detect.h
├── src/detect.cpp
├── tests/test_main.cpp
└── build/
```

### 第2步：模型加载功能 (4天)
**目标**: 实现load_model函数
**任务**:
1. 实现XGBoost模型文件加载
2. 添加模型文件验证
3. 实现错误处理机制
4. 编写模型加载测试

**核心代码**:
```cpp
void* load_model(const char *fname) {
    BoosterHandle booster;
    XGBoosterCreate(nullptr, 0, &booster);
    XGBoosterLoadModel(booster, fname);
    return static_cast<void*>(booster);
}
```

### 第3步：特征提取算法 (5天)
**目标**: 实现时间序列特征提取
**任务**:
1. 实现统计特征提取（均值、方差、分位数等）
2. 实现拟合特征提取（线性回归系数等）
3. 实现分类特征提取（趋势、异常点数量等）
4. 验证特征提取正确性

**特征类型**:
- 统计特征：均值、方差、偏度、峰度、分位数
- 拟合特征：线性回归系数、多项式拟合参数
- 分类特征：趋势方向、异常点数量

### 第4步：量值检测实现 (5天)
**目标**: 实现value_predict函数
**任务**:
1. 集成特征提取到量值检测
2. 实现XGBoost推理调用
3. 实现结果处理和概率计算
4. 编写量值检测测试

**核心流程**:
```
输入数据 → 特征提取 → XGBoost推理 → 概率计算 → 异常判断
```

### 第5步：率值检测实现 (3天)
**目标**: 实现rate_predict函数
**任务**:
1. 实现3σ统计检测算法
2. 实现概率值计算
3. 添加边界条件处理
4. 编写率值检测测试

**核心算法**:
```cpp
// 3σ检测
float mean = calculateMean(data->data_a, data->len_a);
float std_dev = calculateStdDev(data->data_a, data->len_a, mean);
float z_score = abs((last_point - mean) / std_dev);
*result = (z_score > 3.0) ? 0 : 1;  // 0:异常, 1:正常
```

### 第6步：测试数据准备 (3天)
**目标**: 准备完整测试数据
**任务**:
1. 收集现有测试样本
2. 生成边界测试用例
3. 准备性能测试数据
4. 创建参考结果

**测试数据**:
- 正常样本：50个
- 异常样本：30个
- 边界用例：20个
- 性能数据：1000个

### 第7步：Python训练环境 (4天)
**目标**: 建立模型训练能力
**任务**:
1. 编写特征提取Python脚本（与C++对齐）
2. 实现XGBoost模型训练
3. 生成测试用模型文件
4. 验证Python/C++特征一致性

**Python脚本**:
```python
# 特征提取（与C++保持一致）
def extract_features(data_a, data_b, data_c):
    features = []
    features.extend(statistical_features(data_a))
    features.extend(fitting_features(data_a, data_b, data_c))
    features.extend(classification_features(data_a))
    return features

# 模型训练
def train_model(X, y):
    model = xgb.train(params, dtrain, num_boost_round=300)
    model.save_model('test_model.xgb')
```

### 第8步：完整测试验证 (4天)
**目标**: 全面测试验证
**任务**:
1. 运行所有单元测试
2. 执行集成测试
3. 进行性能基准测试
4. 验证结果一致性

**测试内容**:
- 功能测试：API正确性
- 性能测试：推理速度
- 内存测试：无泄漏
- 兼容性测试：多平台

### 第9步：多平台构建 (3天)
**目标**: 支持多平台部署
**任务**:
1. 配置Linux x86_64构建
2. 配置Linux ARM64构建
3. 配置macOS构建
4. 生成发布包

**构建脚本**:
```bash
# Linux x86_64
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)

# Linux ARM64
cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_SYSTEM_PROCESSOR=aarch64
make -j$(nproc)

# macOS
cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_OSX_ARCHITECTURES="x86_64;arm64"
make -j$(sysctl -n hw.ncpu)
```

### 第10步：文档和示例 (2天)
**目标**: 完善文档和使用示例
**任务**:
1. 编写API使用文档
2. 创建C语言使用示例
3. 编写编译部署指南
4. 整理发布说明

## 验证标准
1. **API兼容**: 与原始detect.h完全兼容
2. **功能正确**: 通过所有测试用例
3. **性能要求**: 推理速度不低于原始库
4. **平台支持**: Linux、macOS多架构

## 交付物
1. libdetect.so源代码
2. 多平台编译的动态库
3. 测试套件和测试数据
4. 使用文档和示例
5. Python训练脚本

## 总工期
**36天** (约7.2周)

每个步骤完成后进行验证，确保质量后再进入下一步。
