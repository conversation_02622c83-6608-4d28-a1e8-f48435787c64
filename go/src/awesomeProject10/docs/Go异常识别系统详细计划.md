# Go异常识别系统详细计划

## 项目概述

基于Metis项目和异常监测项目计划，使用Go语言实现一个完整的时间序列异常识别系统。系统将提供从数据训练到异常预测的完整流程，使用Gin框架作为Web API入口，支持量值检测和率值检测两种模式。

## 系统架构设计

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web层 (Gin)   │    │   业务服务层     │    │   数据访问层     │
│                 │    │                 │    │                 │
│ - HTTP API      │───▶│ - 检测服务       │───▶│ - 样本管理       │
│ - 参数验证      │    │ - 训练服务       │    │ - 任务管理       │
│ - 响应格式化    │    │ - 样本管理       │    │ - 异常记录       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   算法引擎层     │    │   存储层         │
                       │                 │    │                 │
                       │ - 无监督算法     │    │ - MySQL数据库    │
                       │ - 有监督算法     │    │ - 模型文件       │
                       │ - C库集成       │    │ - 配置文件       │
                       └─────────────────┘    └─────────────────┘
```

### 模块划分
1. **Web API层**: 使用Gin框架提供HTTP接口
2. **业务服务层**: 核心业务逻辑处理
3. **算法引擎层**: 异常检测算法实现
4. **数据访问层**: 数据库操作和文件管理
5. **存储层**: 数据持久化

## 详细实施计划

### 阶段1: 系统基础搭建

#### 1.1 项目初始化和依赖管理
**功能描述**: 搭建Go项目基础框架，配置必要的依赖包
**预期效果**: 
- 输入: 无
- 输出: 完整的Go项目结构，包含所有必要依赖
- 功能: 提供项目运行的基础环境

**具体任务**:
- 初始化Go模块 (`go mod init`)
- 安装核心依赖包:
  - `github.com/gin-gonic/gin` (Web框架)
  - `gorm.io/gorm` (ORM框架)
  - `gorm.io/driver/mysql` (MySQL驱动)
  - `github.com/spf13/viper` (配置管理)
  - `github.com/sirupsen/logrus` (日志框架)
  - `github.com/stretchr/testify` (测试框架)
- 创建项目目录结构

#### 1.2 数据模型和数据库设计
**功能描述**: 设计数据库表结构和对应的Go结构体
**预期效果**:
- 输入: 业务需求
- 输出: 数据库表结构、Go结构体定义
- 功能: 支持样本数据、训练任务、异常记录的存储和管理

**数据表设计**:
```sql
-- 样本数据表
CREATE TABLE samples (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    view_id VARCHAR(50) NOT NULL,
    attr_id VARCHAR(50) NOT NULL,
    data_a TEXT NOT NULL,
    data_b TEXT NOT NULL,
    data_c TEXT NOT NULL,
    label INT NOT NULL, -- 0:异常, 1:正常
    source VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 训练任务表
CREATE TABLE train_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id VARCHAR(100) UNIQUE NOT NULL,
    model_type VARCHAR(50) NOT NULL, -- xgboost, gbdt
    status VARCHAR(20) NOT NULL, -- pending, running, completed, failed
    model_path VARCHAR(255),
    accuracy FLOAT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL
);

-- 异常记录表
CREATE TABLE anomalies (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    view_id VARCHAR(50) NOT NULL,
    attr_id VARCHAR(50) NOT NULL,
    detect_time TIMESTAMP NOT NULL,
    result INT NOT NULL, -- 0:异常, 1:正常
    probability FLOAT NOT NULL,
    algorithm VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending', -- pending, confirmed, ignored
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 阶段2: 核心算法实现

#### 2.1 核心检测算法实现
**功能描述**: 实现基础的无监督异常检测算法
**预期效果**:
- 输入: 时间序列数据 (dataA, dataB, dataC)
- 输出: 检测结果 (0:异常, 1:正常) 和概率值
- 功能: 提供多种无监督检测算法选择

**算法列表**:
1. **3σ统计算法**
   - 输入: `[]float64` 时间序列数据
   - 输出: `{result: int, probability: float64}`
   - 功能: 基于正态分布的3倍标准差异常检测

2. **EWMA指数加权移动平均**
   - 输入: `[]float64` 时间序列数据, `alpha float64` 平滑系数
   - 输出: `{result: int, probability: float64}`
   - 功能: 指数加权移动平均异常检测

3. **多项式插值算法**
   - 输入: `[]float64` 时间序列数据, `degree int` 多项式阶数
   - 输出: `{result: int, probability: float64}`
   - 功能: 基于多项式回归的异常检测

4. **孤立森林算法**
   - 输入: `[][]float64` 特征矩阵, `trees int` 树的数量
   - 输出: `{result: int, probability: float64}`
   - 功能: 无监督异常检测

#### 2.2 特征提取模块实现
**功能描述**: 从时间序列中提取特征向量
**预期效果**:
- 输入: 时间序列数据 `[]float64`
- 输出: 特征向量 `[]float64`
- 功能: 为机器学习模型提供输入特征

**特征类型**:
1. **统计特征**: 均值、方差、偏度、峰度、分位数等
2. **拟合特征**: 线性回归系数、多项式拟合参数等
3. **分类特征**: 趋势方向、周期性特征等

### 阶段3: 机器学习集成

#### 3.1 机器学习模型集成
**功能描述**: 集成XGBoost和GBDT等有监督学习算法
**预期效果**:
- 训练输入: 特征矩阵 `[][]float64`, 标签 `[]int`
- 训练输出: 模型文件路径 `string`, 训练状态 `TrainStatus`
- 预测输入: 特征向量 `[]float64`, 模型路径 `string`
- 预测输出: `{result: int, probability: float64}`
- 功能: 提供高精度的有监督异常检测

#### 3.2 C库接口封装
**功能描述**: 使用CGO封装现有的libdetect.so库
**预期效果**:
- 输入: ValueData或RateData结构体
- 输出: 检测结果和概率值
- 功能: 复用已有的C语言检测能力，提高检测精度

```go
// CGO接口定义
/*
#cgo LDFLAGS: -L./lib -ldetect
#include "lib/detect.h"
*/
import "C"

type ValueDetector struct {
    handle unsafe.Pointer
}

func (v *ValueDetector) Predict(data ValueData) (int, float64, error)
func (v *ValueDetector) LoadModel(modelPath string) error
```

### 阶段4: 业务服务层

#### 4.1 检测服务实现
**功能描述**: 实现量值检测和率值检测的业务逻辑
**预期效果**:
- 输入: 检测请求 `DetectRequest`
- 输出: 检测结果 `DetectResponse`
- 功能: 协调多种算法，提供统一的检测接口

```go
type DetectRequest struct {
    ViewID   string `json:"viewId"`
    AttrID   string `json:"attrId"`
    TaskID   string `json:"taskId,omitempty"`
    Window   int    `json:"window"`
    Time     string `json:"time"`
    DataA    string `json:"dataA"`
    DataB    string `json:"dataB"`
    DataC    string `json:"dataC"`
}

type DetectResponse struct {
    Code int `json:"code"`
    Msg  string `json:"msg"`
    Data struct {
        Result      int     `json:"ret"`
        Probability string  `json:"p"`
    } `json:"data"`
}
```

#### 4.2 训练服务实现
**功能描述**: 实现模型训练的业务逻辑
**预期效果**:
- 输入: 训练请求 `TrainRequest`
- 输出: 训练任务ID `string`
- 功能: 异步执行模型训练，支持任务状态查询

#### 4.3 样本管理服务
**功能描述**: 实现样本数据的增删改查
**预期效果**:
- 输入: 样本操作请求
- 输出: 操作结果
- 功能: 支持样本导入、查询、更新、删除、统计等操作

### 阶段5: Web API层

#### 5.1 Gin Web API实现
**功能描述**: 使用Gin框架实现HTTP API接口
**预期效果**:
- 输入: HTTP请求
- 输出: JSON响应
- 功能: 提供完整的RESTful API接口

**API接口列表**:
1. `POST /PredictValue` - 量值检测
2. `POST /PredictRate` - 率值检测  
3. `POST /Train` - 模型训练
4. `POST /ImportSample` - 样本导入
5. `POST /QuerySample` - 样本查询
6. `POST /UpdateSample` - 样本更新
7. `POST /DeleteSample` - 样本删除
8. `POST /SearchAnomaly` - 异常查询
9. `POST /UpdateAnomaly` - 异常更新
10. `GET /DownloadSample` - 样本下载

### 阶段6: 系统完善

#### 6.1 配置管理和日志系统
**功能描述**: 实现系统配置和日志记录
**预期效果**:
- 输入: 配置文件
- 输出: 系统运行日志
- 功能: 支持灵活配置和完整的日志记录

#### 6.2 测试和部署
**功能描述**: 编写测试用例和部署文档
**预期效果**:
- 输入: 测试数据
- 输出: 测试报告
- 功能: 确保系统质量和易于部署

## 预期最终效果

### 系统能力
1. **检测能力**: 支持量值和率值两种检测模式
2. **算法丰富**: 提供多种无监督和有监督算法
3. **易于使用**: 提供完整的Web API接口
4. **高性能**: 复用C库，保证检测性能
5. **可扩展**: 模块化设计，易于扩展新算法

### 使用示例
```bash
# 量值检测
curl -X POST http://localhost:8080/PredictValue \
  -H "Content-Type: application/json" \
  -d '{
    "viewId": "2012",
    "attrId": "19201", 
    "window": 180,
    "time": "2018-10-17 17:28:00",
    "dataA": "9,10,152,...,458",
    "dataB": "9,10,152,...,18", 
    "dataC": "9,10,152,...,16"
  }'

# 响应
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "ret": 0,
    "p": "0.05"
  }
}
```

## 技术栈
- **语言**: Go 1.24+
- **Web框架**: Gin
- **ORM**: GORM
- **数据库**: MySQL
- **配置管理**: Viper
- **日志**: Logrus
- **测试**: Testify
- **机器学习**: XGBoost, GBDT (通过CGO调用)
- **容器化**: Docker

## 项目结构
```
awesomeProject10/
├── cmd/                    # 应用入口
├── internal/              # 内部包
│   ├── api/              # API层
│   ├── service/          # 业务服务层
│   ├── repository/       # 数据访问层
│   ├── algorithm/        # 算法实现
│   └── model/           # 数据模型
├── pkg/                  # 公共包
├── configs/              # 配置文件
├── docs/                # 文档
├── tests/               # 测试
├── lib/                 # C库文件
└── deployments/         # 部署文件
```

## 验证方案

### 功能验证
每个阶段完成后需要进行相应的验证：

#### 阶段1验证
- **数据库连接测试**: 验证数据库表创建和基本CRUD操作
- **项目结构验证**: 确保所有依赖包正确安装和导入
- **配置加载测试**: 验证配置文件正确读取

#### 阶段2验证
- **算法精度测试**: 使用已知数据集验证各算法的检测精度
- **性能基准测试**: 测试算法在不同数据量下的执行时间
- **特征提取验证**: 验证提取的特征向量的正确性和有效性

#### 阶段3验证
- **模型训练验证**: 验证模型能够正确训练并保存
- **预测精度测试**: 测试训练后模型的预测准确率
- **C库集成测试**: 验证CGO调用的正确性和性能

#### 阶段4验证
- **业务逻辑测试**: 验证各服务的业务逻辑正确性
- **并发安全测试**: 测试服务在并发环境下的稳定性
- **错误处理验证**: 验证异常情况的正确处理

#### 阶段5验证
- **API接口测试**: 使用Postman或curl测试所有API接口
- **参数验证测试**: 测试各种边界条件和错误输入
- **响应格式验证**: 确保API响应格式符合规范

#### 阶段6验证
- **集成测试**: 端到端的完整流程测试
- **压力测试**: 测试系统在高并发下的表现
- **部署验证**: 验证Docker部署的正确性

### 性能指标
- **检测延迟**: 单次检测响应时间 < 100ms
- **并发处理**: 支持1000+并发请求
- **检测精度**: 准确率 > 95%，召回率 > 90%
- **系统可用性**: 99.9%以上

### 数据流示例

#### 训练流程
```
1. 样本数据导入 → 2. 特征提取 → 3. 模型训练 → 4. 模型保存 → 5. 性能评估
```

#### 检测流程
```
1. 接收检测请求 → 2. 数据预处理 → 3. 特征提取 → 4. 模型预测 → 5. 结果返回
```

## 风险评估和应对策略

### 技术风险
1. **CGO集成复杂性**:
   - 风险: C库调用可能出现内存泄漏或崩溃
   - 应对: 充分测试，添加错误恢复机制

2. **机器学习模型性能**:
   - 风险: 模型训练时间过长或精度不足
   - 应对: 使用增量训练，优化特征工程

3. **并发安全**:
   - 风险: 高并发下的数据竞争
   - 应对: 使用适当的锁机制，无状态设计

### 业务风险
1. **数据质量**:
   - 风险: 训练数据质量影响模型效果
   - 应对: 数据清洗和验证机制

2. **算法选择**:
   - 风险: 不同场景需要不同算法
   - 应对: 提供多种算法选择，支持算法组合

## 扩展规划

### 短期扩展 (3个月内)
- 支持更多时间序列算法 (LSTM, Prophet等)
- 添加实时流数据处理能力
- 实现模型自动调优功能

### 中期扩展 (6个月内)
- 支持多维时间序列检测
- 添加可视化界面
- 实现分布式训练和预测

### 长期扩展 (1年内)
- 支持深度学习模型
- 实现AutoML自动机器学习
- 添加边缘计算支持

## 总结

此计划提供了完整的Go语言异常识别系统实施路径，包含：

1. **完整的技术栈**: 从Web API到算法引擎的全栈解决方案
2. **详细的实施步骤**: 分阶段实施，每阶段都有明确的输入输出和验证方案
3. **丰富的算法支持**: 无监督和有监督算法相结合
4. **高性能设计**: 复用C库，保证检测性能
5. **易于扩展**: 模块化设计，支持后续功能扩展

系统建成后将能够满足各种时间序列异常检测需求，为业务提供可靠的异常监测能力。
