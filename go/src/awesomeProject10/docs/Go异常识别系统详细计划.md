# Go异常识别系统详细计划

## 项目概述

基于Metis项目和异常监测项目计划，使用Go语言实现一个完整的时间序列异常识别系统。系统将提供从数据训练到异常预测的完整流程，使用Gin框架作为Web API入口，支持量值检测和率值检测两种模式。

## 预期效果

系统将实现完整的异常监测流程，严格按照异常监测项目计划.md中定义的功能：

### 第一步：样本数据管理

**样本导入功能** (`import_sample`)
- 输入：样本数据文件
- 处理：解析样本数据并存储到数据库
- 输出：导入结果状态
- HTTP接口：`POST /ImportSample` (文件上传)

**样本查询功能** (`query_sample`)
- 输入：查询条件
- 处理：根据条件检索样本数据
- 输出：样本列表
- HTTP接口：`POST /QuerySample`

**样本更新功能** (`update_sample`)
- 输入：样本ID和新标签
- 处理：更新样本的标签信息
- 输出：更新结果
- HTTP接口：`POST /UpdateSample`

**样本删除功能** (`delete_sample`)
- 输入：样本ID
- 处理：从数据库删除指定样本
- 输出：删除结果
- HTTP接口：`POST /DeleteSample`

**样本统计功能** (`count_sample`)
- 输入：查询条件
- 处理：统计符合条件的样本数量
- 输出：样本统计信息
- HTTP接口：`POST /CountSample`

**样本来源查询** (`query_sample_source`)
- 输入：无
- 处理：查询所有可用的样本来源
- 输出：样本来源列表
- HTTP接口：`POST /QueryTrainSource`

**样本下载功能** (`sample_download`)
- 输入：样本ID列表
- 处理：导出指定样本为CSV文件
- 输出：CSV文件
- HTTP接口：`GET /DownloadSample`

### 第二步：特征提取

**统计特征提取** (`get_statistical_features`)
- 输入：时间序列数据
- 处理：计算均值、方差、偏度、峰度等统计特征
- 输出：统计特征列表

**拟合特征提取** (`get_fitting_features`)
- 输入：时间序列数据列表
- 处理：进行曲线拟合，提取拟合参数
- 输出：拟合特征列表

**分类特征提取** (`get_classification_features`)
- 输入：时间序列数据
- 处理：提取用于分类的特征
- 输出：分类特征列表

**综合特征提取** (`extract_features`)
- 输入：时间序列数据、窗口大小
- 处理：综合提取各类特征
- 输出：特征向量列表

### 第三步：模型训练 (Go实现)

**XGBoost训练** (`xgb_train`) - Go实现
- 输入：训练数据集、任务ID、迭代轮数
- 处理：使用Go的XGBoost库训练模型，保存为.model文件
- 输出：训练状态码、错误信息
- 注意：需要Go语言的XGBoost绑定库

**GBDT训练** (`gbdt_train`) - Go实现
- 输入：训练数据集、任务ID、窗口大小
- 处理：使用Go的GBDT库训练模型，保存为.model文件
- 输出：训练状态码、错误信息
- 注意：需要Go语言的GBDT实现

**训练服务处理** (`process_train`)
- 输入：训练参数(时间范围、数据源等)
- 处理：处理模型训练请求，调用Go实现的训练算法
- 输出：训练任务状态
- HTTP接口：`POST /Train`

**训练任务查询** (`query_train`)
- 输入：查询条件
- 处理：查询训练任务状态和结果
- 输出：训练任务列表
- HTTP接口：`POST /QueryTrain`

**训练任务删除** (`delete_train`)
- 输入：任务ID
- 处理：删除指定的训练任务
- 输出：删除结果
- HTTP接口：`POST /DeleteTrain`

### 第四步：异常检测

**量值异常检测** (`value_predict`)
- 输入：时间序列数据(dataA/B/C)、窗口大小、任务ID
- 处理：优先使用C库libdetect.so进行检测，备选Go实现
- 输出：检测结果(0异常/1正常)、概率值
- HTTP接口：`POST /PredictValue`

**率值异常检测** (`rate_predict`)
- 输入：时间序列数据(dataA/B/C)
- 处理：使用C库libdetect.so或Go实现的无监督算法
- 输出：检测结果(0异常/1正常)、概率值
- HTTP接口：`POST /PredictRate`

**无监督算法检测** (Go实现)：
- **3σ统计检测** (`statistic.predict`): 基于3σ原则的统计检测
- **EWMA检测** (`ewma.predict`): 指数加权移动平均检测
- **多项式检测** (`polynomial_interpolation.predict`): 多项式回归检测
- **孤立森林检测** (`isolation_forest.predict`): 孤立森林无监督检测
- **组合算法检测** (`ewma_and_polynomial.predict`): EWMA和多项式组合检测

**有监督算法检测**：
- **C库预测** (libdetect.so): 使用预训练模型进行推理，仅支持推理不支持训练
- **XGBoost预测** (Go实现): 使用Go训练的XGBoost模型预测
- **GBDT预测** (Go实现): 使用Go训练的GBDT模型预测

### 第五步：异常管理

**异常记录查询** (`query_anomaly`)
- 输入：查询条件
- 处理：根据条件查询异常记录
- 输出：异常记录列表
- HTTP接口：`POST /SearchAnomaly`

**异常状态更新** (`update_anomaly`)
- 输入：异常ID和状态
- 处理：更新异常记录的状态
- 输出：更新结果
- HTTP接口：`POST /UpdateAnomaly`

**异常记录入库** (`insert_anomaly`)
- 输入：异常数据
- 处理：将检测到的异常记录存储到数据库
- 输出：插入结果

### 第六步：C库集成 (仅推理，不支持训练)

**模型加载** (`load_model`)
- 输入：模型文件路径 (预训练的XGBoost模型文件)
- 处理：加载已训练好的模型到内存
- 输出：模型句柄
- 限制：只能加载现有模型，不支持训练新模型

**C接口量值检测** (`value_predict`)
- 输入：ValueData结构体 (包含dataA/B/C的int数组)
- 处理：通过libdetect.so库进行量值检测推理
- 输出：检测结果(0异常/1正常)、概率值
- 优势：高性能推理，适合生产环境

**C接口率值检测** (`rate_predict`)
- 输入：RateData结构体 (包含dataA/B/C的double数组)
- 处理：通过libdetect.so库进行率值检测推理
- 输出：检测结果(0异常/1正常)、概率值
- 特点：无需模型句柄，直接进行无监督检测

**C库限制说明**：
- libdetect.so只提供模型推理功能
- 不支持模型训练，训练需要用Go实现
- 支持加载预训练的XGBoost模型进行推理
- 提供高性能的检测能力，适合实时场景

### 完整流程示例

**1. 样本导入** (`POST /ImportSample`)
```bash
curl -X POST http://localhost:8080/ImportSample \
  -F "file=@samples.csv"
# 返回：{"code": 0, "msg": "导入成功", "data": {"count": 1000}}
```

**2. 样本查询** (`POST /QuerySample`)
```bash
curl -X POST http://localhost:8080/QuerySample \
  -H "Content-Type: application/json" \
  -d '{
    "viewId": "2012",
    "attrId": "19201"
  }'
# 返回：样本列表数据
```

**3. 样本统计** (`POST /CountSample`)
```bash
curl -X POST http://localhost:8080/CountSample \
  -H "Content-Type: application/json" \
  -d '{
    "viewId": "2012"
  }'
# 返回：{"code": 0, "data": {"total": 1000, "normal": 800, "abnormal": 200}}
```

**4. 模型训练** (`POST /Train`)
```bash
curl -X POST http://localhost:8080/Train \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": "train_20241205_001",
    "modelType": "xgboost",
    "dataSource": "samples"
  }'
# 返回：{"code": 0, "msg": "训练任务已创建", "data": {"taskId": "train_20241205_001"}}
```

**5. 训练任务查询** (`POST /QueryTrain`)
```bash
curl -X POST http://localhost:8080/QueryTrain \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": "train_20241205_001"
  }'
# 返回：{"code": 0, "data": {"status": "completed", "accuracy": 0.95}}
```

**6. 量值异常检测** (`POST /PredictValue`)
```bash
curl -X POST http://localhost:8080/PredictValue \
  -H "Content-Type: application/json" \
  -d '{
    "viewId": "2012",
    "viewName": "登陆功能",
    "attrId": "19201",
    "attrName": "ptlogin登陆请求总量",
    "taskId": "train_20241205_001",
    "window": 180,
    "time": "2018-10-17 17:28:00",
    "dataC": "9,10,152,...,255,...,16",
    "dataB": "9,10,152,...,255,...,18",
    "dataA": "9,10,152,...,458"
  }'
# 返回：{"code": 0, "msg": "操作成功", "data": {"ret": 0, "p": "0.05"}}
```

**7. 率值异常检测** (`POST /PredictRate`)
```bash
curl -X POST http://localhost:8080/PredictRate \
  -H "Content-Type: application/json" \
  -d '{
    "viewId": "2012",
    "viewName": "登陆功能",
    "attrId": "19201",
    "attrName": "ptlogin登陆成功率",
    "window": 180,
    "time": "2018-10-17 17:28:00",
    "dataC": "100,99.8,100,...,100,...,100",
    "dataB": "99.5,100,100,...,99.6,...,100",
    "dataA": "100,98.5,100,...,85.9"
  }'
# 返回：{"code": 0, "msg": "操作成功", "data": {"ret": 0, "p": "0"}}
```

**8. 异常查询** (`POST /SearchAnomaly`)
```bash
curl -X POST http://localhost:8080/SearchAnomaly \
  -H "Content-Type: application/json" \
  -d '{
    "viewId": "2012",
    "startTime": "2018-10-17 00:00:00",
    "endTime": "2018-10-17 23:59:59"
  }'
# 返回：异常记录列表
```

**9. 异常状态更新** (`POST /UpdateAnomaly`)
```bash
curl -X POST http://localhost:8080/UpdateAnomaly \
  -H "Content-Type: application/json" \
  -d '{
    "anomalyId": "123",
    "status": "confirmed"
  }'
# 返回：{"code": 0, "msg": "更新成功"}
```

**10. 样本下载** (`GET /DownloadSample`)
```bash
curl -X GET "http://localhost:8080/DownloadSample?sampleIds=1,2,3"
# 返回：CSV文件下载
```

**11. 训练任务删除** (`POST /DeleteTrain`)
```bash
curl -X POST http://localhost:8080/DeleteTrain \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": "train_20241205_001"
  }'
# 返回：{"code": 0, "msg": "删除成功"}
```

### 系统整体效果
- **完整功能覆盖**：实现异常监测项目计划.md中定义的所有56个功能点
- **多算法支持**：7种检测算法(3σ、EWMA、多项式、孤立森林、组合、XGBoost、GBDT)
- **完整API接口**：13个HTTP API接口，覆盖所有业务场景
- **混合架构**：C库推理 + Go实现训练，兼顾性能和灵活性
- **数据管理**：完整的样本和异常数据生命周期管理

## 技术实现说明

### C库 vs Go实现的功能划分

**libdetect.so C库功能** (仅推理)：
- ✅ 模型加载：`load_model()` - 加载预训练的XGBoost模型
- ✅ 量值检测：`value_predict()` - 高性能推理
- ✅ 率值检测：`rate_predict()` - 无监督检测
- ❌ 不支持：模型训练、参数调优、新算法扩展

**Go语言实现功能** (训练+推理)：
- ✅ 所有无监督算法：3σ、EWMA、多项式、孤立森林、组合算法
- ✅ 模型训练：XGBoost训练、GBDT训练 (需要Go机器学习库)
- ✅ 特征提取：统计、拟合、分类特征
- ✅ 数据管理：样本管理、异常管理、训练任务管理
- ✅ Web API：所有HTTP接口
- ✅ 系统服务：配置、日志、数据库操作

### 推荐的实现策略

**阶段1：基础功能** (优先级高)
- 使用Go实现所有无监督算法
- 实现完整的数据管理功能
- 实现Web API接口
- 通过CGO集成C库进行高性能推理

**阶段2：训练功能** (优先级中)
- 集成Go语言的机器学习库
- 实现XGBoost和GBDT的训练功能
- 实现训练任务管理

**阶段3：性能优化** (优先级低)
- 优化Go算法性能
- 实现算法结果缓存
- 添加并发处理能力

### 技术风险和应对

**风险1：Go机器学习库成熟度**
- 风险：Go的XGBoost/GBDT库可能不如Python成熟
- 应对：优先使用C库推理，训练功能可以分阶段实现

**风险2：CGO集成复杂性**
- 风险：C库调用可能出现内存管理问题
- 应对：充分测试，添加错误恢复机制

**风险3：性能差异**
- 风险：Go实现的算法性能可能不如C库
- 应对：关键路径使用C库，非关键路径使用Go实现

## 系统架构设计

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web层 (Gin)   │    │   业务服务层     │    │   数据访问层     │
│                 │    │                 │    │                 │
│ - HTTP API      │───▶│ - 检测服务       │───▶│ - 样本管理       │
│ - 参数验证      │    │ - 训练服务       │    │ - 任务管理       │
│ - 响应格式化    │    │ - 样本管理       │    │ - 异常记录       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   算法引擎层     │    │   存储层         │
                       │                 │    │                 │
                       │ - 无监督算法     │    │ - MySQL数据库    │
                       │ - 有监督算法     │    │ - 模型文件       │
                       │ - C库集成       │    │ - 配置文件       │
                       └─────────────────┘    └─────────────────┘
```

### 模块划分
1. **Web API层**: 使用Gin框架提供HTTP接口
2. **业务服务层**: 核心业务逻辑处理
3. **算法引擎层**: 异常检测算法实现
4. **数据访问层**: 数据库操作和文件管理
5. **存储层**: 数据持久化

## 技术栈
- **语言**: Go 1.24+
- **Web框架**: Gin
- **ORM**: GORM
- **数据库**: MySQL
- **配置管理**: Viper
- **日志**: Logrus
- **测试**: Testify
- **机器学习**: XGBoost, GBDT (通过CGO调用)
- **容器化**: Docker

## 项目结构
```
awesomeProject10/
├── cmd/                    # 应用入口
├── internal/              # 内部包
│   ├── api/              # API层
│   ├── service/          # 业务服务层
│   ├── repository/       # 数据访问层
│   ├── algorithm/        # 算法实现
│   └── model/           # 数据模型
├── pkg/                  # 公共包
├── configs/              # 配置文件
├── docs/                # 文档
├── tests/               # 测试
├── lib/                 # C库文件
└── deployments/         # 部署文件
```
