# Go异常识别系统详细计划

## 项目概述

基于Metis项目和异常监测项目计划，使用Go语言实现一个完整的时间序列异常识别系统。系统将提供从数据训练到异常预测的完整流程，使用Gin框架作为Web API入口，支持量值检测和率值检测两种模式。

## 预期效果

系统将实现完整的异常监测流程，从数据输入到异常检测的全链路功能：

### 第一步：数据输入和样本管理

**样本数据导入**
- 输入：CSV文件或JSON格式的时间序列数据
- 处理：自动解析数据格式，提取dataA、dataB、dataC三段时间序列
- 输出：样本数据存储到数据库，返回导入成功的样本数量
- 效果：支持批量导入历史数据，为模型训练提供数据基础

**样本数据管理**
- 查询样本：根据指标ID、时间范围等条件查询样本数据
- 更新标签：手动标注样本为正常(1)或异常(0)
- 删除样本：清理无效或错误的样本数据
- 样本统计：统计不同指标的样本数量、异常比例等
- 样本下载：导出样本数据为CSV格式

### 第二步：模型训练

**训练任务创建**
- 输入：训练参数（模型类型、数据源、训练比例等）
- 处理：创建训练任务，分配任务ID，设置任务状态为"pending"
- 输出：返回训练任务ID，用户可通过此ID查询训练进度

**特征提取和模型训练**
- 自动从样本数据中提取统计特征、拟合特征、分类特征
- 支持XGBoost和GBDT两种有监督学习算法
- 训练过程异步执行，实时更新任务状态（running → completed/failed）
- 训练完成后保存模型文件，记录模型精度指标

**训练任务管理**
- 查询训练任务：查看所有训练任务的状态、进度、结果
- 删除训练任务：清理失败或过期的训练任务
- 训练来源查询：查看可用的训练数据源

### 第三步：异常检测

**实时异常检测**
- 量值检测：适用于KPI指标，输入181+361+361个数据点，输出异常判断和概率值
- 率值检测：适用于成功率等指标，使用无监督算法进行检测
- 多算法支持：3σ统计、EWMA、多项式插值、孤立森林、XGBoost、GBDT
- 检测结果：返回0(异常)或1(正常)，以及异常概率值

**检测结果处理**
- 自动记录每次检测结果到异常记录表
- 包含检测时间、指标信息、检测结果、使用算法等详细信息
- 支持检测结果的后续查询和分析

### 第四步：异常管理

**异常记录查询**
- 根据时间范围、指标ID、异常状态等条件查询异常记录
- 支持分页查询，返回异常发生时间、概率值、检测算法等信息
- 提供异常趋势统计，如异常数量、异常率等指标

**异常状态管理**
- 异常确认：将检测到的异常标记为"已确认"状态
- 异常忽略：将误报的异常标记为"已忽略"状态
- 状态统计：统计不同状态的异常数量和分布

**异常分析**
- 异常模式识别：分析异常发生的时间模式、频率特征
- 异常关联分析：识别不同指标间的异常关联关系
- 异常报告生成：生成异常监测报告，包含异常统计和趋势分析

### 完整流程示例

**1. 样本导入**
```bash
curl -X POST http://localhost:8080/ImportSample \
  -F "file=@samples.csv" \
  -F "source=manual_import"
# 返回：导入成功1000条样本数据
```

**2. 模型训练**
```bash
curl -X POST http://localhost:8080/Train \
  -d '{
    "taskId": "train_20241205_001",
    "modelType": "xgboost",
    "dataSource": "samples",
    "trainRatio": 0.8
  }'
# 返回：训练任务已创建，任务ID: train_20241205_001
```

**3. 异常检测**
```bash
curl -X POST http://localhost:8080/PredictValue \
  -d '{
    "viewId": "2012",
    "attrId": "19201",
    "taskId": "train_20241205_001",
    "window": 180,
    "time": "2018-10-17 17:28:00",
    "dataA": "9,10,152,...,458",
    "dataB": "9,10,152,...,18",
    "dataC": "9,10,152,...,16"
  }'
# 返回：检测结果异常，概率值0.05
```

**4. 异常查询**
```bash
curl -X POST http://localhost:8080/SearchAnomaly \
  -d '{
    "viewId": "2012",
    "startTime": "2018-10-17 00:00:00",
    "endTime": "2018-10-17 23:59:59"
  }'
# 返回：该指标当天检测到5个异常点
```

**5. 异常处理**
```bash
curl -X POST http://localhost:8080/UpdateAnomaly \
  -d '{
    "anomalyId": "123",
    "status": "confirmed"
  }'
# 返回：异常状态已更新为已确认
```

### 系统整体效果
- **完整闭环**：从数据输入、模型训练、异常检测到异常管理的完整流程
- **多算法支持**：无监督和有监督算法相结合，提高检测精度
- **实时监测**：支持实时异常检测和状态管理
- **可视化管理**：通过Web API实现异常数据的可视化管理
- **历史追溯**：完整记录异常检测历史，支持趋势分析

## 系统架构设计

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web层 (Gin)   │    │   业务服务层     │    │   数据访问层     │
│                 │    │                 │    │                 │
│ - HTTP API      │───▶│ - 检测服务       │───▶│ - 样本管理       │
│ - 参数验证      │    │ - 训练服务       │    │ - 任务管理       │
│ - 响应格式化    │    │ - 样本管理       │    │ - 异常记录       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   算法引擎层     │    │   存储层         │
                       │                 │    │                 │
                       │ - 无监督算法     │    │ - MySQL数据库    │
                       │ - 有监督算法     │    │ - 模型文件       │
                       │ - C库集成       │    │ - 配置文件       │
                       └─────────────────┘    └─────────────────┘
```

### 模块划分
1. **Web API层**: 使用Gin框架提供HTTP接口
2. **业务服务层**: 核心业务逻辑处理
3. **算法引擎层**: 异常检测算法实现
4. **数据访问层**: 数据库操作和文件管理
5. **存储层**: 数据持久化

## 技术栈
- **语言**: Go 1.24+
- **Web框架**: Gin
- **ORM**: GORM
- **数据库**: MySQL
- **配置管理**: Viper
- **日志**: Logrus
- **测试**: Testify
- **机器学习**: XGBoost, GBDT (通过CGO调用)
- **容器化**: Docker

## 项目结构
```
awesomeProject10/
├── cmd/                    # 应用入口
├── internal/              # 内部包
│   ├── api/              # API层
│   ├── service/          # 业务服务层
│   ├── repository/       # 数据访问层
│   ├── algorithm/        # 算法实现
│   └── model/           # 数据模型
├── pkg/                  # 公共包
├── configs/              # 配置文件
├── docs/                # 文档
├── tests/               # 测试
├── lib/                 # C库文件
└── deployments/         # 部署文件
```
