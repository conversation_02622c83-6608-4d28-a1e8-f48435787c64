/*
  Tencent is pleased to support the open source community by making Metis available.
  Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
  Licensed under the BSD 3-Clause License (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at
  https://opensource.org/licenses/BSD-3-Clause
  Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
*/

!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("react"),require("lodash"),require("../uw2"));else if("function"==typeof define&&define.amd)define(["react","lodash","../uw2"],t);else{var n="object"==typeof exports?t(require("react"),require("lodash"),require("../uw2")):t(e.react,e.lodash,e["../uw2"]);for(var r in n)("object"==typeof exports?exports:e)[r]=n[r]}}(window,function(e,t,n){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:r})},n.r=function(e){Object.defineProperty(e,"__esModule",{value:!0})},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=7)}([function(t,n){t.exports=e},function(e,n){e.exports=t},function(e,t,n){var r,i,o={},l=(r=function(){return window&&document&&document.all&&!window.atob},function(){return void 0===i&&(i=r.apply(this,arguments)),i}),a=function(e){var t={};return function(e){if("function"==typeof e)return e();if(void 0===t[e]){var n=function(e){return document.querySelector(e)}.call(this,e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}}(),s=null,c=0,u=[],h=n(49);function f(e,t){for(var n=0;n<e.length;n++){var r=e[n],i=o[r.id];if(i){i.refs++;for(var l=0;l<i.parts.length;l++)i.parts[l](r.parts[l]);for(;l<r.parts.length;l++)i.parts.push(b(r.parts[l],t))}else{var a=[];for(l=0;l<r.parts.length;l++)a.push(b(r.parts[l],t));o[r.id]={id:r.id,refs:1,parts:a}}}}function d(e,t){for(var n=[],r={},i=0;i<e.length;i++){var o=e[i],l=t.base?o[0]+t.base:o[0],a={css:o[1],media:o[2],sourceMap:o[3]};r[l]?r[l].parts.push(a):n.push(r[l]={id:l,parts:[a]})}return n}function p(e,t){var n=a(e.insertInto);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var r=u[u.length-1];if("top"===e.insertAt)r?r.nextSibling?n.insertBefore(t,r.nextSibling):n.appendChild(t):n.insertBefore(t,n.firstChild),u.push(t);else if("bottom"===e.insertAt)n.appendChild(t);else{if("object"!=typeof e.insertAt||!e.insertAt.before)throw new Error("[Style Loader]\n\n Invalid value for parameter 'insertAt' ('options.insertAt') found.\n Must be 'top', 'bottom', or Object.\n (https://github.com/webpack-contrib/style-loader#insertat)\n");var i=a(e.insertInto+" "+e.insertAt.before);n.insertBefore(t,i)}}function g(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e);var t=u.indexOf(e);t>=0&&u.splice(t,1)}function m(e){var t=document.createElement("style");return e.attrs.type="text/css",v(t,e.attrs),p(e,t),t}function v(e,t){Object.keys(t).forEach(function(n){e.setAttribute(n,t[n])})}function b(e,t){var n,r,i,o;if(t.transform&&e.css){if(!(o=t.transform(e.css)))return function(){};e.css=o}if(t.singleton){var l=c++;n=s||(s=m(t)),r=w.bind(null,n,l,!1),i=w.bind(null,n,l,!0)}else e.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(n=function(e){var t=document.createElement("link");return e.attrs.type="text/css",e.attrs.rel="stylesheet",v(t,e.attrs),p(e,t),t}(t),r=function(e,t,n){var r=n.css,i=n.sourceMap,o=void 0===t.convertToAbsoluteUrls&&i;(t.convertToAbsoluteUrls||o)&&(r=h(r));i&&(r+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(i))))+" */");var l=new Blob([r],{type:"text/css"}),a=e.href;e.href=URL.createObjectURL(l),a&&URL.revokeObjectURL(a)}.bind(null,n,t),i=function(){g(n),n.href&&URL.revokeObjectURL(n.href)}):(n=m(t),r=function(e,t){var n=t.css,r=t.media;r&&e.setAttribute("media",r);if(e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}.bind(null,n),i=function(){g(n)});return r(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;r(e=t)}else i()}}e.exports=function(e,t){if("undefined"!=typeof DEBUG&&DEBUG&&"object"!=typeof document)throw new Error("The style-loader cannot be used in a non-browser environment");(t=t||{}).attrs="object"==typeof t.attrs?t.attrs:{},t.singleton||"boolean"==typeof t.singleton||(t.singleton=l()),t.insertInto||(t.insertInto="head"),t.insertAt||(t.insertAt="bottom");var n=d(e,t);return f(n,t),function(e){for(var r=[],i=0;i<n.length;i++){var l=n[i];(a=o[l.id]).refs--,r.push(a)}e&&f(d(e,t),t);for(i=0;i<r.length;i++){var a;if(0===(a=r[i]).refs){for(var s=0;s<a.parts.length;s++)a.parts[s]();delete o[a.id]}}}};var y,x=(y=[],function(e,t){return y[e]=t,y.filter(Boolean).join("\n")});function w(e,t,n,r){var i=n?"":r.css;if(e.styleSheet)e.styleSheet.cssText=x(t,i);else{var o=document.createTextNode(i),l=e.childNodes;l[t]&&e.removeChild(l[t]),l.length?e.insertBefore(o,l[t]):e.appendChild(o)}}},function(e,t){e.exports=function(e){var t=[];return t.toString=function(){return this.map(function(t){var n=function(e,t){var n=e[1]||"",r=e[3];if(!r)return n;if(t&&"function"==typeof btoa){var i=(l=r,"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(l))))+" */"),o=r.sources.map(function(e){return"/*# sourceURL="+r.sourceRoot+e+" */"});return[n].concat(o).concat([i]).join("\n")}var l;return[n].join("\n")}(t,e);return t[2]?"@media "+t[2]+"{"+n+"}":n}).join("")},t.i=function(e,n){"string"==typeof e&&(e=[[null,e,""]]);for(var r={},i=0;i<this.length;i++){var o=this[i][0];"number"==typeof o&&(r[o]=!0)}for(i=0;i<e.length;i++){var l=e[i];"number"==typeof l[0]&&r[l[0]]||(n&&!l[2]?l[2]=n:n&&(l[2]="("+l[2]+") and ("+n+")"),t.push(l))}},t}},function(e,t){e.exports=n},function(e,t,n){e.exports=function(){"use strict";var e=navigator.userAgent,t=navigator.platform,n=/gecko\/\d/i.test(e),r=/MSIE \d/.test(e),i=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(e),o=/Edge\/(\d+)/.exec(e),l=r||i||o,a=l&&(r?document.documentMode||6:+(o||i)[1]),s=!o&&/WebKit\//.test(e),c=s&&/Qt\/\d+\.\d+/.test(e),u=!o&&/Chrome\//.test(e),h=/Opera\//.test(e),f=/Apple Computer/.test(navigator.vendor),d=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(e),p=/PhantomJS/.test(e),g=!o&&/AppleWebKit/.test(e)&&/Mobile\/\w+/.test(e),m=/Android/.test(e),v=g||m||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(e),b=g||/Mac/.test(t),y=/\bCrOS\b/.test(e),x=/win/i.test(t),w=h&&e.match(/Version\/(\d*\.\d*)/);w&&(w=Number(w[1])),w&&w>=15&&(h=!1,s=!0);var C=b&&(c||h&&(null==w||w<12.11)),k=n||l&&a>=9;function M(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var S,L=function(e,t){var n=e.className,r=M(t).exec(n);if(r){var i=n.slice(r.index+r[0].length);e.className=n.slice(0,r.index)+(i?r[1]+i:"")}};function E(e){for(var t=e.childNodes.length;t>0;--t)e.removeChild(e.firstChild);return e}function T(e,t){return E(e).appendChild(t)}function N(e,t,n,r){var i=document.createElement(e);if(n&&(i.className=n),r&&(i.style.cssText=r),"string"==typeof t)i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function A(e,t,n,r){var i=N(e,t,n,r);return i.setAttribute("role","presentation"),i}function O(e,t){if(3==t.nodeType&&(t=t.parentNode),e.contains)return e.contains(t);do{if(11==t.nodeType&&(t=t.host),t==e)return!0}while(t=t.parentNode)}function D(){var e;try{e=document.activeElement}catch(t){e=document.body||null}for(;e&&e.shadowRoot&&e.shadowRoot.activeElement;)e=e.shadowRoot.activeElement;return e}function F(e,t){var n=e.className;M(t).test(n)||(e.className+=(n?" ":"")+t)}function I(e,t){for(var n=e.split(" "),r=0;r<n.length;r++)n[r]&&!M(n[r]).test(t)&&(t+=" "+n[r]);return t}S=document.createRange?function(e,t,n,r){var i=document.createRange();return i.setEnd(r||e,n),i.setStart(e,t),i}:function(e,t,n){var r=document.body.createTextRange();try{r.moveToElementText(e.parentNode)}catch(e){return r}return r.collapse(!0),r.moveEnd("character",n),r.moveStart("character",t),r};var W=function(e){e.select()};function P(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function _(e,t,n){for(var r in t||(t={}),e)!e.hasOwnProperty(r)||!1===n&&t.hasOwnProperty(r)||(t[r]=e[r]);return t}function R(e,t,n,r,i){null==t&&-1==(t=e.search(/[^\s\u00a0]/))&&(t=e.length);for(var o=r||0,l=i||0;;){var a=e.indexOf("\t",o);if(a<0||a>=t)return l+(t-o);l+=a-o,l+=n-l%n,o=a+1}}g?W=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:l&&(W=function(e){try{e.select()}catch(e){}});var H=function(){this.id=null};function z(e,t){for(var n=0;n<e.length;++n)if(e[n]==t)return n;return-1}H.prototype.set=function(e,t){clearTimeout(this.id),this.id=setTimeout(t,e)};var B=30,U={toString:function(){return"CodeMirror.Pass"}},j={scroll:!1},G={origin:"*mouse"},V={origin:"+move"};function $(e,t,n){for(var r=0,i=0;;){var o=e.indexOf("\t",r);-1==o&&(o=e.length);var l=o-r;if(o==e.length||i+l>=t)return r+Math.min(l,t-i);if(i+=o-r,r=o+1,(i+=n-i%n)>=t)return r}}var K=[""];function Y(e){for(;K.length<=e;)K.push(X(K)+" ");return K[e]}function X(e){return e[e.length-1]}function q(e,t){for(var n=[],r=0;r<e.length;r++)n[r]=t(e[r],r);return n}function Z(){}function Q(e,t){var n;return Object.create?n=Object.create(e):(Z.prototype=e,n=new Z),t&&_(t,n),n}var J=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function ee(e){return/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||J.test(e))}function te(e,t){return t?!!(t.source.indexOf("\\w")>-1&&ee(e))||t.test(e):ee(e)}function ne(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return!1;return!0}var re=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function ie(e){return e.charCodeAt(0)>=768&&re.test(e)}function oe(e,t,n){for(;(n<0?t>0:t<e.length)&&ie(e.charAt(t));)t+=n;return t}function le(e,t,n){for(var r=t>n?-1:1;;){if(t==n)return t;var i=(t+n)/2,o=r<0?Math.ceil(i):Math.floor(i);if(o==t)return e(o)?t:n;e(o)?n=o:t=o+r}}function ae(e,t){if((t-=e.first)<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");for(var n=e;!n.lines;)for(var r=0;;++r){var i=n.children[r],o=i.chunkSize();if(t<o){n=i;break}t-=o}return n.lines[t]}function se(e,t,n){var r=[],i=t.line;return e.iter(t.line,n.line+1,function(e){var o=e.text;i==n.line&&(o=o.slice(0,n.ch)),i==t.line&&(o=o.slice(t.ch)),r.push(o),++i}),r}function ce(e,t,n){var r=[];return e.iter(t,n,function(e){r.push(e.text)}),r}function ue(e,t){var n=t-e.height;if(n)for(var r=e;r;r=r.parent)r.height+=n}function he(e){if(null==e.parent)return null;for(var t=e.parent,n=z(t.lines,e),r=t.parent;r;t=r,r=r.parent)for(var i=0;r.children[i]!=t;++i)n+=r.children[i].chunkSize();return n+t.first}function fe(e,t){var n=e.first;e:do{for(var r=0;r<e.children.length;++r){var i=e.children[r],o=i.height;if(t<o){e=i;continue e}t-=o,n+=i.chunkSize()}return n}while(!e.lines);for(var l=0;l<e.lines.length;++l){var a=e.lines[l],s=a.height;if(t<s)break;t-=s}return n+l}function de(e,t){return t>=e.first&&t<e.first+e.size}function pe(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function ge(e,t,n){if(void 0===n&&(n=null),!(this instanceof ge))return new ge(e,t,n);this.line=e,this.ch=t,this.sticky=n}function me(e,t){return e.line-t.line||e.ch-t.ch}function ve(e,t){return e.sticky==t.sticky&&0==me(e,t)}function be(e){return ge(e.line,e.ch)}function ye(e,t){return me(e,t)<0?t:e}function xe(e,t){return me(e,t)<0?e:t}function we(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function Ce(e,t){if(t.line<e.first)return ge(e.first,0);var n=e.first+e.size-1;return t.line>n?ge(n,ae(e,n).text.length):function(e,t){var n=e.ch;return null==n||n>t?ge(e.line,t):n<0?ge(e.line,0):e}(t,ae(e,t.line).text.length)}function ke(e,t){for(var n=[],r=0;r<t.length;r++)n[r]=Ce(e,t[r]);return n}var Me=!1,Se=!1;function Le(e,t,n){this.marker=e,this.from=t,this.to=n}function Ee(e,t){if(e)for(var n=0;n<e.length;++n){var r=e[n];if(r.marker==t)return r}}function Te(e,t){for(var n,r=0;r<e.length;++r)e[r]!=t&&(n||(n=[])).push(e[r]);return n}function Ne(e,t){if(t.full)return null;var n=de(e,t.from.line)&&ae(e,t.from.line).markedSpans,r=de(e,t.to.line)&&ae(e,t.to.line).markedSpans;if(!n&&!r)return null;var i=t.from.ch,o=t.to.ch,l=0==me(t.from,t.to),a=function(e,t,n){var r;if(e)for(var i=0;i<e.length;++i){var o=e[i],l=o.marker,a=null==o.from||(l.inclusiveLeft?o.from<=t:o.from<t);if(a||o.from==t&&"bookmark"==l.type&&(!n||!o.marker.insertLeft)){var s=null==o.to||(l.inclusiveRight?o.to>=t:o.to>t);(r||(r=[])).push(new Le(l,o.from,s?null:o.to))}}return r}(n,i,l),s=function(e,t,n){var r;if(e)for(var i=0;i<e.length;++i){var o=e[i],l=o.marker,a=null==o.to||(l.inclusiveRight?o.to>=t:o.to>t);if(a||o.from==t&&"bookmark"==l.type&&(!n||o.marker.insertLeft)){var s=null==o.from||(l.inclusiveLeft?o.from<=t:o.from<t);(r||(r=[])).push(new Le(l,s?null:o.from-t,null==o.to?null:o.to-t))}}return r}(r,o,l),c=1==t.text.length,u=X(t.text).length+(c?i:0);if(a)for(var h=0;h<a.length;++h){var f=a[h];if(null==f.to){var d=Ee(s,f.marker);d?c&&(f.to=null==d.to?null:d.to+u):f.to=i}}if(s)for(var p=0;p<s.length;++p){var g=s[p];if(null!=g.to&&(g.to+=u),null==g.from){var m=Ee(a,g.marker);m||(g.from=u,c&&(a||(a=[])).push(g))}else g.from+=u,c&&(a||(a=[])).push(g)}a&&(a=Ae(a)),s&&s!=a&&(s=Ae(s));var v=[a];if(!c){var b,y=t.text.length-2;if(y>0&&a)for(var x=0;x<a.length;++x)null==a[x].to&&(b||(b=[])).push(new Le(a[x].marker,null,null));for(var w=0;w<y;++w)v.push(b);v.push(s)}return v}function Ae(e){for(var t=0;t<e.length;++t){var n=e[t];null!=n.from&&n.from==n.to&&!1!==n.marker.clearWhenEmpty&&e.splice(t--,1)}return e.length?e:null}function Oe(e){var t=e.markedSpans;if(t){for(var n=0;n<t.length;++n)t[n].marker.detachLine(e);e.markedSpans=null}}function De(e,t){if(t){for(var n=0;n<t.length;++n)t[n].marker.attachLine(e);e.markedSpans=t}}function Fe(e){return e.inclusiveLeft?-1:0}function Ie(e){return e.inclusiveRight?1:0}function We(e,t){var n=e.lines.length-t.lines.length;if(0!=n)return n;var r=e.find(),i=t.find(),o=me(r.from,i.from)||Fe(e)-Fe(t);if(o)return-o;var l=me(r.to,i.to)||Ie(e)-Ie(t);return l||t.id-e.id}function Pe(e,t){var n,r=Se&&e.markedSpans;if(r)for(var i=void 0,o=0;o<r.length;++o)(i=r[o]).marker.collapsed&&null==(t?i.from:i.to)&&(!n||We(n,i.marker)<0)&&(n=i.marker);return n}function _e(e){return Pe(e,!0)}function Re(e){return Pe(e,!1)}function He(e,t){var n,r=Se&&e.markedSpans;if(r)for(var i=0;i<r.length;++i){var o=r[i];o.marker.collapsed&&(null==o.from||o.from<t)&&(null==o.to||o.to>t)&&(!n||We(n,o.marker)<0)&&(n=o.marker)}return n}function ze(e,t,n,r,i){var o=ae(e,t),l=Se&&o.markedSpans;if(l)for(var a=0;a<l.length;++a){var s=l[a];if(s.marker.collapsed){var c=s.marker.find(0),u=me(c.from,n)||Fe(s.marker)-Fe(i),h=me(c.to,r)||Ie(s.marker)-Ie(i);if(!(u>=0&&h<=0||u<=0&&h>=0)&&(u<=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?me(c.to,n)>=0:me(c.to,n)>0)||u>=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?me(c.from,r)<=0:me(c.from,r)<0)))return!0}}}function Be(e){for(var t;t=_e(e);)e=t.find(-1,!0).line;return e}function Ue(e,t){var n=ae(e,t),r=Be(n);return n==r?t:he(r)}function je(e,t){if(t>e.lastLine())return t;var n,r=ae(e,t);if(!Ge(e,r))return t;for(;n=Re(r);)r=n.find(1,!0).line;return he(r)+1}function Ge(e,t){var n=Se&&t.markedSpans;if(n)for(var r=void 0,i=0;i<n.length;++i)if((r=n[i]).marker.collapsed){if(null==r.from)return!0;if(!r.marker.widgetNode&&0==r.from&&r.marker.inclusiveLeft&&Ve(e,t,r))return!0}}function Ve(e,t,n){if(null==n.to){var r=n.marker.find(1,!0);return Ve(e,r.line,Ee(r.line.markedSpans,n.marker))}if(n.marker.inclusiveRight&&n.to==t.text.length)return!0;for(var i=void 0,o=0;o<t.markedSpans.length;++o)if((i=t.markedSpans[o]).marker.collapsed&&!i.marker.widgetNode&&i.from==n.to&&(null==i.to||i.to!=n.from)&&(i.marker.inclusiveLeft||n.marker.inclusiveRight)&&Ve(e,t,i))return!0}function $e(e){for(var t=0,n=(e=Be(e)).parent,r=0;r<n.lines.length;++r){var i=n.lines[r];if(i==e)break;t+=i.height}for(var o=n.parent;o;o=(n=o).parent)for(var l=0;l<o.children.length;++l){var a=o.children[l];if(a==n)break;t+=a.height}return t}function Ke(e){if(0==e.height)return 0;for(var t,n=e.text.length,r=e;t=_e(r);){var i=t.find(0,!0);r=i.from.line,n+=i.from.ch-i.to.ch}for(r=e;t=Re(r);){var o=t.find(0,!0);n-=r.text.length-o.from.ch,r=o.to.line,n+=r.text.length-o.to.ch}return n}function Ye(e){var t=e.display,n=e.doc;t.maxLine=ae(n,n.first),t.maxLineLength=Ke(t.maxLine),t.maxLineChanged=!0,n.iter(function(e){var n=Ke(e);n>t.maxLineLength&&(t.maxLineLength=n,t.maxLine=e)})}var Xe=null;function qe(e,t,n){var r;Xe=null;for(var i=0;i<e.length;++i){var o=e[i];if(o.from<t&&o.to>t)return i;o.to==t&&(o.from!=o.to&&"before"==n?r=i:Xe=i),o.from==t&&(o.from!=o.to&&"before"!=n?r=i:Xe=i)}return null!=r?r:Xe}var Ze=function(){var e="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",t="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111";var n=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,r=/[stwN]/,i=/[LRr]/,o=/[Lb1n]/,l=/[1n]/;function a(e,t,n){this.level=e,this.from=t,this.to=n}return function(s,c){var u,h="ltr"==c?"L":"R";if(0==s.length||"ltr"==c&&!n.test(s))return!1;for(var f=s.length,d=[],p=0;p<f;++p)d.push((u=s.charCodeAt(p))<=247?e.charAt(u):1424<=u&&u<=1524?"R":1536<=u&&u<=1785?t.charAt(u-1536):1774<=u&&u<=2220?"r":8192<=u&&u<=8203?"w":8204==u?"b":"L");for(var g=0,m=h;g<f;++g){var v=d[g];"m"==v?d[g]=m:m=v}for(var b=0,y=h;b<f;++b){var x=d[b];"1"==x&&"r"==y?d[b]="n":i.test(x)&&(y=x,"r"==x&&(d[b]="R"))}for(var w=1,C=d[0];w<f-1;++w){var k=d[w];"+"==k&&"1"==C&&"1"==d[w+1]?d[w]="1":","!=k||C!=d[w+1]||"1"!=C&&"n"!=C||(d[w]=C),C=k}for(var M=0;M<f;++M){var S=d[M];if(","==S)d[M]="N";else if("%"==S){var L=void 0;for(L=M+1;L<f&&"%"==d[L];++L);for(var E=M&&"!"==d[M-1]||L<f&&"1"==d[L]?"1":"N",T=M;T<L;++T)d[T]=E;M=L-1}}for(var N=0,A=h;N<f;++N){var O=d[N];"L"==A&&"1"==O?d[N]="L":i.test(O)&&(A=O)}for(var D=0;D<f;++D)if(r.test(d[D])){var F=void 0;for(F=D+1;F<f&&r.test(d[F]);++F);for(var I="L"==(D?d[D-1]:h),W="L"==(F<f?d[F]:h),P=I==W?I?"L":"R":h,_=D;_<F;++_)d[_]=P;D=F-1}for(var R,H=[],z=0;z<f;)if(o.test(d[z])){var B=z;for(++z;z<f&&o.test(d[z]);++z);H.push(new a(0,B,z))}else{var U=z,j=H.length;for(++z;z<f&&"L"!=d[z];++z);for(var G=U;G<z;)if(l.test(d[G])){U<G&&H.splice(j,0,new a(1,U,G));var V=G;for(++G;G<z&&l.test(d[G]);++G);H.splice(j,0,new a(2,V,G)),U=G}else++G;U<z&&H.splice(j,0,new a(1,U,z))}return"ltr"==c&&(1==H[0].level&&(R=s.match(/^\s+/))&&(H[0].from=R[0].length,H.unshift(new a(0,0,R[0].length))),1==X(H).level&&(R=s.match(/\s+$/))&&(X(H).to-=R[0].length,H.push(new a(0,f-R[0].length,f)))),"rtl"==c?H.reverse():H}}();function Qe(e,t){var n=e.order;return null==n&&(n=e.order=Ze(e.text,t)),n}var Je=[],et=function(e,t,n){if(e.addEventListener)e.addEventListener(t,n,!1);else if(e.attachEvent)e.attachEvent("on"+t,n);else{var r=e._handlers||(e._handlers={});r[t]=(r[t]||Je).concat(n)}};function tt(e,t){return e._handlers&&e._handlers[t]||Je}function nt(e,t,n){if(e.removeEventListener)e.removeEventListener(t,n,!1);else if(e.detachEvent)e.detachEvent("on"+t,n);else{var r=e._handlers,i=r&&r[t];if(i){var o=z(i,n);o>-1&&(r[t]=i.slice(0,o).concat(i.slice(o+1)))}}}function rt(e,t){var n=tt(e,t);if(n.length)for(var r=Array.prototype.slice.call(arguments,2),i=0;i<n.length;++i)n[i].apply(null,r)}function it(e,t,n){return"string"==typeof t&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),rt(e,n||t.type,e,t),ut(t)||t.codemirrorIgnore}function ot(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var n=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),r=0;r<t.length;++r)-1==z(n,t[r])&&n.push(t[r])}function lt(e,t){return tt(e,t).length>0}function at(e){e.prototype.on=function(e,t){et(this,e,t)},e.prototype.off=function(e,t){nt(this,e,t)}}function st(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function ct(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function ut(e){return null!=e.defaultPrevented?e.defaultPrevented:0==e.returnValue}function ht(e){st(e),ct(e)}function ft(e){return e.target||e.srcElement}function dt(e){var t=e.which;return null==t&&(1&e.button?t=1:2&e.button?t=3:4&e.button&&(t=2)),b&&e.ctrlKey&&1==t&&(t=3),t}var pt,gt,mt=function(){if(l&&a<9)return!1;var e=N("div");return"draggable"in e||"dragDrop"in e}();function vt(e){if(null==pt){var t=N("span","​");T(e,N("span",[t,document.createTextNode("x")])),0!=e.firstChild.offsetHeight&&(pt=t.offsetWidth<=1&&t.offsetHeight>2&&!(l&&a<8))}var n=pt?N("span","​"):N("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return n.setAttribute("cm-text",""),n}function bt(e){if(null!=gt)return gt;var t=T(e,document.createTextNode("AخA")),n=S(t,0,1).getBoundingClientRect(),r=S(t,1,2).getBoundingClientRect();return E(e),!(!n||n.left==n.right)&&(gt=r.right-n.right<3)}var yt,xt=3!="\n\nb".split(/\n/).length?function(e){for(var t=0,n=[],r=e.length;t<=r;){var i=e.indexOf("\n",t);-1==i&&(i=e.length);var o=e.slice(t,"\r"==e.charAt(i-1)?i-1:i),l=o.indexOf("\r");-1!=l?(n.push(o.slice(0,l)),t+=l+1):(n.push(o),t=i+1)}return n}:function(e){return e.split(/\r\n?|\n/)},wt=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch(e){return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch(e){}return!(!t||t.parentElement()!=e)&&0!=t.compareEndPoints("StartToEnd",t)},Ct="oncopy"in(yt=N("div"))||(yt.setAttribute("oncopy","return;"),"function"==typeof yt.oncopy),kt=null,Mt={},St={};function Lt(e){if("string"==typeof e&&St.hasOwnProperty(e))e=St[e];else if(e&&"string"==typeof e.name&&St.hasOwnProperty(e.name)){var t=St[e.name];"string"==typeof t&&(t={name:t}),(e=Q(t,e)).name=t.name}else{if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return Lt("application/xml");if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return Lt("application/json")}return"string"==typeof e?{name:e}:e||{name:"null"}}function Et(e,t){t=Lt(t);var n=Mt[t.name];if(!n)return Et(e,"text/plain");var r=n(e,t);if(Tt.hasOwnProperty(t.name)){var i=Tt[t.name];for(var o in i)i.hasOwnProperty(o)&&(r.hasOwnProperty(o)&&(r["_"+o]=r[o]),r[o]=i[o])}if(r.name=t.name,t.helperType&&(r.helperType=t.helperType),t.modeProps)for(var l in t.modeProps)r[l]=t.modeProps[l];return r}var Tt={};function Nt(e,t){var n=Tt.hasOwnProperty(e)?Tt[e]:Tt[e]={};_(t,n)}function At(e,t){if(!0===t)return t;if(e.copyState)return e.copyState(t);var n={};for(var r in t){var i=t[r];i instanceof Array&&(i=i.concat([])),n[r]=i}return n}function Ot(e,t){for(var n;e.innerMode&&(n=e.innerMode(t))&&n.mode!=e;)t=n.state,e=n.mode;return n||{mode:e,state:t}}function Dt(e,t,n){return!e.startState||e.startState(t,n)}var Ft=function(e,t,n){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=n};Ft.prototype.eol=function(){return this.pos>=this.string.length},Ft.prototype.sol=function(){return this.pos==this.lineStart},Ft.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},Ft.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},Ft.prototype.eat=function(e){var t=this.string.charAt(this.pos);if("string"==typeof e?t==e:t&&(e.test?e.test(t):e(t)))return++this.pos,t},Ft.prototype.eatWhile=function(e){for(var t=this.pos;this.eat(e););return this.pos>t},Ft.prototype.eatSpace=function(){for(var e=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e},Ft.prototype.skipToEnd=function(){this.pos=this.string.length},Ft.prototype.skipTo=function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},Ft.prototype.backUp=function(e){this.pos-=e},Ft.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=R(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?R(this.string,this.lineStart,this.tabSize):0)},Ft.prototype.indentation=function(){return R(this.string,null,this.tabSize)-(this.lineStart?R(this.string,this.lineStart,this.tabSize):0)},Ft.prototype.match=function(e,t,n){if("string"!=typeof e){var r=this.string.slice(this.pos).match(e);return r&&r.index>0?null:(r&&!1!==t&&(this.pos+=r[0].length),r)}var i=function(e){return n?e.toLowerCase():e},o=this.string.substr(this.pos,e.length);if(i(o)==i(e))return!1!==t&&(this.pos+=e.length),!0},Ft.prototype.current=function(){return this.string.slice(this.start,this.pos)},Ft.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}},Ft.prototype.lookAhead=function(e){var t=this.lineOracle;return t&&t.lookAhead(e)},Ft.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};var It=function(e,t){this.state=e,this.lookAhead=t},Wt=function(e,t,n,r){this.state=t,this.doc=e,this.line=n,this.maxLookAhead=r||0,this.baseTokens=null,this.baseTokenPos=1};function Pt(e,t,n,r){var i=[e.state.modeGen],o={};Vt(e,t.text,e.doc.mode,n,function(e,t){return i.push(e,t)},o,r);for(var l=n.state,a=function(r){n.baseTokens=i;var a=e.state.overlays[r],s=1,c=0;n.state=!0,Vt(e,t.text,a.mode,n,function(e,t){for(var n=s;c<e;){var r=i[s];r>e&&i.splice(s,1,e,i[s+1],r),s+=2,c=Math.min(e,r)}if(t)if(a.opaque)i.splice(n,s-n,e,"overlay "+t),s=n+2;else for(;n<s;n+=2){var o=i[n+1];i[n+1]=(o?o+" ":"")+"overlay "+t}},o),n.state=l,n.baseTokens=null,n.baseTokenPos=1},s=0;s<e.state.overlays.length;++s)a(s);return{styles:i,classes:o.bgClass||o.textClass?o:null}}function _t(e,t,n){if(!t.styles||t.styles[0]!=e.state.modeGen){var r=Rt(e,he(t)),i=t.text.length>e.options.maxHighlightLength&&At(e.doc.mode,r.state),o=Pt(e,t,r);i&&(r.state=i),t.stateAfter=r.save(!i),t.styles=o.styles,o.classes?t.styleClasses=o.classes:t.styleClasses&&(t.styleClasses=null),n===e.doc.highlightFrontier&&(e.doc.modeFrontier=Math.max(e.doc.modeFrontier,++e.doc.highlightFrontier))}return t.styles}function Rt(e,t,n){var r=e.doc,i=e.display;if(!r.mode.startState)return new Wt(r,!0,t);var o=function(e,t,n){for(var r,i,o=e.doc,l=n?-1:t-(e.doc.mode.innerMode?1e3:100),a=t;a>l;--a){if(a<=o.first)return o.first;var s=ae(o,a-1),c=s.stateAfter;if(c&&(!n||a+(c instanceof It?c.lookAhead:0)<=o.modeFrontier))return a;var u=R(s.text,null,e.options.tabSize);(null==i||r>u)&&(i=a-1,r=u)}return i}(e,t,n),l=o>r.first&&ae(r,o-1).stateAfter,a=l?Wt.fromSaved(r,l,o):new Wt(r,Dt(r.mode),o);return r.iter(o,t,function(n){Ht(e,n.text,a);var r=a.line;n.stateAfter=r==t-1||r%5==0||r>=i.viewFrom&&r<i.viewTo?a.save():null,a.nextLine()}),n&&(r.modeFrontier=a.line),a}function Ht(e,t,n,r){var i=e.doc.mode,o=new Ft(t,e.options.tabSize,n);for(o.start=o.pos=r||0,""==t&&zt(i,n.state);!o.eol();)Bt(i,o,n.state),o.start=o.pos}function zt(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){var n=Ot(e,t);return n.mode.blankLine?n.mode.blankLine(n.state):void 0}}function Bt(e,t,n,r){for(var i=0;i<10;i++){r&&(r[0]=Ot(e,n).mode);var o=e.token(t,n);if(t.pos>t.start)return o}throw new Error("Mode "+e.name+" failed to advance stream.")}Wt.prototype.lookAhead=function(e){var t=this.doc.getLine(this.line+e);return null!=t&&e>this.maxLookAhead&&(this.maxLookAhead=e),t},Wt.prototype.baseToken=function(e){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=e;)this.baseTokenPos+=2;var t=this.baseTokens[this.baseTokenPos+1];return{type:t&&t.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-e}},Wt.prototype.nextLine=function(){this.line++,this.maxLookAhead>0&&this.maxLookAhead--},Wt.fromSaved=function(e,t,n){return t instanceof It?new Wt(e,At(e.mode,t.state),n,t.lookAhead):new Wt(e,At(e.mode,t),n)},Wt.prototype.save=function(e){var t=!1!==e?At(this.doc.mode,this.state):this.state;return this.maxLookAhead>0?new It(t,this.maxLookAhead):t};var Ut=function(e,t,n){this.start=e.start,this.end=e.pos,this.string=e.current(),this.type=t||null,this.state=n};function jt(e,t,n,r){var i,o=e.doc,l=o.mode;t=Ce(o,t);var a,s=ae(o,t.line),c=Rt(e,t.line,n),u=new Ft(s.text,e.options.tabSize,c);for(r&&(a=[]);(r||u.pos<t.ch)&&!u.eol();)u.start=u.pos,i=Bt(l,u,c.state),r&&a.push(new Ut(u,i,At(o.mode,c.state)));return r?a:new Ut(u,i,c.state)}function Gt(e,t){if(e)for(;;){var n=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!n)break;e=e.slice(0,n.index)+e.slice(n.index+n[0].length);var r=n[1]?"bgClass":"textClass";null==t[r]?t[r]=n[2]:new RegExp("(?:^|s)"+n[2]+"(?:$|s)").test(t[r])||(t[r]+=" "+n[2])}return e}function Vt(e,t,n,r,i,o,l){var a=n.flattenSpans;null==a&&(a=e.options.flattenSpans);var s,c=0,u=null,h=new Ft(t,e.options.tabSize,r),f=e.options.addModeClass&&[null];for(""==t&&Gt(zt(n,r.state),o);!h.eol();){if(h.pos>e.options.maxHighlightLength?(a=!1,l&&Ht(e,t,r,h.pos),h.pos=t.length,s=null):s=Gt(Bt(n,h,r.state,f),o),f){var d=f[0].name;d&&(s="m-"+(s?d+" "+s:d))}if(!a||u!=s){for(;c<h.start;)c=Math.min(h.start,c+5e3),i(c,u);u=s}h.start=h.pos}for(;c<h.pos;){var p=Math.min(h.pos,c+5e3);i(p,u),c=p}}var $t=function(e,t,n){this.text=e,De(this,t),this.height=n?n(this):1};function Kt(e){e.parent=null,Oe(e)}$t.prototype.lineNo=function(){return he(this)},at($t);var Yt={},Xt={};function qt(e,t){if(!e||/^\s*$/.test(e))return null;var n=t.addModeClass?Xt:Yt;return n[e]||(n[e]=e.replace(/\S+/g,"cm-$&"))}function Zt(e,t){var n=A("span",null,null,s?"padding-right: .1px":null),r={pre:A("pre",[n],"CodeMirror-line"),content:n,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:(l||s)&&e.getOption("lineWrapping")};t.measure={};for(var i=0;i<=(t.rest?t.rest.length:0);i++){var o=i?t.rest[i-1]:t.line,a=void 0;r.pos=0,r.addToken=Jt,bt(e.display.measure)&&(a=Qe(o,e.doc.direction))&&(r.addToken=en(r.addToken,a)),r.map=[];var c=t!=e.display.externalMeasured&&he(o);nn(o,r,_t(e,o,c)),o.styleClasses&&(o.styleClasses.bgClass&&(r.bgClass=I(o.styleClasses.bgClass,r.bgClass||"")),o.styleClasses.textClass&&(r.textClass=I(o.styleClasses.textClass,r.textClass||""))),0==r.map.length&&r.map.push(0,0,r.content.appendChild(vt(e.display.measure))),0==i?(t.measure.map=r.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(r.map),(t.measure.caches||(t.measure.caches=[])).push({}))}if(s){var u=r.content.lastChild;(/\bcm-tab\b/.test(u.className)||u.querySelector&&u.querySelector(".cm-tab"))&&(r.content.className="cm-tab-wrap-hack")}return rt(e,"renderLine",e,t.line,r.pre),r.pre.className&&(r.textClass=I(r.pre.className,r.textClass||"")),r}function Qt(e){var t=N("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function Jt(e,t,n,r,i,o,s){if(t){var c,u=e.splitSpaces?function(e,t){if(e.length>1&&!/  /.test(e))return e;for(var n=t,r="",i=0;i<e.length;i++){var o=e.charAt(i);" "!=o||!n||i!=e.length-1&&32!=e.charCodeAt(i+1)||(o=" "),r+=o,n=" "==o}return r}(t,e.trailingSpace):t,h=e.cm.state.specialChars,f=!1;if(h.test(t)){c=document.createDocumentFragment();for(var d=0;;){h.lastIndex=d;var p=h.exec(t),g=p?p.index-d:t.length-d;if(g){var m=document.createTextNode(u.slice(d,d+g));l&&a<9?c.appendChild(N("span",[m])):c.appendChild(m),e.map.push(e.pos,e.pos+g,m),e.col+=g,e.pos+=g}if(!p)break;d+=g+1;var v=void 0;if("\t"==p[0]){var b=e.cm.options.tabSize,y=b-e.col%b;(v=c.appendChild(N("span",Y(y),"cm-tab"))).setAttribute("role","presentation"),v.setAttribute("cm-text","\t"),e.col+=y}else"\r"==p[0]||"\n"==p[0]?((v=c.appendChild(N("span","\r"==p[0]?"␍":"␤","cm-invalidchar"))).setAttribute("cm-text",p[0]),e.col+=1):((v=e.cm.options.specialCharPlaceholder(p[0])).setAttribute("cm-text",p[0]),l&&a<9?c.appendChild(N("span",[v])):c.appendChild(v),e.col+=1);e.map.push(e.pos,e.pos+1,v),e.pos++}}else e.col+=t.length,c=document.createTextNode(u),e.map.push(e.pos,e.pos+t.length,c),l&&a<9&&(f=!0),e.pos+=t.length;if(e.trailingSpace=32==u.charCodeAt(t.length-1),n||r||i||f||s){var x=n||"";r&&(x+=r),i&&(x+=i);var w=N("span",[c],x,s);return o&&(w.title=o),e.content.appendChild(w)}e.content.appendChild(c)}}function en(e,t){return function(n,r,i,o,l,a,s){i=i?i+" cm-force-border":"cm-force-border";for(var c=n.pos,u=c+r.length;;){for(var h=void 0,f=0;f<t.length&&!((h=t[f]).to>c&&h.from<=c);f++);if(h.to>=u)return e(n,r,i,o,l,a,s);e(n,r.slice(0,h.to-c),i,o,null,a,s),o=null,r=r.slice(h.to-c),c=h.to}}}function tn(e,t,n,r){var i=!r&&n.widgetNode;i&&e.map.push(e.pos,e.pos+t,i),!r&&e.cm.display.input.needsContentAttribute&&(i||(i=e.content.appendChild(document.createElement("span"))),i.setAttribute("cm-marker",n.id)),i&&(e.cm.display.input.setUneditable(i),e.content.appendChild(i)),e.pos+=t,e.trailingSpace=!1}function nn(e,t,n){var r=e.markedSpans,i=e.text,o=0;if(r)for(var l,a,s,c,u,h,f,d=i.length,p=0,g=1,m="",v=0;;){if(v==p){s=c=u=h=a="",f=null,v=1/0;for(var b=[],y=void 0,x=0;x<r.length;++x){var w=r[x],C=w.marker;"bookmark"==C.type&&w.from==p&&C.widgetNode?b.push(C):w.from<=p&&(null==w.to||w.to>p||C.collapsed&&w.to==p&&w.from==p)?(null!=w.to&&w.to!=p&&v>w.to&&(v=w.to,c=""),C.className&&(s+=" "+C.className),C.css&&(a=(a?a+";":"")+C.css),C.startStyle&&w.from==p&&(u+=" "+C.startStyle),C.endStyle&&w.to==v&&(y||(y=[])).push(C.endStyle,w.to),C.title&&!h&&(h=C.title),C.collapsed&&(!f||We(f.marker,C)<0)&&(f=w)):w.from>p&&v>w.from&&(v=w.from)}if(y)for(var k=0;k<y.length;k+=2)y[k+1]==v&&(c+=" "+y[k]);if(!f||f.from==p)for(var M=0;M<b.length;++M)tn(t,0,b[M]);if(f&&(f.from||0)==p){if(tn(t,(null==f.to?d+1:f.to)-p,f.marker,null==f.from),null==f.to)return;f.to==p&&(f=!1)}}if(p>=d)break;for(var S=Math.min(d,v);;){if(m){var L=p+m.length;if(!f){var E=L>S?m.slice(0,S-p):m;t.addToken(t,E,l?l+s:s,u,p+E.length==v?c:"",h,a)}if(L>=S){m=m.slice(S-p),p=S;break}p=L,u=""}m=i.slice(o,o=n[g++]),l=qt(n[g++],t.cm.options)}}else for(var T=1;T<n.length;T+=2)t.addToken(t,i.slice(o,o=n[T]),qt(n[T+1],t.cm.options))}function rn(e,t,n){this.line=t,this.rest=function(e){for(var t,n;t=Re(e);)e=t.find(1,!0).line,(n||(n=[])).push(e);return n}(t),this.size=this.rest?he(X(this.rest))-n+1:1,this.node=this.text=null,this.hidden=Ge(e,t)}function on(e,t,n){for(var r,i=[],o=t;o<n;o=r){var l=new rn(e.doc,ae(e.doc,o),o);r=o+l.size,i.push(l)}return i}var ln=null,an=null;function sn(e,t){var n=tt(e,t);if(n.length){var r,i=Array.prototype.slice.call(arguments,2);ln?r=ln.delayedCallbacks:an?r=an:(r=an=[],setTimeout(cn,0));for(var o=function(e){r.push(function(){return n[e].apply(null,i)})},l=0;l<n.length;++l)o(l)}}function cn(){var e=an;an=null;for(var t=0;t<e.length;++t)e[t]()}function un(e,t,n,r){for(var i=0;i<t.changes.length;i++){var o=t.changes[i];"text"==o?dn(e,t):"gutter"==o?gn(e,t,n,r):"class"==o?pn(e,t):"widget"==o&&mn(e,t,r)}t.changes=null}function hn(e){return e.node==e.text&&(e.node=N("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),l&&a<8&&(e.node.style.zIndex=2)),e.node}function fn(e,t){var n=e.display.externalMeasured;return n&&n.line==t.line?(e.display.externalMeasured=null,t.measure=n.measure,n.built):Zt(e,t)}function dn(e,t){var n=t.text.className,r=fn(e,t);t.text==t.node&&(t.node=r.pre),t.text.parentNode.replaceChild(r.pre,t.text),t.text=r.pre,r.bgClass!=t.bgClass||r.textClass!=t.textClass?(t.bgClass=r.bgClass,t.textClass=r.textClass,pn(e,t)):n&&(t.text.className=n)}function pn(e,t){!function(e,t){var n=t.bgClass?t.bgClass+" "+(t.line.bgClass||""):t.line.bgClass;if(n&&(n+=" CodeMirror-linebackground"),t.background)n?t.background.className=n:(t.background.parentNode.removeChild(t.background),t.background=null);else if(n){var r=hn(t);t.background=r.insertBefore(N("div",null,n),r.firstChild),e.display.input.setUneditable(t.background)}}(e,t),t.line.wrapClass?hn(t).className=t.line.wrapClass:t.node!=t.text&&(t.node.className="");var n=t.textClass?t.textClass+" "+(t.line.textClass||""):t.line.textClass;t.text.className=n||""}function gn(e,t,n,r){if(t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass){var i=hn(t);t.gutterBackground=N("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px; width: "+r.gutterTotalWidth+"px"),e.display.input.setUneditable(t.gutterBackground),i.insertBefore(t.gutterBackground,t.text)}var o=t.line.gutterMarkers;if(e.options.lineNumbers||o){var l=hn(t),a=t.gutter=N("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px");if(e.display.input.setUneditable(a),l.insertBefore(a,t.text),t.line.gutterClass&&(a.className+=" "+t.line.gutterClass),!e.options.lineNumbers||o&&o["CodeMirror-linenumbers"]||(t.lineNumber=a.appendChild(N("div",pe(e.options,n),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+r.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),o)for(var s=0;s<e.options.gutters.length;++s){var c=e.options.gutters[s],u=o.hasOwnProperty(c)&&o[c];u&&a.appendChild(N("div",[u],"CodeMirror-gutter-elt","left: "+r.gutterLeft[c]+"px; width: "+r.gutterWidth[c]+"px"))}}}function mn(e,t,n){t.alignable&&(t.alignable=null);for(var r=t.node.firstChild,i=void 0;r;r=i)i=r.nextSibling,"CodeMirror-linewidget"==r.className&&t.node.removeChild(r);bn(e,t,n)}function vn(e,t,n,r){var i=fn(e,t);return t.text=t.node=i.pre,i.bgClass&&(t.bgClass=i.bgClass),i.textClass&&(t.textClass=i.textClass),pn(e,t),gn(e,t,n,r),bn(e,t,r),t.node}function bn(e,t,n){if(yn(e,t.line,t,n,!0),t.rest)for(var r=0;r<t.rest.length;r++)yn(e,t.rest[r],t,n,!1)}function yn(e,t,n,r,i){if(t.widgets)for(var o=hn(n),l=0,a=t.widgets;l<a.length;++l){var s=a[l],c=N("div",[s.node],"CodeMirror-linewidget");s.handleMouseEvents||c.setAttribute("cm-ignore-events","true"),xn(s,c,n,r),e.display.input.setUneditable(c),i&&s.above?o.insertBefore(c,n.gutter||n.text):o.appendChild(c),sn(s,"redraw")}}function xn(e,t,n,r){if(e.noHScroll){(n.alignable||(n.alignable=[])).push(t);var i=r.wrapperWidth;t.style.left=r.fixedPos+"px",e.coverGutter||(i-=r.gutterTotalWidth,t.style.paddingLeft=r.gutterTotalWidth+"px"),t.style.width=i+"px"}e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-r.gutterTotalWidth+"px"))}function wn(e){if(null!=e.height)return e.height;var t=e.doc.cm;if(!t)return 0;if(!O(document.body,e.node)){var n="position: relative;";e.coverGutter&&(n+="margin-left: -"+t.display.gutters.offsetWidth+"px;"),e.noHScroll&&(n+="width: "+t.display.wrapper.clientWidth+"px;"),T(t.display.measure,N("div",[e.node],null,n))}return e.height=e.node.parentNode.offsetHeight}function Cn(e,t){for(var n=ft(t);n!=e.wrapper;n=n.parentNode)if(!n||1==n.nodeType&&"true"==n.getAttribute("cm-ignore-events")||n.parentNode==e.sizer&&n!=e.mover)return!0}function kn(e){return e.lineSpace.offsetTop}function Mn(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function Sn(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=T(e.measure,N("pre","x")),n=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,r={left:parseInt(n.paddingLeft),right:parseInt(n.paddingRight)};return isNaN(r.left)||isNaN(r.right)||(e.cachedPaddingH=r),r}function Ln(e){return B-e.display.nativeBarWidth}function En(e){return e.display.scroller.clientWidth-Ln(e)-e.display.barWidth}function Tn(e){return e.display.scroller.clientHeight-Ln(e)-e.display.barHeight}function Nn(e,t,n){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};for(var r=0;r<e.rest.length;r++)if(e.rest[r]==t)return{map:e.measure.maps[r],cache:e.measure.caches[r]};for(var i=0;i<e.rest.length;i++)if(he(e.rest[i])>n)return{map:e.measure.maps[i],cache:e.measure.caches[i],before:!0}}function An(e,t,n,r){return Fn(e,Dn(e,t),n,r)}function On(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[sr(e,t)];var n=e.display.externalMeasured;return n&&t>=n.lineN&&t<n.lineN+n.size?n:void 0}function Dn(e,t){var n=he(t),r=On(e,n);r&&!r.text?r=null:r&&r.changes&&(un(e,r,n,rr(e)),e.curOp.forceUpdate=!0),r||(r=function(e,t){var n=he(t=Be(t)),r=e.display.externalMeasured=new rn(e.doc,t,n);r.lineN=n;var i=r.built=Zt(e,r);return r.text=i.pre,T(e.display.lineMeasure,i.pre),r}(e,t));var i=Nn(r,t,n);return{line:t,view:r,rect:null,map:i.map,cache:i.cache,before:i.before,hasHeights:!1}}function Fn(e,t,n,r,i){t.before&&(n=-1);var o,s=n+(r||"");return t.cache.hasOwnProperty(s)?o=t.cache[s]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(function(e,t,n){var r=e.options.lineWrapping,i=r&&En(e);if(!t.measure.heights||r&&t.measure.width!=i){var o=t.measure.heights=[];if(r){t.measure.width=i;for(var l=t.text.firstChild.getClientRects(),a=0;a<l.length-1;a++){var s=l[a],c=l[a+1];Math.abs(s.bottom-c.bottom)>2&&o.push((s.bottom+c.top)/2-n.top)}}o.push(n.bottom-n.top)}}(e,t.view,t.rect),t.hasHeights=!0),(o=function(e,t,n,r){var i,o=Pn(t.map,n,r),s=o.node,c=o.start,u=o.end,h=o.collapse;if(3==s.nodeType){for(var f=0;f<4;f++){for(;c&&ie(t.line.text.charAt(o.coverStart+c));)--c;for(;o.coverStart+u<o.coverEnd&&ie(t.line.text.charAt(o.coverStart+u));)++u;if((i=l&&a<9&&0==c&&u==o.coverEnd-o.coverStart?s.parentNode.getBoundingClientRect():_n(S(s,c,u).getClientRects(),r)).left||i.right||0==c)break;u=c,c-=1,h="right"}l&&a<11&&(i=function(e,t){if(!window.screen||null==screen.logicalXDPI||screen.logicalXDPI==screen.deviceXDPI||!function(e){if(null!=kt)return kt;var t=T(e,N("span","x")),n=t.getBoundingClientRect(),r=S(t,0,1).getBoundingClientRect();return kt=Math.abs(n.left-r.left)>1}(e))return t;var n=screen.logicalXDPI/screen.deviceXDPI,r=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*n,right:t.right*n,top:t.top*r,bottom:t.bottom*r}}(e.display.measure,i))}else{var d;c>0&&(h=r="right"),i=e.options.lineWrapping&&(d=s.getClientRects()).length>1?d["right"==r?d.length-1:0]:s.getBoundingClientRect()}if(l&&a<9&&!c&&(!i||!i.left&&!i.right)){var p=s.parentNode.getClientRects()[0];i=p?{left:p.left,right:p.left+nr(e.display),top:p.top,bottom:p.bottom}:Wn}for(var g=i.top-t.rect.top,m=i.bottom-t.rect.top,v=(g+m)/2,b=t.view.measure.heights,y=0;y<b.length-1&&!(v<b[y]);y++);var x=y?b[y-1]:0,w=b[y],C={left:("right"==h?i.right:i.left)-t.rect.left,right:("left"==h?i.left:i.right)-t.rect.left,top:x,bottom:w};return i.left||i.right||(C.bogus=!0),e.options.singleCursorHeightPerLine||(C.rtop=g,C.rbottom=m),C}(e,t,n,r)).bogus||(t.cache[s]=o)),{left:o.left,right:o.right,top:i?o.rtop:o.top,bottom:i?o.rbottom:o.bottom}}var In,Wn={left:0,right:0,top:0,bottom:0};function Pn(e,t,n){for(var r,i,o,l,a,s,c=0;c<e.length;c+=3)if(a=e[c],s=e[c+1],t<a?(i=0,o=1,l="left"):t<s?o=1+(i=t-a):(c==e.length-3||t==s&&e[c+3]>t)&&(i=(o=s-a)-1,t>=s&&(l="right")),null!=i){if(r=e[c+2],a==s&&n==(r.insertLeft?"left":"right")&&(l=n),"left"==n&&0==i)for(;c&&e[c-2]==e[c-3]&&e[c-1].insertLeft;)r=e[2+(c-=3)],l="left";if("right"==n&&i==s-a)for(;c<e.length-3&&e[c+3]==e[c+4]&&!e[c+5].insertLeft;)r=e[(c+=3)+2],l="right";break}return{node:r,start:i,end:o,collapse:l,coverStart:a,coverEnd:s}}function _n(e,t){var n=Wn;if("left"==t)for(var r=0;r<e.length&&(n=e[r]).left==n.right;r++);else for(var i=e.length-1;i>=0&&(n=e[i]).left==n.right;i--);return n}function Rn(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function Hn(e){e.display.externalMeasure=null,E(e.display.lineMeasure);for(var t=0;t<e.display.view.length;t++)Rn(e.display.view[t])}function zn(e){Hn(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function Bn(){return u&&m?-(document.body.getBoundingClientRect().left-parseInt(getComputedStyle(document.body).marginLeft)):window.pageXOffset||(document.documentElement||document.body).scrollLeft}function Un(){return u&&m?-(document.body.getBoundingClientRect().top-parseInt(getComputedStyle(document.body).marginTop)):window.pageYOffset||(document.documentElement||document.body).scrollTop}function jn(e){var t=0;if(e.widgets)for(var n=0;n<e.widgets.length;++n)e.widgets[n].above&&(t+=wn(e.widgets[n]));return t}function Gn(e,t,n,r,i){if(!i){var o=jn(t);n.top+=o,n.bottom+=o}if("line"==r)return n;r||(r="local");var l=$e(t);if("local"==r?l+=kn(e.display):l-=e.display.viewOffset,"page"==r||"window"==r){var a=e.display.lineSpace.getBoundingClientRect();l+=a.top+("window"==r?0:Un());var s=a.left+("window"==r?0:Bn());n.left+=s,n.right+=s}return n.top+=l,n.bottom+=l,n}function Vn(e,t,n){if("div"==n)return t;var r=t.left,i=t.top;if("page"==n)r-=Bn(),i-=Un();else if("local"==n||!n){var o=e.display.sizer.getBoundingClientRect();r+=o.left,i+=o.top}var l=e.display.lineSpace.getBoundingClientRect();return{left:r-l.left,top:i-l.top}}function $n(e,t,n,r,i){return r||(r=ae(e.doc,t.line)),Gn(e,r,An(e,r,t.ch,i),n)}function Kn(e,t,n,r,i,o){function l(t,l){var a=Fn(e,i,t,l?"right":"left",o);return l?a.left=a.right:a.right=a.left,Gn(e,r,a,n)}r=r||ae(e.doc,t.line),i||(i=Dn(e,r));var a=Qe(r,e.doc.direction),s=t.ch,c=t.sticky;if(s>=r.text.length?(s=r.text.length,c="before"):s<=0&&(s=0,c="after"),!a)return l("before"==c?s-1:s,"before"==c);function u(e,t,n){var r=a[t],i=1==r.level;return l(n?e-1:e,i!=n)}var h=qe(a,s,c),f=Xe,d=u(s,h,"before"==c);return null!=f&&(d.other=u(s,f,"before"!=c)),d}function Yn(e,t){var n=0;t=Ce(e.doc,t),e.options.lineWrapping||(n=nr(e.display)*t.ch);var r=ae(e.doc,t.line),i=$e(r)+kn(e.display);return{left:n,right:n,top:i,bottom:i+r.height}}function Xn(e,t,n,r,i){var o=ge(e,t,n);return o.xRel=i,r&&(o.outside=!0),o}function qn(e,t,n){var r=e.doc;if((n+=e.display.viewOffset)<0)return Xn(r.first,0,null,!0,-1);var i=fe(r,n),o=r.first+r.size-1;if(i>o)return Xn(r.first+r.size-1,ae(r,o).text.length,null,!0,1);t<0&&(t=0);for(var l=ae(r,i);;){var a=er(e,l,i,t,n),s=He(l,a.ch+(a.xRel>0?1:0));if(!s)return a;var c=s.find(1);if(c.line==i)return c;l=ae(r,i=c.line)}}function Zn(e,t,n,r){r-=jn(t);var i=t.text.length,o=le(function(t){return Fn(e,n,t-1).bottom<=r},i,0);return i=le(function(t){return Fn(e,n,t).top>r},o,i),{begin:o,end:i}}function Qn(e,t,n,r){n||(n=Dn(e,t));var i=Gn(e,t,Fn(e,n,r),"line").top;return Zn(e,t,n,i)}function Jn(e,t,n,r){return!(e.bottom<=n)&&(e.top>n||(r?e.left:e.right)>t)}function er(e,t,n,r,i){i-=$e(t);var o=Dn(e,t),l=jn(t),a=0,s=t.text.length,c=!0,u=Qe(t,e.doc.direction);if(u){var h=(e.options.lineWrapping?function(e,t,n,r,i,o,l){var a=Zn(e,t,r,l),s=a.begin,c=a.end;/\s/.test(t.text.charAt(c-1))&&c--;for(var u=null,h=null,f=0;f<i.length;f++){var d=i[f];if(!(d.from>=c||d.to<=s)){var p=1!=d.level,g=Fn(e,r,p?Math.min(c,d.to)-1:Math.max(s,d.from)).right,m=g<o?o-g+1e9:g-o;(!u||h>m)&&(u=d,h=m)}}return u||(u=i[i.length-1]),u.from<s&&(u={from:s,to:u.to,level:u.level}),u.to>c&&(u={from:u.from,to:c,level:u.level}),u}:function(e,t,n,r,i,o,l){var a=le(function(a){var s=i[a],c=1!=s.level;return Jn(Kn(e,ge(n,c?s.to:s.from,c?"before":"after"),"line",t,r),o,l,!0)},0,i.length-1),s=i[a];if(a>0){var c=1!=s.level,u=Kn(e,ge(n,c?s.from:s.to,c?"after":"before"),"line",t,r);Jn(u,o,l,!0)&&u.top>l&&(s=i[a-1])}return s})(e,t,n,o,u,r,i);c=1!=h.level,a=c?h.from:h.to-1,s=c?h.to:h.from-1}var f,d,p=null,g=null,m=le(function(t){var n=Fn(e,o,t);return n.top+=l,n.bottom+=l,!!Jn(n,r,i,!1)&&(n.top<=i&&n.left<=r&&(p=t,g=n),!0)},a,s),v=!1;if(g){var b=r-g.left<g.right-r,y=b==c;m=p+(y?0:1),d=y?"after":"before",f=b?g.left:g.right}else{c||m!=s&&m!=a||m++,d=0==m?"after":m==t.text.length?"before":Fn(e,o,m-(c?1:0)).bottom+l<=i==c?"after":"before";var x=Kn(e,ge(n,m,d),"line",t,o);f=x.left,v=i<x.top||i>=x.bottom}return m=oe(t.text,m,1),Xn(n,m,d,v,r-f)}function tr(e){if(null!=e.cachedTextHeight)return e.cachedTextHeight;if(null==In){In=N("pre");for(var t=0;t<49;++t)In.appendChild(document.createTextNode("x")),In.appendChild(N("br"));In.appendChild(document.createTextNode("x"))}T(e.measure,In);var n=In.offsetHeight/50;return n>3&&(e.cachedTextHeight=n),E(e.measure),n||1}function nr(e){if(null!=e.cachedCharWidth)return e.cachedCharWidth;var t=N("span","xxxxxxxxxx"),n=N("pre",[t]);T(e.measure,n);var r=t.getBoundingClientRect(),i=(r.right-r.left)/10;return i>2&&(e.cachedCharWidth=i),i||10}function rr(e){for(var t=e.display,n={},r={},i=t.gutters.clientLeft,o=t.gutters.firstChild,l=0;o;o=o.nextSibling,++l)n[e.options.gutters[l]]=o.offsetLeft+o.clientLeft+i,r[e.options.gutters[l]]=o.clientWidth;return{fixedPos:ir(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:n,gutterWidth:r,wrapperWidth:t.wrapper.clientWidth}}function ir(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function or(e){var t=tr(e.display),n=e.options.lineWrapping,r=n&&Math.max(5,e.display.scroller.clientWidth/nr(e.display)-3);return function(i){if(Ge(e.doc,i))return 0;var o=0;if(i.widgets)for(var l=0;l<i.widgets.length;l++)i.widgets[l].height&&(o+=i.widgets[l].height);return n?o+(Math.ceil(i.text.length/r)||1)*t:o+t}}function lr(e){var t=e.doc,n=or(e);t.iter(function(e){var t=n(e);t!=e.height&&ue(e,t)})}function ar(e,t,n,r){var i=e.display;if(!n&&"true"==ft(t).getAttribute("cm-not-content"))return null;var o,l,a=i.lineSpace.getBoundingClientRect();try{o=t.clientX-a.left,l=t.clientY-a.top}catch(t){return null}var s,c=qn(e,o,l);if(r&&1==c.xRel&&(s=ae(e.doc,c.line).text).length==c.ch){var u=R(s,s.length,e.options.tabSize)-s.length;c=ge(c.line,Math.max(0,Math.round((o-Sn(e.display).left)/nr(e.display))-u))}return c}function sr(e,t){if(t>=e.display.viewTo)return null;if((t-=e.display.viewFrom)<0)return null;for(var n=e.display.view,r=0;r<n.length;r++)if((t-=n[r].size)<0)return r}function cr(e){e.display.input.showSelection(e.display.input.prepareSelection())}function ur(e,t){void 0===t&&(t=!0);for(var n=e.doc,r={},i=r.cursors=document.createDocumentFragment(),o=r.selection=document.createDocumentFragment(),l=0;l<n.sel.ranges.length;l++)if(t||l!=n.sel.primIndex){var a=n.sel.ranges[l];if(!(a.from().line>=e.display.viewTo||a.to().line<e.display.viewFrom)){var s=a.empty();(s||e.options.showCursorWhenSelecting)&&hr(e,a.head,i),s||dr(e,a,o)}}return r}function hr(e,t,n){var r=Kn(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),i=n.appendChild(N("div"," ","CodeMirror-cursor"));if(i.style.left=r.left+"px",i.style.top=r.top+"px",i.style.height=Math.max(0,r.bottom-r.top)*e.options.cursorHeight+"px",r.other){var o=n.appendChild(N("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));o.style.display="",o.style.left=r.other.left+"px",o.style.top=r.other.top+"px",o.style.height=.85*(r.other.bottom-r.other.top)+"px"}}function fr(e,t){return e.top-t.top||e.left-t.left}function dr(e,t,n){var r=e.display,i=e.doc,o=document.createDocumentFragment(),l=Sn(e.display),a=l.left,s=Math.max(r.sizerWidth,En(e)-r.sizer.offsetLeft)-l.right,c="ltr"==i.direction;function u(e,t,n,r){t<0&&(t=0),t=Math.round(t),r=Math.round(r),o.appendChild(N("div",null,"CodeMirror-selected","position: absolute; left: "+e+"px;\n                             top: "+t+"px; width: "+(null==n?s-e:n)+"px;\n                             height: "+(r-t)+"px"))}function h(t,n,r){var o,l,h=ae(i,t),f=h.text.length;function d(n,r){return $n(e,ge(t,n),"div",h,r)}function p(t,n,r){var i=Qn(e,h,null,t),o="ltr"==n==("after"==r)?"left":"right",l="after"==r?i.begin:i.end-(/\s/.test(h.text.charAt(i.end-1))?2:1);return d(l,o)[o]}var g=Qe(h,i.direction);return function(e,t,n,r){if(!e)return r(t,n,"ltr",0);for(var i=!1,o=0;o<e.length;++o){var l=e[o];(l.from<n&&l.to>t||t==n&&l.to==t)&&(r(Math.max(l.from,t),Math.min(l.to,n),1==l.level?"rtl":"ltr",o),i=!0)}i||r(t,n,"ltr")}(g,n||0,null==r?f:r,function(e,t,i,h){var m="ltr"==i,v=d(e,m?"left":"right"),b=d(t-1,m?"right":"left"),y=null==n&&0==e,x=null==r&&t==f,w=0==h,C=!g||h==g.length-1;if(b.top-v.top<=3){var k=(c?y:x)&&w,M=(c?x:y)&&C,S=k?a:(m?v:b).left,L=M?s:(m?b:v).right;u(S,v.top,L-S,v.bottom)}else{var E,T,N,A;m?(E=c&&y&&w?a:v.left,T=c?s:p(e,i,"before"),N=c?a:p(t,i,"after"),A=c&&x&&C?s:b.right):(E=c?p(e,i,"before"):a,T=!c&&y&&w?s:v.right,N=!c&&x&&C?a:b.left,A=c?p(t,i,"after"):s),u(E,v.top,T-E,v.bottom),v.bottom<b.top&&u(a,v.bottom,null,b.top),u(N,b.top,A-N,b.bottom)}(!o||fr(v,o)<0)&&(o=v),fr(b,o)<0&&(o=b),(!l||fr(v,l)<0)&&(l=v),fr(b,l)<0&&(l=b)}),{start:o,end:l}}var f=t.from(),d=t.to();if(f.line==d.line)h(f.line,f.ch,d.ch);else{var p=ae(i,f.line),g=ae(i,d.line),m=Be(p)==Be(g),v=h(f.line,f.ch,m?p.text.length+1:null).end,b=h(d.line,m?0:null,d.ch).start;m&&(v.top<b.top-2?(u(v.right,v.top,null,v.bottom),u(a,b.top,b.left,b.bottom)):u(v.right,v.top,b.left-v.right,v.bottom)),v.bottom<b.top&&u(a,v.bottom,null,b.top)}n.appendChild(o)}function pr(e){if(e.state.focused){var t=e.display;clearInterval(t.blinker);var n=!0;t.cursorDiv.style.visibility="",e.options.cursorBlinkRate>0?t.blinker=setInterval(function(){return t.cursorDiv.style.visibility=(n=!n)?"":"hidden"},e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden")}}function gr(e){e.state.focused||(e.display.input.focus(),vr(e))}function mr(e){e.state.delayingBlurEvent=!0,setTimeout(function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,br(e))},100)}function vr(e,t){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1),"nocursor"!=e.options.readOnly&&(e.state.focused||(rt(e,"focus",e,t),e.state.focused=!0,F(e.display.wrapper,"CodeMirror-focused"),e.curOp||e.display.selForContextMenu==e.doc.sel||(e.display.input.reset(),s&&setTimeout(function(){return e.display.input.reset(!0)},20)),e.display.input.receivedFocus()),pr(e))}function br(e,t){e.state.delayingBlurEvent||(e.state.focused&&(rt(e,"blur",e,t),e.state.focused=!1,L(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout(function(){e.state.focused||(e.display.shift=!1)},150))}function yr(e){for(var t=e.display,n=t.lineDiv.offsetTop,r=0;r<t.view.length;r++){var i=t.view[r],o=void 0;if(!i.hidden){if(l&&a<8){var s=i.node.offsetTop+i.node.offsetHeight;o=s-n,n=s}else{var c=i.node.getBoundingClientRect();o=c.bottom-c.top}var u=i.line.height-o;if(o<2&&(o=tr(t)),(u>.005||u<-.005)&&(ue(i.line,o),xr(i.line),i.rest))for(var h=0;h<i.rest.length;h++)xr(i.rest[h])}}}function xr(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t){var n=e.widgets[t],r=n.node.parentNode;r&&(n.height=r.offsetHeight)}}function wr(e,t,n){var r=n&&null!=n.top?Math.max(0,n.top):e.scroller.scrollTop;r=Math.floor(r-kn(e));var i=n&&null!=n.bottom?n.bottom:r+e.wrapper.clientHeight,o=fe(t,r),l=fe(t,i);if(n&&n.ensure){var a=n.ensure.from.line,s=n.ensure.to.line;a<o?(o=a,l=fe(t,$e(ae(t,a))+e.wrapper.clientHeight)):Math.min(s,t.lastLine())>=l&&(o=fe(t,$e(ae(t,s))-e.wrapper.clientHeight),l=s)}return{from:o,to:Math.max(l,o+1)}}function Cr(e){var t=e.display,n=t.view;if(t.alignWidgets||t.gutters.firstChild&&e.options.fixedGutter){for(var r=ir(t)-t.scroller.scrollLeft+e.doc.scrollLeft,i=t.gutters.offsetWidth,o=r+"px",l=0;l<n.length;l++)if(!n[l].hidden){e.options.fixedGutter&&(n[l].gutter&&(n[l].gutter.style.left=o),n[l].gutterBackground&&(n[l].gutterBackground.style.left=o));var a=n[l].alignable;if(a)for(var s=0;s<a.length;s++)a[s].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=r+i+"px")}}function kr(e){if(!e.options.lineNumbers)return!1;var t=e.doc,n=pe(e.options,t.first+t.size-1),r=e.display;if(n.length!=r.lineNumChars){var i=r.measure.appendChild(N("div",[N("div",n)],"CodeMirror-linenumber CodeMirror-gutter-elt")),o=i.firstChild.offsetWidth,l=i.offsetWidth-o;return r.lineGutter.style.width="",r.lineNumInnerWidth=Math.max(o,r.lineGutter.offsetWidth-l)+1,r.lineNumWidth=r.lineNumInnerWidth+l,r.lineNumChars=r.lineNumInnerWidth?n.length:-1,r.lineGutter.style.width=r.lineNumWidth+"px",si(e),!0}return!1}function Mr(e,t){var n=e.display,r=tr(e.display);t.top<0&&(t.top=0);var i=e.curOp&&null!=e.curOp.scrollTop?e.curOp.scrollTop:n.scroller.scrollTop,o=Tn(e),l={};t.bottom-t.top>o&&(t.bottom=t.top+o);var a=e.doc.height+Mn(n),s=t.top<r,c=t.bottom>a-r;if(t.top<i)l.scrollTop=s?0:t.top;else if(t.bottom>i+o){var u=Math.min(t.top,(c?a:t.bottom)-o);u!=i&&(l.scrollTop=u)}var h=e.curOp&&null!=e.curOp.scrollLeft?e.curOp.scrollLeft:n.scroller.scrollLeft,f=En(e)-(e.options.fixedGutter?n.gutters.offsetWidth:0),d=t.right-t.left>f;return d&&(t.right=t.left+f),t.left<10?l.scrollLeft=0:t.left<h?l.scrollLeft=Math.max(0,t.left-(d?0:10)):t.right>f+h-3&&(l.scrollLeft=t.right+(d?0:10)-f),l}function Sr(e,t){null!=t&&(Tr(e),e.curOp.scrollTop=(null==e.curOp.scrollTop?e.doc.scrollTop:e.curOp.scrollTop)+t)}function Lr(e){Tr(e);var t=e.getCursor();e.curOp.scrollToPos={from:t,to:t,margin:e.options.cursorScrollMargin}}function Er(e,t,n){null==t&&null==n||Tr(e),null!=t&&(e.curOp.scrollLeft=t),null!=n&&(e.curOp.scrollTop=n)}function Tr(e){var t=e.curOp.scrollToPos;if(t){e.curOp.scrollToPos=null;var n=Yn(e,t.from),r=Yn(e,t.to);Nr(e,n,r,t.margin)}}function Nr(e,t,n,r){var i=Mr(e,{left:Math.min(t.left,n.left),top:Math.min(t.top,n.top)-r,right:Math.max(t.right,n.right),bottom:Math.max(t.bottom,n.bottom)+r});Er(e,i.scrollLeft,i.scrollTop)}function Ar(e,t){Math.abs(e.doc.scrollTop-t)<2||(n||ai(e,{top:t}),Or(e,t,!0),n&&ai(e),ni(e,100))}function Or(e,t,n){t=Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,t),(e.display.scroller.scrollTop!=t||n)&&(e.doc.scrollTop=t,e.display.scrollbars.setScrollTop(t),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t))}function Dr(e,t,n,r){t=Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth),(n?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)&&!r||(e.doc.scrollLeft=t,Cr(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function Fr(e){var t=e.display,n=t.gutters.offsetWidth,r=Math.round(e.doc.height+Mn(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?n:0,docHeight:r,scrollHeight:r+Ln(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:n}}var Ir=function(e,t,n){this.cm=n;var r=this.vert=N("div",[N("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),i=this.horiz=N("div",[N("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");r.tabIndex=i.tabIndex=-1,e(r),e(i),et(r,"scroll",function(){r.clientHeight&&t(r.scrollTop,"vertical")}),et(i,"scroll",function(){i.clientWidth&&t(i.scrollLeft,"horizontal")}),this.checkedZeroWidth=!1,l&&a<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};Ir.prototype.update=function(e){var t=e.scrollWidth>e.clientWidth+1,n=e.scrollHeight>e.clientHeight+1,r=e.nativeBarWidth;if(n){this.vert.style.display="block",this.vert.style.bottom=t?r+"px":"0";var i=e.viewHeight-(t?r:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+i)+"px"}else this.vert.style.display="",this.vert.firstChild.style.height="0";if(t){this.horiz.style.display="block",this.horiz.style.right=n?r+"px":"0",this.horiz.style.left=e.barLeft+"px";var o=e.viewWidth-e.barLeft-(n?r:0);this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+o)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&e.clientHeight>0&&(0==r&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:n?r:0,bottom:t?r:0}},Ir.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},Ir.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},Ir.prototype.zeroWidthHack=function(){var e=b&&!d?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.pointerEvents=this.vert.style.pointerEvents="none",this.disableHoriz=new H,this.disableVert=new H},Ir.prototype.enableZeroWidthBar=function(e,t,n){e.style.pointerEvents="auto",t.set(1e3,function r(){var i=e.getBoundingClientRect(),o="vert"==n?document.elementFromPoint(i.right-1,(i.top+i.bottom)/2):document.elementFromPoint((i.right+i.left)/2,i.bottom-1);o!=e?e.style.pointerEvents="none":t.set(1e3,r)})},Ir.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};var Wr=function(){};function Pr(e,t){t||(t=Fr(e));var n=e.display.barWidth,r=e.display.barHeight;_r(e,t);for(var i=0;i<4&&n!=e.display.barWidth||r!=e.display.barHeight;i++)n!=e.display.barWidth&&e.options.lineWrapping&&yr(e),_r(e,Fr(e)),n=e.display.barWidth,r=e.display.barHeight}function _r(e,t){var n=e.display,r=n.scrollbars.update(t);n.sizer.style.paddingRight=(n.barWidth=r.right)+"px",n.sizer.style.paddingBottom=(n.barHeight=r.bottom)+"px",n.heightForcer.style.borderBottom=r.bottom+"px solid transparent",r.right&&r.bottom?(n.scrollbarFiller.style.display="block",n.scrollbarFiller.style.height=r.bottom+"px",n.scrollbarFiller.style.width=r.right+"px"):n.scrollbarFiller.style.display="",r.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(n.gutterFiller.style.display="block",n.gutterFiller.style.height=r.bottom+"px",n.gutterFiller.style.width=t.gutterWidth+"px"):n.gutterFiller.style.display=""}Wr.prototype.update=function(){return{bottom:0,right:0}},Wr.prototype.setScrollLeft=function(){},Wr.prototype.setScrollTop=function(){},Wr.prototype.clear=function(){};var Rr={native:Ir,null:Wr};function Hr(e){e.display.scrollbars&&(e.display.scrollbars.clear(),e.display.scrollbars.addClass&&L(e.display.wrapper,e.display.scrollbars.addClass)),e.display.scrollbars=new Rr[e.options.scrollbarStyle](function(t){e.display.wrapper.insertBefore(t,e.display.scrollbarFiller),et(t,"mousedown",function(){e.state.focused&&setTimeout(function(){return e.display.input.focus()},0)}),t.setAttribute("cm-not-content","true")},function(t,n){"horizontal"==n?Dr(e,t):Ar(e,t)},e),e.display.scrollbars.addClass&&F(e.display.wrapper,e.display.scrollbars.addClass)}var zr=0;function Br(e){var t;e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:null,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++zr},t=e.curOp,ln?ln.ops.push(t):t.ownsGroup=ln={ops:[t],delayedCallbacks:[]}}function Ur(e){var t=e.curOp;!function(e,t){var n=e.ownsGroup;if(n)try{!function(e){var t=e.delayedCallbacks,n=0;do{for(;n<t.length;n++)t[n].call(null);for(var r=0;r<e.ops.length;r++){var i=e.ops[r];if(i.cursorActivityHandlers)for(;i.cursorActivityCalled<i.cursorActivityHandlers.length;)i.cursorActivityHandlers[i.cursorActivityCalled++].call(null,i.cm)}}while(n<t.length)}(n)}finally{ln=null,t(n)}}(t,function(e){for(var t=0;t<e.ops.length;t++)e.ops[t].cm.curOp=null;!function(e){for(var t=e.ops,n=0;n<t.length;n++)jr(t[n]);for(var r=0;r<t.length;r++)(i=t[r]).updatedDisplay=i.mustUpdate&&oi(i.cm,i.update);for(var i,o=0;o<t.length;o++)Gr(t[o]);for(var l=0;l<t.length;l++)Vr(t[l]);for(var a=0;a<t.length;a++)$r(t[a])}(e)})}function jr(e){var t=e.cm,n=t.display;!function(e){var t=e.display;!t.scrollbarsClipped&&t.scroller.offsetWidth&&(t.nativeBarWidth=t.scroller.offsetWidth-t.scroller.clientWidth,t.heightForcer.style.height=Ln(e)+"px",t.sizer.style.marginBottom=-t.nativeBarWidth+"px",t.sizer.style.borderRightWidth=Ln(e)+"px",t.scrollbarsClipped=!0)}(t),e.updateMaxLine&&Ye(t),e.mustUpdate=e.viewChanged||e.forceUpdate||null!=e.scrollTop||e.scrollToPos&&(e.scrollToPos.from.line<n.viewFrom||e.scrollToPos.to.line>=n.viewTo)||n.maxLineChanged&&t.options.lineWrapping,e.update=e.mustUpdate&&new ii(t,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function Gr(e){var t=e.cm,n=t.display;e.updatedDisplay&&yr(t),e.barMeasure=Fr(t),n.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=An(t,n.maxLine,n.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(n.scroller.clientWidth,n.sizer.offsetLeft+e.adjustWidthTo+Ln(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,n.sizer.offsetLeft+e.adjustWidthTo-En(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=n.input.prepareSelection())}function Vr(e){var t=e.cm;null!=e.adjustWidthTo&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&Dr(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var n=e.focus&&e.focus==D();e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,n),(e.updatedDisplay||e.startHeight!=t.doc.height)&&Pr(t,e.barMeasure),e.updatedDisplay&&ci(t,e.barMeasure),e.selectionChanged&&pr(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),n&&gr(e.cm)}function $r(e){var t=e.cm,n=t.display,r=t.doc;if(e.updatedDisplay&&li(t,e.update),null==n.wheelStartX||null==e.scrollTop&&null==e.scrollLeft&&!e.scrollToPos||(n.wheelStartX=n.wheelStartY=null),null!=e.scrollTop&&Or(t,e.scrollTop,e.forceScroll),null!=e.scrollLeft&&Dr(t,e.scrollLeft,!0,!0),e.scrollToPos){var i=function(e,t,n,r){var i;null==r&&(r=0),e.options.lineWrapping||t!=n||(t=t.ch?ge(t.line,"before"==t.sticky?t.ch-1:t.ch,"after"):t,n="before"==t.sticky?ge(t.line,t.ch+1,"before"):t);for(var o=0;o<5;o++){var l=!1,a=Kn(e,t),s=n&&n!=t?Kn(e,n):a;i={left:Math.min(a.left,s.left),top:Math.min(a.top,s.top)-r,right:Math.max(a.left,s.left),bottom:Math.max(a.bottom,s.bottom)+r};var c=Mr(e,i),u=e.doc.scrollTop,h=e.doc.scrollLeft;if(null!=c.scrollTop&&(Ar(e,c.scrollTop),Math.abs(e.doc.scrollTop-u)>1&&(l=!0)),null!=c.scrollLeft&&(Dr(e,c.scrollLeft),Math.abs(e.doc.scrollLeft-h)>1&&(l=!0)),!l)break}return i}(t,Ce(r,e.scrollToPos.from),Ce(r,e.scrollToPos.to),e.scrollToPos.margin);!function(e,t){if(!it(e,"scrollCursorIntoView")){var n=e.display,r=n.sizer.getBoundingClientRect(),i=null;if(t.top+r.top<0?i=!0:t.bottom+r.top>(window.innerHeight||document.documentElement.clientHeight)&&(i=!1),null!=i&&!p){var o=N("div","​",null,"position: absolute;\n                         top: "+(t.top-n.viewOffset-kn(e.display))+"px;\n                         height: "+(t.bottom-t.top+Ln(e)+n.barHeight)+"px;\n                         left: "+t.left+"px; width: "+Math.max(2,t.right-t.left)+"px;");e.display.lineSpace.appendChild(o),o.scrollIntoView(i),e.display.lineSpace.removeChild(o)}}}(t,i)}var o=e.maybeHiddenMarkers,l=e.maybeUnhiddenMarkers;if(o)for(var a=0;a<o.length;++a)o[a].lines.length||rt(o[a],"hide");if(l)for(var s=0;s<l.length;++s)l[s].lines.length&&rt(l[s],"unhide");n.wrapper.offsetHeight&&(r.scrollTop=t.display.scroller.scrollTop),e.changeObjs&&rt(t,"changes",t,e.changeObjs),e.update&&e.update.finish()}function Kr(e,t){if(e.curOp)return t();Br(e);try{return t()}finally{Ur(e)}}function Yr(e,t){return function(){if(e.curOp)return t.apply(e,arguments);Br(e);try{return t.apply(e,arguments)}finally{Ur(e)}}}function Xr(e){return function(){if(this.curOp)return e.apply(this,arguments);Br(this);try{return e.apply(this,arguments)}finally{Ur(this)}}}function qr(e){return function(){var t=this.cm;if(!t||t.curOp)return e.apply(this,arguments);Br(t);try{return e.apply(this,arguments)}finally{Ur(t)}}}function Zr(e,t,n,r){null==t&&(t=e.doc.first),null==n&&(n=e.doc.first+e.doc.size),r||(r=0);var i=e.display;if(r&&n<i.viewTo&&(null==i.updateLineNumbers||i.updateLineNumbers>t)&&(i.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=i.viewTo)Se&&Ue(e.doc,t)<i.viewTo&&Jr(e);else if(n<=i.viewFrom)Se&&je(e.doc,n+r)>i.viewFrom?Jr(e):(i.viewFrom+=r,i.viewTo+=r);else if(t<=i.viewFrom&&n>=i.viewTo)Jr(e);else if(t<=i.viewFrom){var o=ei(e,n,n+r,1);o?(i.view=i.view.slice(o.index),i.viewFrom=o.lineN,i.viewTo+=r):Jr(e)}else if(n>=i.viewTo){var l=ei(e,t,t,-1);l?(i.view=i.view.slice(0,l.index),i.viewTo=l.lineN):Jr(e)}else{var a=ei(e,t,t,-1),s=ei(e,n,n+r,1);a&&s?(i.view=i.view.slice(0,a.index).concat(on(e,a.lineN,s.lineN)).concat(i.view.slice(s.index)),i.viewTo+=r):Jr(e)}var c=i.externalMeasured;c&&(n<c.lineN?c.lineN+=r:t<c.lineN+c.size&&(i.externalMeasured=null))}function Qr(e,t,n){e.curOp.viewChanged=!0;var r=e.display,i=e.display.externalMeasured;if(i&&t>=i.lineN&&t<i.lineN+i.size&&(r.externalMeasured=null),!(t<r.viewFrom||t>=r.viewTo)){var o=r.view[sr(e,t)];if(null!=o.node){var l=o.changes||(o.changes=[]);-1==z(l,n)&&l.push(n)}}}function Jr(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function ei(e,t,n,r){var i,o=sr(e,t),l=e.display.view;if(!Se||n==e.doc.first+e.doc.size)return{index:o,lineN:n};for(var a=e.display.viewFrom,s=0;s<o;s++)a+=l[s].size;if(a!=t){if(r>0){if(o==l.length-1)return null;i=a+l[o].size-t,o++}else i=a-t;t+=i,n+=i}for(;Ue(e.doc,n)!=n;){if(o==(r<0?0:l.length-1))return null;n+=r*l[o-(r<0?1:0)].size,o+=r}return{index:o,lineN:n}}function ti(e){for(var t=e.display.view,n=0,r=0;r<t.length;r++){var i=t[r];i.hidden||i.node&&!i.changes||++n}return n}function ni(e,t){e.doc.highlightFrontier<e.display.viewTo&&e.state.highlight.set(t,P(ri,e))}function ri(e){var t=e.doc;if(!(t.highlightFrontier>=e.display.viewTo)){var n=+new Date+e.options.workTime,r=Rt(e,t.highlightFrontier),i=[];t.iter(r.line,Math.min(t.first+t.size,e.display.viewTo+500),function(o){if(r.line>=e.display.viewFrom){var l=o.styles,a=o.text.length>e.options.maxHighlightLength?At(t.mode,r.state):null,s=Pt(e,o,r,!0);a&&(r.state=a),o.styles=s.styles;var c=o.styleClasses,u=s.classes;u?o.styleClasses=u:c&&(o.styleClasses=null);for(var h=!l||l.length!=o.styles.length||c!=u&&(!c||!u||c.bgClass!=u.bgClass||c.textClass!=u.textClass),f=0;!h&&f<l.length;++f)h=l[f]!=o.styles[f];h&&i.push(r.line),o.stateAfter=r.save(),r.nextLine()}else o.text.length<=e.options.maxHighlightLength&&Ht(e,o.text,r),o.stateAfter=r.line%5==0?r.save():null,r.nextLine();if(+new Date>n)return ni(e,e.options.workDelay),!0}),t.highlightFrontier=r.line,t.modeFrontier=Math.max(t.modeFrontier,r.line),i.length&&Kr(e,function(){for(var t=0;t<i.length;t++)Qr(e,i[t],"text")})}}var ii=function(e,t,n){var r=e.display;this.viewport=t,this.visible=wr(r,e.doc,t),this.editorIsHidden=!r.wrapper.offsetWidth,this.wrapperHeight=r.wrapper.clientHeight,this.wrapperWidth=r.wrapper.clientWidth,this.oldDisplayWidth=En(e),this.force=n,this.dims=rr(e),this.events=[]};function oi(e,t){var n=e.display,r=e.doc;if(t.editorIsHidden)return Jr(e),!1;if(!t.force&&t.visible.from>=n.viewFrom&&t.visible.to<=n.viewTo&&(null==n.updateLineNumbers||n.updateLineNumbers>=n.viewTo)&&n.renderedView==n.view&&0==ti(e))return!1;kr(e)&&(Jr(e),t.dims=rr(e));var i=r.first+r.size,o=Math.max(t.visible.from-e.options.viewportMargin,r.first),l=Math.min(i,t.visible.to+e.options.viewportMargin);n.viewFrom<o&&o-n.viewFrom<20&&(o=Math.max(r.first,n.viewFrom)),n.viewTo>l&&n.viewTo-l<20&&(l=Math.min(i,n.viewTo)),Se&&(o=Ue(e.doc,o),l=je(e.doc,l));var a=o!=n.viewFrom||l!=n.viewTo||n.lastWrapHeight!=t.wrapperHeight||n.lastWrapWidth!=t.wrapperWidth;!function(e,t,n){var r=e.display;0==r.view.length||t>=r.viewTo||n<=r.viewFrom?(r.view=on(e,t,n),r.viewFrom=t):(r.viewFrom>t?r.view=on(e,t,r.viewFrom).concat(r.view):r.viewFrom<t&&(r.view=r.view.slice(sr(e,t))),r.viewFrom=t,r.viewTo<n?r.view=r.view.concat(on(e,r.viewTo,n)):r.viewTo>n&&(r.view=r.view.slice(0,sr(e,n)))),r.viewTo=n}(e,o,l),n.viewOffset=$e(ae(e.doc,n.viewFrom)),e.display.mover.style.top=n.viewOffset+"px";var c=ti(e);if(!a&&0==c&&!t.force&&n.renderedView==n.view&&(null==n.updateLineNumbers||n.updateLineNumbers>=n.viewTo))return!1;var u=function(e){if(e.hasFocus())return null;var t=D();if(!t||!O(e.display.lineDiv,t))return null;var n={activeElt:t};if(window.getSelection){var r=window.getSelection();r.anchorNode&&r.extend&&O(e.display.lineDiv,r.anchorNode)&&(n.anchorNode=r.anchorNode,n.anchorOffset=r.anchorOffset,n.focusNode=r.focusNode,n.focusOffset=r.focusOffset)}return n}(e);return c>4&&(n.lineDiv.style.display="none"),function(e,t,n){var r=e.display,i=e.options.lineNumbers,o=r.lineDiv,l=o.firstChild;function a(t){var n=t.nextSibling;return s&&b&&e.display.currentWheelTarget==t?t.style.display="none":t.parentNode.removeChild(t),n}for(var c=r.view,u=r.viewFrom,h=0;h<c.length;h++){var f=c[h];if(f.hidden);else if(f.node&&f.node.parentNode==o){for(;l!=f.node;)l=a(l);var d=i&&null!=t&&t<=u&&f.lineNumber;f.changes&&(z(f.changes,"gutter")>-1&&(d=!1),un(e,f,u,n)),d&&(E(f.lineNumber),f.lineNumber.appendChild(document.createTextNode(pe(e.options,u)))),l=f.node.nextSibling}else{var p=vn(e,f,u,n);o.insertBefore(p,l)}u+=f.size}for(;l;)l=a(l)}(e,n.updateLineNumbers,t.dims),c>4&&(n.lineDiv.style.display=""),n.renderedView=n.view,function(e){if(e&&e.activeElt&&e.activeElt!=D()&&(e.activeElt.focus(),e.anchorNode&&O(document.body,e.anchorNode)&&O(document.body,e.focusNode))){var t=window.getSelection(),n=document.createRange();n.setEnd(e.anchorNode,e.anchorOffset),n.collapse(!1),t.removeAllRanges(),t.addRange(n),t.extend(e.focusNode,e.focusOffset)}}(u),E(n.cursorDiv),E(n.selectionDiv),n.gutters.style.height=n.sizer.style.minHeight=0,a&&(n.lastWrapHeight=t.wrapperHeight,n.lastWrapWidth=t.wrapperWidth,ni(e,400)),n.updateLineNumbers=null,!0}function li(e,t){for(var n=t.viewport,r=!0;(r&&e.options.lineWrapping&&t.oldDisplayWidth!=En(e)||(n&&null!=n.top&&(n={top:Math.min(e.doc.height+Mn(e.display)-Tn(e),n.top)}),t.visible=wr(e.display,e.doc,n),!(t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)))&&oi(e,t);r=!1){yr(e);var i=Fr(e);cr(e),Pr(e,i),ci(e,i),t.force=!1}t.signal(e,"update",e),e.display.viewFrom==e.display.reportedViewFrom&&e.display.viewTo==e.display.reportedViewTo||(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function ai(e,t){var n=new ii(e,t);if(oi(e,n)){yr(e),li(e,n);var r=Fr(e);cr(e),Pr(e,r),ci(e,r),n.finish()}}function si(e){var t=e.display.gutters.offsetWidth;e.display.sizer.style.marginLeft=t+"px"}function ci(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+Ln(e)+"px"}function ui(e){var t=e.display.gutters,n=e.options.gutters;E(t);for(var r=0;r<n.length;++r){var i=n[r],o=t.appendChild(N("div",null,"CodeMirror-gutter "+i));"CodeMirror-linenumbers"==i&&(e.display.lineGutter=o,o.style.width=(e.display.lineNumWidth||1)+"px")}t.style.display=r?"":"none",si(e)}function hi(e){var t=z(e.gutters,"CodeMirror-linenumbers");-1==t&&e.lineNumbers?e.gutters=e.gutters.concat(["CodeMirror-linenumbers"]):t>-1&&!e.lineNumbers&&(e.gutters=e.gutters.slice(0),e.gutters.splice(t,1))}ii.prototype.signal=function(e,t){lt(e,t)&&this.events.push(arguments)},ii.prototype.finish=function(){for(var e=0;e<this.events.length;e++)rt.apply(null,this.events[e])};var fi=0,di=null;function pi(e){var t=e.wheelDeltaX,n=e.wheelDeltaY;return null==t&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),null==n&&e.detail&&e.axis==e.VERTICAL_AXIS?n=e.detail:null==n&&(n=e.wheelDelta),{x:t,y:n}}function gi(e){var t=pi(e);return t.x*=di,t.y*=di,t}function mi(e,t){var r=pi(t),i=r.x,o=r.y,l=e.display,a=l.scroller,c=a.scrollWidth>a.clientWidth,u=a.scrollHeight>a.clientHeight;if(i&&c||o&&u){if(o&&b&&s)e:for(var f=t.target,d=l.view;f!=a;f=f.parentNode)for(var p=0;p<d.length;p++)if(d[p].node==f){e.display.currentWheelTarget=f;break e}if(i&&!n&&!h&&null!=di)return o&&u&&Ar(e,Math.max(0,a.scrollTop+o*di)),Dr(e,Math.max(0,a.scrollLeft+i*di)),(!o||o&&u)&&st(t),void(l.wheelStartX=null);if(o&&null!=di){var g=o*di,m=e.doc.scrollTop,v=m+l.wrapper.clientHeight;g<0?m=Math.max(0,m+g-50):v=Math.min(e.doc.height,v+g+50),ai(e,{top:m,bottom:v})}fi<20&&(null==l.wheelStartX?(l.wheelStartX=a.scrollLeft,l.wheelStartY=a.scrollTop,l.wheelDX=i,l.wheelDY=o,setTimeout(function(){if(null!=l.wheelStartX){var e=a.scrollLeft-l.wheelStartX,t=a.scrollTop-l.wheelStartY,n=t&&l.wheelDY&&t/l.wheelDY||e&&l.wheelDX&&e/l.wheelDX;l.wheelStartX=l.wheelStartY=null,n&&(di=(di*fi+n)/(fi+1),++fi)}},200)):(l.wheelDX+=i,l.wheelDY+=o))}}l?di=-.53:n?di=15:u?di=-.7:f&&(di=-1/3);var vi=function(e,t){this.ranges=e,this.primIndex=t};vi.prototype.primary=function(){return this.ranges[this.primIndex]},vi.prototype.equals=function(e){if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var t=0;t<this.ranges.length;t++){var n=this.ranges[t],r=e.ranges[t];if(!ve(n.anchor,r.anchor)||!ve(n.head,r.head))return!1}return!0},vi.prototype.deepCopy=function(){for(var e=[],t=0;t<this.ranges.length;t++)e[t]=new bi(be(this.ranges[t].anchor),be(this.ranges[t].head));return new vi(e,this.primIndex)},vi.prototype.somethingSelected=function(){for(var e=0;e<this.ranges.length;e++)if(!this.ranges[e].empty())return!0;return!1},vi.prototype.contains=function(e,t){t||(t=e);for(var n=0;n<this.ranges.length;n++){var r=this.ranges[n];if(me(t,r.from())>=0&&me(e,r.to())<=0)return n}return-1};var bi=function(e,t){this.anchor=e,this.head=t};function yi(e,t){var n=e[t];e.sort(function(e,t){return me(e.from(),t.from())}),t=z(e,n);for(var r=1;r<e.length;r++){var i=e[r],o=e[r-1];if(me(o.to(),i.from())>=0){var l=xe(o.from(),i.from()),a=ye(o.to(),i.to()),s=o.empty()?i.from()==i.head:o.from()==o.head;r<=t&&--t,e.splice(--r,2,new bi(s?a:l,s?l:a))}}return new vi(e,t)}function xi(e,t){return new vi([new bi(e,t||e)],0)}function wi(e){return e.text?ge(e.from.line+e.text.length-1,X(e.text).length+(1==e.text.length?e.from.ch:0)):e.to}function Ci(e,t){if(me(e,t.from)<0)return e;if(me(e,t.to)<=0)return wi(t);var n=e.line+t.text.length-(t.to.line-t.from.line)-1,r=e.ch;return e.line==t.to.line&&(r+=wi(t).ch-t.to.ch),ge(n,r)}function ki(e,t){for(var n=[],r=0;r<e.sel.ranges.length;r++){var i=e.sel.ranges[r];n.push(new bi(Ci(i.anchor,t),Ci(i.head,t)))}return yi(n,e.sel.primIndex)}function Mi(e,t,n){return e.line==t.line?ge(n.line,e.ch-t.ch+n.ch):ge(n.line+(e.line-t.line),e.ch)}function Si(e){e.doc.mode=Et(e.options,e.doc.modeOption),Li(e)}function Li(e){e.doc.iter(function(e){e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null)}),e.doc.modeFrontier=e.doc.highlightFrontier=e.doc.first,ni(e,100),e.state.modeGen++,e.curOp&&Zr(e)}function Ei(e,t){return 0==t.from.ch&&0==t.to.ch&&""==X(t.text)&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function Ti(e,t,n,r){function i(e){return n?n[e]:null}function o(e,n,i){!function(e,t,n,r){e.text=t,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),null!=e.order&&(e.order=null),Oe(e),De(e,n);var i=r?r(e):1;i!=e.height&&ue(e,i)}(e,n,i,r),sn(e,"change",e,t)}function l(e,t){for(var n=[],o=e;o<t;++o)n.push(new $t(c[o],i(o),r));return n}var a=t.from,s=t.to,c=t.text,u=ae(e,a.line),h=ae(e,s.line),f=X(c),d=i(c.length-1),p=s.line-a.line;if(t.full)e.insert(0,l(0,c.length)),e.remove(c.length,e.size-c.length);else if(Ei(e,t)){var g=l(0,c.length-1);o(h,h.text,d),p&&e.remove(a.line,p),g.length&&e.insert(a.line,g)}else if(u==h)if(1==c.length)o(u,u.text.slice(0,a.ch)+f+u.text.slice(s.ch),d);else{var m=l(1,c.length-1);m.push(new $t(f+u.text.slice(s.ch),d,r)),o(u,u.text.slice(0,a.ch)+c[0],i(0)),e.insert(a.line+1,m)}else if(1==c.length)o(u,u.text.slice(0,a.ch)+c[0]+h.text.slice(s.ch),i(0)),e.remove(a.line+1,p);else{o(u,u.text.slice(0,a.ch)+c[0],i(0)),o(h,f+h.text.slice(s.ch),d);var v=l(1,c.length-1);p>1&&e.remove(a.line+1,p-1),e.insert(a.line+1,v)}sn(e,"change",e,t)}function Ni(e,t,n){!function e(r,i,o){if(r.linked)for(var l=0;l<r.linked.length;++l){var a=r.linked[l];if(a.doc!=i){var s=o&&a.sharedHist;n&&!s||(t(a.doc,s),e(a.doc,r,s))}}}(e,null,!0)}function Ai(e,t){if(t.cm)throw new Error("This document is already in use.");e.doc=t,t.cm=e,lr(e),Si(e),Oi(e),e.options.lineWrapping||Ye(e),e.options.mode=t.modeOption,Zr(e)}function Oi(e){("rtl"==e.doc.direction?F:L)(e.display.lineDiv,"CodeMirror-rtl")}function Di(e){this.done=[],this.undone=[],this.undoDepth=1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e||1}function Fi(e,t){var n={from:be(t.from),to:wi(t),text:se(e,t.from,t.to)};return Ri(e,n,t.from.line,t.to.line+1),Ni(e,function(e){return Ri(e,n,t.from.line,t.to.line+1)},!0),n}function Ii(e){for(;e.length;){var t=X(e);if(!t.ranges)break;e.pop()}}function Wi(e,t,n,r){var i=e.history;i.undone.length=0;var o,l,a=+new Date;if((i.lastOp==r||i.lastOrigin==t.origin&&t.origin&&("+"==t.origin.charAt(0)&&i.lastModTime>a-(e.cm?e.cm.options.historyEventDelay:500)||"*"==t.origin.charAt(0)))&&(o=function(e,t){return t?(Ii(e.done),X(e.done)):e.done.length&&!X(e.done).ranges?X(e.done):e.done.length>1&&!e.done[e.done.length-2].ranges?(e.done.pop(),X(e.done)):void 0}(i,i.lastOp==r)))l=X(o.changes),0==me(t.from,t.to)&&0==me(t.from,l.to)?l.to=wi(t):o.changes.push(Fi(e,t));else{var s=X(i.done);for(s&&s.ranges||_i(e.sel,i.done),o={changes:[Fi(e,t)],generation:i.generation},i.done.push(o);i.done.length>i.undoDepth;)i.done.shift(),i.done[0].ranges||i.done.shift()}i.done.push(n),i.generation=++i.maxGeneration,i.lastModTime=i.lastSelTime=a,i.lastOp=i.lastSelOp=r,i.lastOrigin=i.lastSelOrigin=t.origin,l||rt(e,"historyAdded")}function Pi(e,t,n,r){var i=e.history,o=r&&r.origin;n==i.lastSelOp||o&&i.lastSelOrigin==o&&(i.lastModTime==i.lastSelTime&&i.lastOrigin==o||function(e,t,n,r){var i=t.charAt(0);return"*"==i||"+"==i&&n.ranges.length==r.ranges.length&&n.somethingSelected()==r.somethingSelected()&&new Date-e.history.lastSelTime<=(e.cm?e.cm.options.historyEventDelay:500)}(e,o,X(i.done),t))?i.done[i.done.length-1]=t:_i(t,i.done),i.lastSelTime=+new Date,i.lastSelOrigin=o,i.lastSelOp=n,r&&!1!==r.clearRedo&&Ii(i.undone)}function _i(e,t){var n=X(t);n&&n.ranges&&n.equals(e)||t.push(e)}function Ri(e,t,n,r){var i=t["spans_"+e.id],o=0;e.iter(Math.max(e.first,n),Math.min(e.first+e.size,r),function(n){n.markedSpans&&((i||(i=t["spans_"+e.id]={}))[o]=n.markedSpans),++o})}function Hi(e){if(!e)return null;for(var t,n=0;n<e.length;++n)e[n].marker.explicitlyCleared?t||(t=e.slice(0,n)):t&&t.push(e[n]);return t?t.length?t:null:e}function zi(e,t){var n=function(e,t){var n=t["spans_"+e.id];if(!n)return null;for(var r=[],i=0;i<t.text.length;++i)r.push(Hi(n[i]));return r}(e,t),r=Ne(e,t);if(!n)return r;if(!r)return n;for(var i=0;i<n.length;++i){var o=n[i],l=r[i];if(o&&l)e:for(var a=0;a<l.length;++a){for(var s=l[a],c=0;c<o.length;++c)if(o[c].marker==s.marker)continue e;o.push(s)}else l&&(n[i]=l)}return n}function Bi(e,t,n){for(var r=[],i=0;i<e.length;++i){var o=e[i];if(o.ranges)r.push(n?vi.prototype.deepCopy.call(o):o);else{var l=o.changes,a=[];r.push({changes:a});for(var s=0;s<l.length;++s){var c=l[s],u=void 0;if(a.push({from:c.from,to:c.to,text:c.text}),t)for(var h in c)(u=h.match(/^spans_(\d+)$/))&&z(t,Number(u[1]))>-1&&(X(a)[h]=c[h],delete c[h])}}}return r}function Ui(e,t,n,r){if(r){var i=e.anchor;if(n){var o=me(t,i)<0;o!=me(n,i)<0?(i=t,t=n):o!=me(t,n)<0&&(t=n)}return new bi(i,t)}return new bi(n||t,t)}function ji(e,t,n,r,i){null==i&&(i=e.cm&&(e.cm.display.shift||e.extend)),Yi(e,new vi([Ui(e.sel.primary(),t,n,i)],0),r)}function Gi(e,t,n){for(var r=[],i=e.cm&&(e.cm.display.shift||e.extend),o=0;o<e.sel.ranges.length;o++)r[o]=Ui(e.sel.ranges[o],t[o],null,i);var l=yi(r,e.sel.primIndex);Yi(e,l,n)}function Vi(e,t,n,r){var i=e.sel.ranges.slice(0);i[t]=n,Yi(e,yi(i,e.sel.primIndex),r)}function $i(e,t,n,r){Yi(e,xi(t,n),r)}function Ki(e,t,n){var r=e.history.done,i=X(r);i&&i.ranges?(r[r.length-1]=t,Xi(e,t,n)):Yi(e,t,n)}function Yi(e,t,n){Xi(e,t,n),Pi(e,e.sel,e.cm?e.cm.curOp.id:NaN,n)}function Xi(e,t,n){(lt(e,"beforeSelectionChange")||e.cm&&lt(e.cm,"beforeSelectionChange"))&&(t=function(e,t,n){var r={ranges:t.ranges,update:function(t){this.ranges=[];for(var n=0;n<t.length;n++)this.ranges[n]=new bi(Ce(e,t[n].anchor),Ce(e,t[n].head))},origin:n&&n.origin};return rt(e,"beforeSelectionChange",e,r),e.cm&&rt(e.cm,"beforeSelectionChange",e.cm,r),r.ranges!=t.ranges?yi(r.ranges,r.ranges.length-1):t}(e,t,n));var r=n&&n.bias||(me(t.primary().head,e.sel.primary().head)<0?-1:1);qi(e,Qi(e,t,r,!0)),n&&!1===n.scroll||!e.cm||Lr(e.cm)}function qi(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=e.cm.curOp.selectionChanged=!0,ot(e.cm)),sn(e,"cursorActivity",e))}function Zi(e){qi(e,Qi(e,e.sel,null,!1))}function Qi(e,t,n,r){for(var i,o=0;o<t.ranges.length;o++){var l=t.ranges[o],a=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],s=eo(e,l.anchor,a&&a.anchor,n,r),c=eo(e,l.head,a&&a.head,n,r);(i||s!=l.anchor||c!=l.head)&&(i||(i=t.ranges.slice(0,o)),i[o]=new bi(s,c))}return i?yi(i,t.primIndex):t}function Ji(e,t,n,r,i){var o=ae(e,t.line);if(o.markedSpans)for(var l=0;l<o.markedSpans.length;++l){var a=o.markedSpans[l],s=a.marker;if((null==a.from||(s.inclusiveLeft?a.from<=t.ch:a.from<t.ch))&&(null==a.to||(s.inclusiveRight?a.to>=t.ch:a.to>t.ch))){if(i&&(rt(s,"beforeCursorEnter"),s.explicitlyCleared)){if(o.markedSpans){--l;continue}break}if(!s.atomic)continue;if(n){var c=s.find(r<0?1:-1),u=void 0;if((r<0?s.inclusiveRight:s.inclusiveLeft)&&(c=to(e,c,-r,c&&c.line==t.line?o:null)),c&&c.line==t.line&&(u=me(c,n))&&(r<0?u<0:u>0))return Ji(e,c,t,r,i)}var h=s.find(r<0?-1:1);return(r<0?s.inclusiveLeft:s.inclusiveRight)&&(h=to(e,h,r,h.line==t.line?o:null)),h?Ji(e,h,t,r,i):null}}return t}function eo(e,t,n,r,i){var o=r||1,l=Ji(e,t,n,o,i)||!i&&Ji(e,t,n,o,!0)||Ji(e,t,n,-o,i)||!i&&Ji(e,t,n,-o,!0);return l||(e.cantEdit=!0,ge(e.first,0))}function to(e,t,n,r){return n<0&&0==t.ch?t.line>e.first?Ce(e,ge(t.line-1)):null:n>0&&t.ch==(r||ae(e,t.line)).text.length?t.line<e.first+e.size-1?ge(t.line+1,0):null:new ge(t.line,t.ch+n)}function no(e){e.setSelection(ge(e.firstLine(),0),ge(e.lastLine()),j)}function ro(e,t,n){var r={canceled:!1,from:t.from,to:t.to,text:t.text,origin:t.origin,cancel:function(){return r.canceled=!0}};return n&&(r.update=function(t,n,i,o){t&&(r.from=Ce(e,t)),n&&(r.to=Ce(e,n)),i&&(r.text=i),void 0!==o&&(r.origin=o)}),rt(e,"beforeChange",e,r),e.cm&&rt(e.cm,"beforeChange",e.cm,r),r.canceled?null:{from:r.from,to:r.to,text:r.text,origin:r.origin}}function io(e,t,n){if(e.cm){if(!e.cm.curOp)return Yr(e.cm,io)(e,t,n);if(e.cm.state.suppressEdits)return}if(!(lt(e,"beforeChange")||e.cm&&lt(e.cm,"beforeChange"))||(t=ro(e,t,!0))){var r=Me&&!n&&function(e,t,n){var r=null;if(e.iter(t.line,n.line+1,function(e){if(e.markedSpans)for(var t=0;t<e.markedSpans.length;++t){var n=e.markedSpans[t].marker;!n.readOnly||r&&-1!=z(r,n)||(r||(r=[])).push(n)}}),!r)return null;for(var i=[{from:t,to:n}],o=0;o<r.length;++o)for(var l=r[o],a=l.find(0),s=0;s<i.length;++s){var c=i[s];if(!(me(c.to,a.from)<0||me(c.from,a.to)>0)){var u=[s,1],h=me(c.from,a.from),f=me(c.to,a.to);(h<0||!l.inclusiveLeft&&!h)&&u.push({from:c.from,to:a.from}),(f>0||!l.inclusiveRight&&!f)&&u.push({from:a.to,to:c.to}),i.splice.apply(i,u),s+=u.length-3}}return i}(e,t.from,t.to);if(r)for(var i=r.length-1;i>=0;--i)oo(e,{from:r[i].from,to:r[i].to,text:i?[""]:t.text,origin:t.origin});else oo(e,t)}}function oo(e,t){if(1!=t.text.length||""!=t.text[0]||0!=me(t.from,t.to)){var n=ki(e,t);Wi(e,t,n,e.cm?e.cm.curOp.id:NaN),so(e,t,n,Ne(e,t));var r=[];Ni(e,function(e,n){n||-1!=z(r,e.history)||(fo(e.history,t),r.push(e.history)),so(e,t,null,Ne(e,t))})}}function lo(e,t,n){var r=e.cm&&e.cm.state.suppressEdits;if(!r||n){for(var i,o=e.history,l=e.sel,a="undo"==t?o.done:o.undone,s="undo"==t?o.undone:o.done,c=0;c<a.length&&(i=a[c],n?!i.ranges||i.equals(e.sel):i.ranges);c++);if(c!=a.length){for(o.lastOrigin=o.lastSelOrigin=null;;){if(!(i=a.pop()).ranges){if(r)return void a.push(i);break}if(_i(i,s),n&&!i.equals(e.sel))return void Yi(e,i,{clearRedo:!1});l=i}var u=[];_i(l,s),s.push({changes:u,generation:o.generation}),o.generation=i.generation||++o.maxGeneration;for(var h=lt(e,"beforeChange")||e.cm&&lt(e.cm,"beforeChange"),f=function(n){var r=i.changes[n];if(r.origin=t,h&&!ro(e,r,!1))return a.length=0,{};u.push(Fi(e,r));var o=n?ki(e,r):X(a);so(e,r,o,zi(e,r)),!n&&e.cm&&e.cm.scrollIntoView({from:r.from,to:wi(r)});var l=[];Ni(e,function(e,t){t||-1!=z(l,e.history)||(fo(e.history,r),l.push(e.history)),so(e,r,null,zi(e,r))})},d=i.changes.length-1;d>=0;--d){var p=f(d);if(p)return p.v}}}}function ao(e,t){if(0!=t&&(e.first+=t,e.sel=new vi(q(e.sel.ranges,function(e){return new bi(ge(e.anchor.line+t,e.anchor.ch),ge(e.head.line+t,e.head.ch))}),e.sel.primIndex),e.cm)){Zr(e.cm,e.first,e.first-t,t);for(var n=e.cm.display,r=n.viewFrom;r<n.viewTo;r++)Qr(e.cm,r,"gutter")}}function so(e,t,n,r){if(e.cm&&!e.cm.curOp)return Yr(e.cm,so)(e,t,n,r);if(t.to.line<e.first)ao(e,t.text.length-1-(t.to.line-t.from.line));else if(!(t.from.line>e.lastLine())){if(t.from.line<e.first){var i=t.text.length-1-(e.first-t.from.line);ao(e,i),t={from:ge(e.first,0),to:ge(t.to.line+i,t.to.ch),text:[X(t.text)],origin:t.origin}}var o=e.lastLine();t.to.line>o&&(t={from:t.from,to:ge(o,ae(e,o).text.length),text:[t.text[0]],origin:t.origin}),t.removed=se(e,t.from,t.to),n||(n=ki(e,t)),e.cm?function(e,t,n){var r=e.doc,i=e.display,o=t.from,l=t.to,a=!1,s=o.line;e.options.lineWrapping||(s=he(Be(ae(r,o.line))),r.iter(s,l.line+1,function(e){if(e==i.maxLine)return a=!0,!0})),r.sel.contains(t.from,t.to)>-1&&ot(e),Ti(r,t,n,or(e)),e.options.lineWrapping||(r.iter(s,o.line+t.text.length,function(e){var t=Ke(e);t>i.maxLineLength&&(i.maxLine=e,i.maxLineLength=t,i.maxLineChanged=!0,a=!1)}),a&&(e.curOp.updateMaxLine=!0)),function(e,t){if(e.modeFrontier=Math.min(e.modeFrontier,t),!(e.highlightFrontier<t-10)){for(var n=e.first,r=t-1;r>n;r--){var i=ae(e,r).stateAfter;if(i&&(!(i instanceof It)||r+i.lookAhead<t)){n=r+1;break}}e.highlightFrontier=Math.min(e.highlightFrontier,n)}}(r,o.line),ni(e,400);var c=t.text.length-(l.line-o.line)-1;t.full?Zr(e):o.line!=l.line||1!=t.text.length||Ei(e.doc,t)?Zr(e,o.line,l.line+1,c):Qr(e,o.line,"text");var u=lt(e,"changes"),h=lt(e,"change");if(h||u){var f={from:o,to:l,text:t.text,removed:t.removed,origin:t.origin};h&&sn(e,"change",e,f),u&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(f)}e.display.selForContextMenu=null}(e.cm,t,r):Ti(e,t,r),Xi(e,n,j)}}function co(e,t,n,r,i){var o;r||(r=n),me(r,n)<0&&(n=(o=[r,n])[0],r=o[1]),"string"==typeof t&&(t=e.splitLines(t)),io(e,{from:n,to:r,text:t,origin:i})}function uo(e,t,n,r){n<e.line?e.line+=r:t<e.line&&(e.line=t,e.ch=0)}function ho(e,t,n,r){for(var i=0;i<e.length;++i){var o=e[i],l=!0;if(o.ranges){o.copied||((o=e[i]=o.deepCopy()).copied=!0);for(var a=0;a<o.ranges.length;a++)uo(o.ranges[a].anchor,t,n,r),uo(o.ranges[a].head,t,n,r)}else{for(var s=0;s<o.changes.length;++s){var c=o.changes[s];if(n<c.from.line)c.from=ge(c.from.line+r,c.from.ch),c.to=ge(c.to.line+r,c.to.ch);else if(t<=c.to.line){l=!1;break}}l||(e.splice(0,i+1),i=0)}}}function fo(e,t){var n=t.from.line,r=t.to.line,i=t.text.length-(r-n)-1;ho(e.done,n,r,i),ho(e.undone,n,r,i)}function po(e,t,n,r){var i=t,o=t;return"number"==typeof t?o=ae(e,we(e,t)):i=he(t),null==i?null:(r(o,i)&&e.cm&&Qr(e.cm,i,n),o)}function go(e){this.lines=e,this.parent=null;for(var t=0,n=0;n<e.length;++n)e[n].parent=this,t+=e[n].height;this.height=t}function mo(e){this.children=e;for(var t=0,n=0,r=0;r<e.length;++r){var i=e[r];t+=i.chunkSize(),n+=i.height,i.parent=this}this.size=t,this.height=n,this.parent=null}bi.prototype.from=function(){return xe(this.anchor,this.head)},bi.prototype.to=function(){return ye(this.anchor,this.head)},bi.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch},go.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var n=e,r=e+t;n<r;++n){var i=this.lines[n];this.height-=i.height,Kt(i),sn(i,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,n){this.height+=n,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var r=0;r<t.length;++r)t[r].parent=this},iterN:function(e,t,n){for(var r=e+t;e<r;++e)if(n(this.lines[e]))return!0}},mo.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){this.size-=t;for(var n=0;n<this.children.length;++n){var r=this.children[n],i=r.chunkSize();if(e<i){var o=Math.min(t,i-e),l=r.height;if(r.removeInner(e,o),this.height-=l-r.height,i==o&&(this.children.splice(n--,1),r.parent=null),0==(t-=o))break;e=0}else e-=i}if(this.size-t<25&&(this.children.length>1||!(this.children[0]instanceof go))){var a=[];this.collapse(a),this.children=[new go(a)],this.children[0].parent=this}},collapse:function(e){for(var t=0;t<this.children.length;++t)this.children[t].collapse(e)},insertInner:function(e,t,n){this.size+=t.length,this.height+=n;for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(e<=o){if(i.insertInner(e,t,n),i.lines&&i.lines.length>50){for(var l=i.lines.length%25+25,a=l;a<i.lines.length;){var s=new go(i.lines.slice(a,a+=25));i.height-=s.height,this.children.splice(++r,0,s),s.parent=this}i.lines=i.lines.slice(0,l),this.maybeSpill()}break}e-=o}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t=e.children.splice(e.children.length-5,5),n=new mo(t);if(e.parent){e.size-=n.size,e.height-=n.height;var r=z(e.parent.children,e);e.parent.children.splice(r+1,0,n)}else{var i=new mo(e.children);i.parent=e,e.children=[i,n],e=i}n.parent=e.parent}while(e.children.length>10);e.parent.maybeSpill()}},iterN:function(e,t,n){for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(e<o){var l=Math.min(t,o-e);if(i.iterN(e,l,n))return!0;if(0==(t-=l))break;e=0}else e-=o}}};var vo=function(e,t,n){if(n)for(var r in n)n.hasOwnProperty(r)&&(this[r]=n[r]);this.doc=e,this.node=t};function bo(e,t,n){$e(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&Sr(e,n)}vo.prototype.clear=function(){var e=this.doc.cm,t=this.line.widgets,n=this.line,r=he(n);if(null!=r&&t){for(var i=0;i<t.length;++i)t[i]==this&&t.splice(i--,1);t.length||(n.widgets=null);var o=wn(this);ue(n,Math.max(0,n.height-o)),e&&(Kr(e,function(){bo(e,n,-o),Qr(e,r,"widget")}),sn(e,"lineWidgetCleared",e,this,r))}},vo.prototype.changed=function(){var e=this,t=this.height,n=this.doc.cm,r=this.line;this.height=null;var i=wn(this)-t;i&&(ue(r,r.height+i),n&&Kr(n,function(){n.curOp.forceUpdate=!0,bo(n,r,i),sn(n,"lineWidgetChanged",n,e,he(r))}))},at(vo);var yo=0,xo=function(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++yo};function wo(e,t,n,r,i){if(r&&r.shared)return function(e,t,n,r,i){(r=_(r)).shared=!1;var o=[wo(e,t,n,r,i)],l=o[0],a=r.widgetNode;return Ni(e,function(e){a&&(r.widgetNode=a.cloneNode(!0)),o.push(wo(e,Ce(e,t),Ce(e,n),r,i));for(var s=0;s<e.linked.length;++s)if(e.linked[s].isParent)return;l=X(o)}),new Co(o,l)}(e,t,n,r,i);if(e.cm&&!e.cm.curOp)return Yr(e.cm,wo)(e,t,n,r,i);var o=new xo(e,i),l=me(t,n);if(r&&_(r,o,!1),l>0||0==l&&!1!==o.clearWhenEmpty)return o;if(o.replacedWith&&(o.collapsed=!0,o.widgetNode=A("span",[o.replacedWith],"CodeMirror-widget"),r.handleMouseEvents||o.widgetNode.setAttribute("cm-ignore-events","true"),r.insertLeft&&(o.widgetNode.insertLeft=!0)),o.collapsed){if(ze(e,t.line,t,n,o)||t.line!=n.line&&ze(e,n.line,t,n,o))throw new Error("Inserting collapsed marker partially overlapping an existing one");Se=!0}o.addToHistory&&Wi(e,{from:t,to:n,origin:"markText"},e.sel,NaN);var a,s=t.line,c=e.cm;if(e.iter(s,n.line+1,function(e){c&&o.collapsed&&!c.options.lineWrapping&&Be(e)==c.display.maxLine&&(a=!0),o.collapsed&&s!=t.line&&ue(e,0),function(e,t){e.markedSpans=e.markedSpans?e.markedSpans.concat([t]):[t],t.marker.attachLine(e)}(e,new Le(o,s==t.line?t.ch:null,s==n.line?n.ch:null)),++s}),o.collapsed&&e.iter(t.line,n.line+1,function(t){Ge(e,t)&&ue(t,0)}),o.clearOnEnter&&et(o,"beforeCursorEnter",function(){return o.clear()}),o.readOnly&&(Me=!0,(e.history.done.length||e.history.undone.length)&&e.clearHistory()),o.collapsed&&(o.id=++yo,o.atomic=!0),c){if(a&&(c.curOp.updateMaxLine=!0),o.collapsed)Zr(c,t.line,n.line+1);else if(o.className||o.title||o.startStyle||o.endStyle||o.css)for(var u=t.line;u<=n.line;u++)Qr(c,u,"text");o.atomic&&Zi(c.doc),sn(c,"markerAdded",c,o)}return o}xo.prototype.clear=function(){if(!this.explicitlyCleared){var e=this.doc.cm,t=e&&!e.curOp;if(t&&Br(e),lt(this,"clear")){var n=this.find();n&&sn(this,"clear",n.from,n.to)}for(var r=null,i=null,o=0;o<this.lines.length;++o){var l=this.lines[o],a=Ee(l.markedSpans,this);e&&!this.collapsed?Qr(e,he(l),"text"):e&&(null!=a.to&&(i=he(l)),null!=a.from&&(r=he(l))),l.markedSpans=Te(l.markedSpans,a),null==a.from&&this.collapsed&&!Ge(this.doc,l)&&e&&ue(l,tr(e.display))}if(e&&this.collapsed&&!e.options.lineWrapping)for(var s=0;s<this.lines.length;++s){var c=Be(this.lines[s]),u=Ke(c);u>e.display.maxLineLength&&(e.display.maxLine=c,e.display.maxLineLength=u,e.display.maxLineChanged=!0)}null!=r&&e&&this.collapsed&&Zr(e,r,i+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,e&&Zi(e.doc)),e&&sn(e,"markerCleared",e,this,r,i),t&&Ur(e),this.parent&&this.parent.clear()}},xo.prototype.find=function(e,t){var n,r;null==e&&"bookmark"==this.type&&(e=1);for(var i=0;i<this.lines.length;++i){var o=this.lines[i],l=Ee(o.markedSpans,this);if(null!=l.from&&(n=ge(t?o:he(o),l.from),-1==e))return n;if(null!=l.to&&(r=ge(t?o:he(o),l.to),1==e))return r}return n&&{from:n,to:r}},xo.prototype.changed=function(){var e=this,t=this.find(-1,!0),n=this,r=this.doc.cm;t&&r&&Kr(r,function(){var i=t.line,o=he(t.line),l=On(r,o);if(l&&(Rn(l),r.curOp.selectionChanged=r.curOp.forceUpdate=!0),r.curOp.updateMaxLine=!0,!Ge(n.doc,i)&&null!=n.height){var a=n.height;n.height=null;var s=wn(n)-a;s&&ue(i,i.height+s)}sn(r,"markerChanged",r,e)})},xo.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;t.maybeHiddenMarkers&&-1!=z(t.maybeHiddenMarkers,this)||(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},xo.prototype.detachLine=function(e){if(this.lines.splice(z(this.lines,e),1),!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(t.maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)}},at(xo);var Co=function(e,t){this.markers=e,this.primary=t;for(var n=0;n<e.length;++n)e[n].parent=this};function ko(e){return e.findMarks(ge(e.first,0),e.clipPos(ge(e.lastLine())),function(e){return e.parent})}function Mo(e){for(var t=function(t){var n=e[t],r=[n.primary.doc];Ni(n.primary.doc,function(e){return r.push(e)});for(var i=0;i<n.markers.length;i++){var o=n.markers[i];-1==z(r,o.doc)&&(o.parent=null,n.markers.splice(i--,1))}},n=0;n<e.length;n++)t(n)}Co.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var e=0;e<this.markers.length;++e)this.markers[e].clear();sn(this,"clear")}},Co.prototype.find=function(e,t){return this.primary.find(e,t)},at(Co);var So=0,Lo=function(e,t,n,r,i){if(!(this instanceof Lo))return new Lo(e,t,n,r,i);null==n&&(n=0),mo.call(this,[new go([new $t("",null)])]),this.first=n,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=n;var o=ge(n,0);this.sel=xi(o),this.history=new Di(null),this.id=++So,this.modeOption=t,this.lineSep=r,this.direction="rtl"==i?"rtl":"ltr",this.extend=!1,"string"==typeof e&&(e=this.splitLines(e)),Ti(this,{from:o,to:o,text:e}),Yi(this,xi(o),j)};Lo.prototype=Q(mo.prototype,{constructor:Lo,iter:function(e,t,n){n?this.iterN(e-this.first,t-e,n):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var n=0,r=0;r<t.length;++r)n+=t[r].height;this.insertInner(e-this.first,t,n)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=ce(this,this.first,this.first+this.size);return!1===e?t:t.join(e||this.lineSeparator())},setValue:qr(function(e){var t=ge(this.first,0),n=this.first+this.size-1;io(this,{from:t,to:ge(n,ae(this,n).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&Er(this.cm,0,0),Yi(this,xi(t),j)}),replaceRange:function(e,t,n,r){t=Ce(this,t),n=n?Ce(this,n):t,co(this,e,t,n,r)},getRange:function(e,t,n){var r=se(this,Ce(this,e),Ce(this,t));return!1===n?r:r.join(n||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(de(this,e))return ae(this,e)},getLineNumber:function(e){return he(e)},getLineHandleVisualStart:function(e){return"number"==typeof e&&(e=ae(this,e)),Be(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return Ce(this,e)},getCursor:function(e){var t=this.sel.primary();return null==e||"head"==e?t.head:"anchor"==e?t.anchor:"end"==e||"to"==e||!1===e?t.to():t.from()},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:qr(function(e,t,n){$i(this,Ce(this,"number"==typeof e?ge(e,t||0):e),null,n)}),setSelection:qr(function(e,t,n){$i(this,Ce(this,e),Ce(this,t||e),n)}),extendSelection:qr(function(e,t,n){ji(this,Ce(this,e),t&&Ce(this,t),n)}),extendSelections:qr(function(e,t){Gi(this,ke(this,e),t)}),extendSelectionsBy:qr(function(e,t){var n=q(this.sel.ranges,e);Gi(this,ke(this,n),t)}),setSelections:qr(function(e,t,n){if(e.length){for(var r=[],i=0;i<e.length;i++)r[i]=new bi(Ce(this,e[i].anchor),Ce(this,e[i].head));null==t&&(t=Math.min(e.length-1,this.sel.primIndex)),Yi(this,yi(r,t),n)}}),addSelection:qr(function(e,t,n){var r=this.sel.ranges.slice(0);r.push(new bi(Ce(this,e),Ce(this,t||e))),Yi(this,yi(r,r.length-1),n)}),getSelection:function(e){for(var t,n=this.sel.ranges,r=0;r<n.length;r++){var i=se(this,n[r].from(),n[r].to());t=t?t.concat(i):i}return!1===e?t:t.join(e||this.lineSeparator())},getSelections:function(e){for(var t=[],n=this.sel.ranges,r=0;r<n.length;r++){var i=se(this,n[r].from(),n[r].to());!1!==e&&(i=i.join(e||this.lineSeparator())),t[r]=i}return t},replaceSelection:function(e,t,n){for(var r=[],i=0;i<this.sel.ranges.length;i++)r[i]=e;this.replaceSelections(r,t,n||"+input")},replaceSelections:qr(function(e,t,n){for(var r=[],i=this.sel,o=0;o<i.ranges.length;o++){var l=i.ranges[o];r[o]={from:l.from(),to:l.to(),text:this.splitLines(e[o]),origin:n}}for(var a=t&&"end"!=t&&function(e,t,n){for(var r=[],i=ge(e.first,0),o=i,l=0;l<t.length;l++){var a=t[l],s=Mi(a.from,i,o),c=Mi(wi(a),i,o);if(i=a.to,o=c,"around"==n){var u=e.sel.ranges[l],h=me(u.head,u.anchor)<0;r[l]=new bi(h?c:s,h?s:c)}else r[l]=new bi(s,s)}return new vi(r,e.sel.primIndex)}(this,r,t),s=r.length-1;s>=0;s--)io(this,r[s]);a?Ki(this,a):this.cm&&Lr(this.cm)}),undo:qr(function(){lo(this,"undo")}),redo:qr(function(){lo(this,"redo")}),undoSelection:qr(function(){lo(this,"undo",!0)}),redoSelection:qr(function(){lo(this,"redo",!0)}),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,n=0,r=0;r<e.done.length;r++)e.done[r].ranges||++t;for(var i=0;i<e.undone.length;i++)e.undone[i].ranges||++n;return{undo:t,redo:n}},clearHistory:function(){this.history=new Di(this.history.maxGeneration)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:Bi(this.history.done),undone:Bi(this.history.undone)}},setHistory:function(e){var t=this.history=new Di(this.history.maxGeneration);t.done=Bi(e.done.slice(0),null,!0),t.undone=Bi(e.undone.slice(0),null,!0)},setGutterMarker:qr(function(e,t,n){return po(this,e,"gutter",function(e){var r=e.gutterMarkers||(e.gutterMarkers={});return r[t]=n,!n&&ne(r)&&(e.gutterMarkers=null),!0})}),clearGutter:qr(function(e){var t=this;this.iter(function(n){n.gutterMarkers&&n.gutterMarkers[e]&&po(t,n,"gutter",function(){return n.gutterMarkers[e]=null,ne(n.gutterMarkers)&&(n.gutterMarkers=null),!0})})}),lineInfo:function(e){var t;if("number"==typeof e){if(!de(this,e))return null;if(t=e,!(e=ae(this,e)))return null}else if(null==(t=he(e)))return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:qr(function(e,t,n){return po(this,e,"gutter"==t?"gutter":"class",function(e){var r="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass";if(e[r]){if(M(n).test(e[r]))return!1;e[r]+=" "+n}else e[r]=n;return!0})}),removeLineClass:qr(function(e,t,n){return po(this,e,"gutter"==t?"gutter":"class",function(e){var r="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass",i=e[r];if(!i)return!1;if(null==n)e[r]=null;else{var o=i.match(M(n));if(!o)return!1;var l=o.index+o[0].length;e[r]=i.slice(0,o.index)+(o.index&&l!=i.length?" ":"")+i.slice(l)||null}return!0})}),addLineWidget:qr(function(e,t,n){return function(e,t,n,r){var i=new vo(e,n,r),o=e.cm;return o&&i.noHScroll&&(o.display.alignWidgets=!0),po(e,t,"widget",function(t){var n=t.widgets||(t.widgets=[]);if(null==i.insertAt?n.push(i):n.splice(Math.min(n.length-1,Math.max(0,i.insertAt)),0,i),i.line=t,o&&!Ge(e,t)){var r=$e(t)<e.scrollTop;ue(t,t.height+wn(i)),r&&Sr(o,i.height),o.curOp.forceUpdate=!0}return!0}),o&&sn(o,"lineWidgetAdded",o,i,"number"==typeof t?t:he(t)),i}(this,e,t,n)}),removeLineWidget:function(e){e.clear()},markText:function(e,t,n){return wo(this,Ce(this,e),Ce(this,t),n,n&&n.type||"range")},setBookmark:function(e,t){var n={replacedWith:t&&(null==t.nodeType?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return wo(this,e=Ce(this,e),e,n,"bookmark")},findMarksAt:function(e){var t=[],n=ae(this,(e=Ce(this,e)).line).markedSpans;if(n)for(var r=0;r<n.length;++r){var i=n[r];(null==i.from||i.from<=e.ch)&&(null==i.to||i.to>=e.ch)&&t.push(i.marker.parent||i.marker)}return t},findMarks:function(e,t,n){e=Ce(this,e),t=Ce(this,t);var r=[],i=e.line;return this.iter(e.line,t.line+1,function(o){var l=o.markedSpans;if(l)for(var a=0;a<l.length;a++){var s=l[a];null!=s.to&&i==e.line&&e.ch>=s.to||null==s.from&&i!=e.line||null!=s.from&&i==t.line&&s.from>=t.ch||n&&!n(s.marker)||r.push(s.marker.parent||s.marker)}++i}),r},getAllMarks:function(){var e=[];return this.iter(function(t){var n=t.markedSpans;if(n)for(var r=0;r<n.length;++r)null!=n[r].from&&e.push(n[r].marker)}),e},posFromIndex:function(e){var t,n=this.first,r=this.lineSeparator().length;return this.iter(function(i){var o=i.text.length+r;if(o>e)return t=e,!0;e-=o,++n}),Ce(this,ge(n,t))},indexFromPos:function(e){var t=(e=Ce(this,e)).ch;if(e.line<this.first||e.ch<0)return 0;var n=this.lineSeparator().length;return this.iter(this.first,e.line,function(e){t+=e.text.length+n}),t},copy:function(e){var t=new Lo(ce(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e||(e={});var t=this.first,n=this.first+this.size;null!=e.from&&e.from>t&&(t=e.from),null!=e.to&&e.to<n&&(n=e.to);var r=new Lo(ce(this,t,n),e.mode||this.modeOption,t,this.lineSep,this.direction);return e.sharedHist&&(r.history=this.history),(this.linked||(this.linked=[])).push({doc:r,sharedHist:e.sharedHist}),r.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],function(e,t){for(var n=0;n<t.length;n++){var r=t[n],i=r.find(),o=e.clipPos(i.from),l=e.clipPos(i.to);if(me(o,l)){var a=wo(e,o,l,r.primary,r.primary.type);r.markers.push(a),a.parent=r}}}(r,ko(this)),r},unlinkDoc:function(e){if(e instanceof kl&&(e=e.doc),this.linked)for(var t=0;t<this.linked.length;++t){var n=this.linked[t];if(n.doc==e){this.linked.splice(t,1),e.unlinkDoc(this),Mo(ko(this));break}}if(e.history==this.history){var r=[e.id];Ni(e,function(e){return r.push(e.id)},!0),e.history=new Di(null),e.history.done=Bi(this.history.done,r),e.history.undone=Bi(this.history.undone,r)}},iterLinkedDocs:function(e){Ni(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):xt(e)},lineSeparator:function(){return this.lineSep||"\n"},setDirection:qr(function(e){var t;"rtl"!=e&&(e="ltr"),e!=this.direction&&(this.direction=e,this.iter(function(e){return e.order=null}),this.cm&&Kr(t=this.cm,function(){Oi(t),Zr(t)}))})}),Lo.prototype.eachLine=Lo.prototype.iter;var Eo=0;function To(e){var t=this;if(No(t),!it(t,e)&&!Cn(t.display,e)){st(e),l&&(Eo=+new Date);var n=ar(t,e,!0),r=e.dataTransfer.files;if(n&&!t.isReadOnly())if(r&&r.length&&window.FileReader&&window.File)for(var i=r.length,o=Array(i),a=0,s=function(e,r){if(!t.options.allowDropFileTypes||-1!=z(t.options.allowDropFileTypes,e.type)){var l=new FileReader;l.onload=Yr(t,function(){var e=l.result;if(/[\x00-\x08\x0e-\x1f]{2}/.test(e)&&(e=""),o[r]=e,++a==i){var s={from:n=Ce(t.doc,n),to:n,text:t.doc.splitLines(o.join(t.doc.lineSeparator())),origin:"paste"};io(t.doc,s),Ki(t.doc,xi(n,wi(s)))}}),l.readAsText(e)}},c=0;c<i;++c)s(r[c],c);else{if(t.state.draggingText&&t.doc.sel.contains(n)>-1)return t.state.draggingText(e),void setTimeout(function(){return t.display.input.focus()},20);try{var u=e.dataTransfer.getData("Text");if(u){var h;if(t.state.draggingText&&!t.state.draggingText.copy&&(h=t.listSelections()),Xi(t.doc,xi(n,n)),h)for(var f=0;f<h.length;++f)co(t.doc,"",h[f].anchor,h[f].head,"drag");t.replaceSelection(u,"around","paste"),t.display.input.focus()}}catch(e){}}}}function No(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function Ao(e){if(document.getElementsByClassName)for(var t=document.getElementsByClassName("CodeMirror"),n=0;n<t.length;n++){var r=t[n].CodeMirror;r&&e(r)}}var Oo=!1;function Do(){var e;Oo||(et(window,"resize",function(){null==e&&(e=setTimeout(function(){e=null,Ao(Fo)},100))}),et(window,"blur",function(){return Ao(br)}),Oo=!0)}function Fo(e){var t=e.display;t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize()}for(var Io={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",127:"Delete",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},Wo=0;Wo<10;Wo++)Io[Wo+48]=Io[Wo+96]=String(Wo);for(var Po=65;Po<=90;Po++)Io[Po]=String.fromCharCode(Po);for(var _o=1;_o<=12;_o++)Io[_o+111]=Io[_o+63235]="F"+_o;var Ro={};function Ho(e){var t,n,r,i,o=e.split(/-(?!$)/);e=o[o.length-1];for(var l=0;l<o.length-1;l++){var a=o[l];if(/^(cmd|meta|m)$/i.test(a))i=!0;else if(/^a(lt)?$/i.test(a))t=!0;else if(/^(c|ctrl|control)$/i.test(a))n=!0;else{if(!/^s(hift)?$/i.test(a))throw new Error("Unrecognized modifier name: "+a);r=!0}}return t&&(e="Alt-"+e),n&&(e="Ctrl-"+e),i&&(e="Cmd-"+e),r&&(e="Shift-"+e),e}function zo(e){var t={};for(var n in e)if(e.hasOwnProperty(n)){var r=e[n];if(/^(name|fallthrough|(de|at)tach)$/.test(n))continue;if("..."==r){delete e[n];continue}for(var i=q(n.split(" "),Ho),o=0;o<i.length;o++){var l=void 0,a=void 0;o==i.length-1?(a=i.join(" "),l=r):(a=i.slice(0,o+1).join(" "),l="...");var s=t[a];if(s){if(s!=l)throw new Error("Inconsistent bindings for "+a)}else t[a]=l}delete e[n]}for(var c in t)e[c]=t[c];return e}function Bo(e,t,n,r){var i=(t=Vo(t)).call?t.call(e,r):t[e];if(!1===i)return"nothing";if("..."===i)return"multi";if(null!=i&&n(i))return"handled";if(t.fallthrough){if("[object Array]"!=Object.prototype.toString.call(t.fallthrough))return Bo(e,t.fallthrough,n,r);for(var o=0;o<t.fallthrough.length;o++){var l=Bo(e,t.fallthrough[o],n,r);if(l)return l}}}function Uo(e){var t="string"==typeof e?e:Io[e.keyCode];return"Ctrl"==t||"Alt"==t||"Shift"==t||"Mod"==t}function jo(e,t,n){var r=e;return t.altKey&&"Alt"!=r&&(e="Alt-"+e),(C?t.metaKey:t.ctrlKey)&&"Ctrl"!=r&&(e="Ctrl-"+e),(C?t.ctrlKey:t.metaKey)&&"Cmd"!=r&&(e="Cmd-"+e),!n&&t.shiftKey&&"Shift"!=r&&(e="Shift-"+e),e}function Go(e,t){if(h&&34==e.keyCode&&e.char)return!1;var n=Io[e.keyCode];return null!=n&&!e.altGraphKey&&(3==e.keyCode&&e.code&&(n=e.code),jo(n,e,t))}function Vo(e){return"string"==typeof e?Ro[e]:e}function $o(e,t){for(var n=e.doc.sel.ranges,r=[],i=0;i<n.length;i++){for(var o=t(n[i]);r.length&&me(o.from,X(r).to)<=0;){var l=r.pop();if(me(l.from,o.from)<0){o.from=l.from;break}}r.push(o)}Kr(e,function(){for(var t=r.length-1;t>=0;t--)co(e.doc,"",r[t].from,r[t].to,"+delete");Lr(e)})}function Ko(e,t,n){var r=oe(e.text,t+n,n);return r<0||r>e.text.length?null:r}function Yo(e,t,n){var r=Ko(e,t.ch,n);return null==r?null:new ge(t.line,r,n<0?"after":"before")}function Xo(e,t,n,r,i){if(e){var o=Qe(n,t.doc.direction);if(o){var l,a=i<0?X(o):o[0],s=i<0==(1==a.level),c=s?"after":"before";if(a.level>0||"rtl"==t.doc.direction){var u=Dn(t,n);l=i<0?n.text.length-1:0;var h=Fn(t,u,l).top;l=le(function(e){return Fn(t,u,e).top==h},i<0==(1==a.level)?a.from:a.to-1,l),"before"==c&&(l=Ko(n,l,1))}else l=i<0?a.to:a.from;return new ge(r,l,c)}}return new ge(r,i<0?n.text.length:0,i<0?"before":"after")}Ro.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},Ro.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},Ro.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Alt-F":"goWordRight","Alt-B":"goWordLeft","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-D":"delWordAfter","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},Ro.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},Ro.default=b?Ro.macDefault:Ro.pcDefault;var qo={selectAll:no,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),j)},killLine:function(e){return $o(e,function(t){if(t.empty()){var n=ae(e.doc,t.head.line).text.length;return t.head.ch==n&&t.head.line<e.lastLine()?{from:t.head,to:ge(t.head.line+1,0)}:{from:t.head,to:ge(t.head.line,n)}}return{from:t.from(),to:t.to()}})},deleteLine:function(e){return $o(e,function(t){return{from:ge(t.from().line,0),to:Ce(e.doc,ge(t.to().line+1,0))}})},delLineLeft:function(e){return $o(e,function(e){return{from:ge(e.from().line,0),to:e.from()}})},delWrappedLineLeft:function(e){return $o(e,function(t){var n=e.charCoords(t.head,"div").top+5,r=e.coordsChar({left:0,top:n},"div");return{from:r,to:t.from()}})},delWrappedLineRight:function(e){return $o(e,function(t){var n=e.charCoords(t.head,"div").top+5,r=e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:n},"div");return{from:t.from(),to:r}})},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(ge(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(ge(e.lastLine()))},goLineStart:function(e){return e.extendSelectionsBy(function(t){return Zo(e,t.head.line)},{origin:"+move",bias:1})},goLineStartSmart:function(e){return e.extendSelectionsBy(function(t){return Qo(e,t.head)},{origin:"+move",bias:1})},goLineEnd:function(e){return e.extendSelectionsBy(function(t){return function(e,t){var n=ae(e.doc,t),r=function(e){for(var t;t=Re(e);)e=t.find(1,!0).line;return e}(n);return r!=n&&(t=he(r)),Xo(!0,e,n,t,-1)}(e,t.head.line)},{origin:"+move",bias:-1})},goLineRight:function(e){return e.extendSelectionsBy(function(t){var n=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:n},"div")},V)},goLineLeft:function(e){return e.extendSelectionsBy(function(t){var n=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:0,top:n},"div")},V)},goLineLeftSmart:function(e){return e.extendSelectionsBy(function(t){var n=e.cursorCoords(t.head,"div").top+5,r=e.coordsChar({left:0,top:n},"div");return r.ch<e.getLine(r.line).search(/\S/)?Qo(e,t.head):r},V)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"char")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("\t")},insertSoftTab:function(e){for(var t=[],n=e.listSelections(),r=e.options.tabSize,i=0;i<n.length;i++){var o=n[i].from(),l=R(e.getLine(o.line),o.ch,r);t.push(Y(r-l%r))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(e){return Kr(e,function(){for(var t=e.listSelections(),n=[],r=0;r<t.length;r++)if(t[r].empty()){var i=t[r].head,o=ae(e.doc,i.line).text;if(o)if(i.ch==o.length&&(i=new ge(i.line,i.ch-1)),i.ch>0)i=new ge(i.line,i.ch+1),e.replaceRange(o.charAt(i.ch-1)+o.charAt(i.ch-2),ge(i.line,i.ch-2),i,"+transpose");else if(i.line>e.doc.first){var l=ae(e.doc,i.line-1).text;l&&(i=new ge(i.line,1),e.replaceRange(o.charAt(0)+e.doc.lineSeparator()+l.charAt(l.length-1),ge(i.line-1,l.length-1),i,"+transpose"))}n.push(new bi(i,i))}e.setSelections(n)})},newlineAndIndent:function(e){return Kr(e,function(){for(var t=e.listSelections(),n=t.length-1;n>=0;n--)e.replaceRange(e.doc.lineSeparator(),t[n].anchor,t[n].head,"+input");t=e.listSelections();for(var r=0;r<t.length;r++)e.indentLine(t[r].from().line,null,!0);Lr(e)})},openLine:function(e){return e.replaceSelection("\n","start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function Zo(e,t){var n=ae(e.doc,t),r=Be(n);return r!=n&&(t=he(r)),Xo(!0,e,r,t,1)}function Qo(e,t){var n=Zo(e,t.line),r=ae(e.doc,n.line),i=Qe(r,e.doc.direction);if(!i||0==i[0].level){var o=Math.max(0,r.text.search(/\S/)),l=t.line==n.line&&t.ch<=o&&t.ch;return ge(n.line,l?0:o,n.sticky)}return n}function Jo(e,t,n){if("string"==typeof t&&!(t=qo[t]))return!1;e.display.input.ensurePolled();var r=e.display.shift,i=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),n&&(e.display.shift=!1),i=t(e)!=U}finally{e.display.shift=r,e.state.suppressEdits=!1}return i}var el=new H;function tl(e,t,n,r){var i=e.state.keySeq;if(i){if(Uo(t))return"handled";if(/\'$/.test(t)?e.state.keySeq=null:el.set(50,function(){e.state.keySeq==i&&(e.state.keySeq=null,e.display.input.reset())}),nl(e,i+" "+t,n,r))return!0}return nl(e,t,n,r)}function nl(e,t,n,r){var i=function(e,t,n){for(var r=0;r<e.state.keyMaps.length;r++){var i=Bo(t,e.state.keyMaps[r],n,e);if(i)return i}return e.options.extraKeys&&Bo(t,e.options.extraKeys,n,e)||Bo(t,e.options.keyMap,n,e)}(e,t,r);return"multi"==i&&(e.state.keySeq=t),"handled"==i&&sn(e,"keyHandled",e,t,n),"handled"!=i&&"multi"!=i||(st(n),pr(e)),!!i}function rl(e,t){var n=Go(t,!0);return!!n&&(t.shiftKey&&!e.state.keySeq?tl(e,"Shift-"+n,t,function(t){return Jo(e,t,!0)})||tl(e,n,t,function(t){if("string"==typeof t?/^go[A-Z]/.test(t):t.motion)return Jo(e,t)}):tl(e,n,t,function(t){return Jo(e,t)}))}var il=null;function ol(e){var t=this;if(t.curOp.focus=D(),!it(t,e)){l&&a<11&&27==e.keyCode&&(e.returnValue=!1);var n=e.keyCode;t.display.shift=16==n||e.shiftKey;var r=rl(t,e);h&&(il=r?n:null,!r&&88==n&&!Ct&&(b?e.metaKey:e.ctrlKey)&&t.replaceSelection("",null,"cut")),18!=n||/\bCodeMirror-crosshair\b/.test(t.display.lineDiv.className)||function(e){var t=e.display.lineDiv;function n(e){18!=e.keyCode&&e.altKey||(L(t,"CodeMirror-crosshair"),nt(document,"keyup",n),nt(document,"mouseover",n))}F(t,"CodeMirror-crosshair"),et(document,"keyup",n),et(document,"mouseover",n)}(t)}}function ll(e){16==e.keyCode&&(this.doc.sel.shift=!1),it(this,e)}function al(e){var t=this;if(!(Cn(t.display,e)||it(t,e)||e.ctrlKey&&!e.altKey||b&&e.metaKey)){var n=e.keyCode,r=e.charCode;if(h&&n==il)return il=null,void st(e);if(!h||e.which&&!(e.which<10)||!rl(t,e)){var i=String.fromCharCode(null==r?n:r);"\b"!=i&&(function(e,t,n){return tl(e,"'"+n+"'",t,function(t){return Jo(e,t,!0)})}(t,e,i)||t.display.input.onKeyPress(e))}}}var sl,cl,ul=function(e,t,n){this.time=e,this.pos=t,this.button=n};function hl(e){var t=this,n=t.display;if(!(it(t,e)||n.activeTouch&&n.input.supportsTouch()))if(n.input.ensurePolled(),n.shift=e.shiftKey,Cn(n,e))s||(n.scroller.draggable=!1,setTimeout(function(){return n.scroller.draggable=!0},100));else if(!pl(t,e)){var r=ar(t,e),i=dt(e),o=r?function(e,t){var n=+new Date;return cl&&cl.compare(n,e,t)?(sl=cl=null,"triple"):sl&&sl.compare(n,e,t)?(cl=new ul(n,e,t),sl=null,"double"):(sl=new ul(n,e,t),cl=null,"single")}(r,i):"single";window.focus(),1==i&&t.state.selectingText&&t.state.selectingText(e),r&&function(e,t,n,r,i){var o="Click";return"double"==r?o="Double"+o:"triple"==r&&(o="Triple"+o),tl(e,jo(o=(1==t?"Left":2==t?"Middle":"Right")+o,i),i,function(t){if("string"==typeof t&&(t=qo[t]),!t)return!1;var r=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),r=t(e,n)!=U}finally{e.state.suppressEdits=!1}return r})}(t,i,r,o,e)||(1==i?r?function(e,t,n,r){l?setTimeout(P(gr,e),0):e.curOp.focus=D();var i,o=function(e,t,n){var r=e.getOption("configureMouse"),i=r?r(e,t,n):{};if(null==i.unit){var o=y?n.shiftKey&&n.metaKey:n.altKey;i.unit=o?"rectangle":"single"==t?"char":"double"==t?"word":"line"}return(null==i.extend||e.doc.extend)&&(i.extend=e.doc.extend||n.shiftKey),null==i.addNew&&(i.addNew=b?n.metaKey:n.ctrlKey),null==i.moveOnDrag&&(i.moveOnDrag=!(b?n.altKey:n.ctrlKey)),i}(e,n,r),c=e.doc.sel;e.options.dragDrop&&mt&&!e.isReadOnly()&&"single"==n&&(i=c.contains(t))>-1&&(me((i=c.ranges[i]).from(),t)<0||t.xRel>0)&&(me(i.to(),t)>0||t.xRel<0)?function(e,t,n,r){var i=e.display,o=!1,c=Yr(e,function(t){s&&(i.scroller.draggable=!1),e.state.draggingText=!1,nt(i.wrapper.ownerDocument,"mouseup",c),nt(i.wrapper.ownerDocument,"mousemove",u),nt(i.scroller,"dragstart",h),nt(i.scroller,"drop",c),o||(st(t),r.addNew||ji(e.doc,n,null,null,r.extend),s||l&&9==a?setTimeout(function(){i.wrapper.ownerDocument.body.focus(),i.input.focus()},20):i.input.focus())}),u=function(e){o=o||Math.abs(t.clientX-e.clientX)+Math.abs(t.clientY-e.clientY)>=10},h=function(){return o=!0};s&&(i.scroller.draggable=!0),e.state.draggingText=c,c.copy=!r.moveOnDrag,i.scroller.dragDrop&&i.scroller.dragDrop(),et(i.wrapper.ownerDocument,"mouseup",c),et(i.wrapper.ownerDocument,"mousemove",u),et(i.scroller,"dragstart",h),et(i.scroller,"drop",c),mr(e),setTimeout(function(){return i.input.focus()},20)}(e,r,t,o):function(e,t,n,r){var i=e.display,o=e.doc;st(t);var l,a,s=o.sel,c=s.ranges;if(r.addNew&&!r.extend?(a=o.sel.contains(n),l=a>-1?c[a]:new bi(n,n)):(l=o.sel.primary(),a=o.sel.primIndex),"rectangle"==r.unit)r.addNew||(l=new bi(n,n)),n=ar(e,t,!0,!0),a=-1;else{var u=fl(e,n,r.unit);l=r.extend?Ui(l,u.anchor,u.head,r.extend):u}r.addNew?-1==a?(a=c.length,Yi(o,yi(c.concat([l]),a),{scroll:!1,origin:"*mouse"})):c.length>1&&c[a].empty()&&"char"==r.unit&&!r.extend?(Yi(o,yi(c.slice(0,a).concat(c.slice(a+1)),0),{scroll:!1,origin:"*mouse"}),s=o.sel):Vi(o,a,l,G):(a=0,Yi(o,new vi([l],0),G),s=o.sel);var h=n;function f(t){if(0!=me(h,t))if(h=t,"rectangle"==r.unit){for(var i=[],c=e.options.tabSize,u=R(ae(o,n.line).text,n.ch,c),f=R(ae(o,t.line).text,t.ch,c),d=Math.min(u,f),p=Math.max(u,f),g=Math.min(n.line,t.line),m=Math.min(e.lastLine(),Math.max(n.line,t.line));g<=m;g++){var v=ae(o,g).text,b=$(v,d,c);d==p?i.push(new bi(ge(g,b),ge(g,b))):v.length>b&&i.push(new bi(ge(g,b),ge(g,$(v,p,c))))}i.length||i.push(new bi(n,n)),Yi(o,yi(s.ranges.slice(0,a).concat(i),a),{origin:"*mouse",scroll:!1}),e.scrollIntoView(t)}else{var y,x=l,w=fl(e,t,r.unit),C=x.anchor;me(w.anchor,C)>0?(y=w.head,C=xe(x.from(),w.anchor)):(y=w.anchor,C=ye(x.to(),w.head));var k=s.ranges.slice(0);k[a]=function(e,t){var n=t.anchor,r=t.head,i=ae(e.doc,n.line);if(0==me(n,r)&&n.sticky==r.sticky)return t;var o=Qe(i);if(!o)return t;var l=qe(o,n.ch,n.sticky),a=o[l];if(a.from!=n.ch&&a.to!=n.ch)return t;var s,c=l+(a.from==n.ch==(1!=a.level)?0:1);if(0==c||c==o.length)return t;if(r.line!=n.line)s=(r.line-n.line)*("ltr"==e.doc.direction?1:-1)>0;else{var u=qe(o,r.ch,r.sticky),h=u-l||(r.ch-n.ch)*(1==a.level?-1:1);s=u==c-1||u==c?h<0:h>0}var f=o[c+(s?-1:0)],d=s==(1==f.level),p=d?f.from:f.to,g=d?"after":"before";return n.ch==p&&n.sticky==g?t:new bi(new ge(n.line,p,g),r)}(e,new bi(Ce(o,C),y)),Yi(o,yi(k,a),G)}}var d=i.wrapper.getBoundingClientRect(),p=0;function g(t){e.state.selectingText=!1,p=1/0,st(t),i.input.focus(),nt(i.wrapper.ownerDocument,"mousemove",m),nt(i.wrapper.ownerDocument,"mouseup",v),o.history.lastSelOrigin=null}var m=Yr(e,function(t){0!==t.buttons&&dt(t)?function t(n){var l=++p,a=ar(e,n,!0,"rectangle"==r.unit);if(a)if(0!=me(a,h)){e.curOp.focus=D(),f(a);var s=wr(i,o);(a.line>=s.to||a.line<s.from)&&setTimeout(Yr(e,function(){p==l&&t(n)}),150)}else{var c=n.clientY<d.top?-20:n.clientY>d.bottom?20:0;c&&setTimeout(Yr(e,function(){p==l&&(i.scroller.scrollTop+=c,t(n))}),50)}}(t):g(t)}),v=Yr(e,g);e.state.selectingText=v,et(i.wrapper.ownerDocument,"mousemove",m),et(i.wrapper.ownerDocument,"mouseup",v)}(e,r,t,o)}(t,r,o,e):ft(e)==n.scroller&&st(e):2==i?(r&&ji(t.doc,r),setTimeout(function(){return n.input.focus()},20)):3==i&&(k?gl(t,e):mr(t)))}}function fl(e,t,n){if("char"==n)return new bi(t,t);if("word"==n)return e.findWordAt(t);if("line"==n)return new bi(ge(t.line,0),Ce(e.doc,ge(t.line+1,0)));var r=n(e,t);return new bi(r.from,r.to)}function dl(e,t,n,r){var i,o;if(t.touches)i=t.touches[0].clientX,o=t.touches[0].clientY;else try{i=t.clientX,o=t.clientY}catch(t){return!1}if(i>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;r&&st(t);var l=e.display,a=l.lineDiv.getBoundingClientRect();if(o>a.bottom||!lt(e,n))return ut(t);o-=a.top-l.viewOffset;for(var s=0;s<e.options.gutters.length;++s){var c=l.gutters.childNodes[s];if(c&&c.getBoundingClientRect().right>=i){var u=fe(e.doc,o),h=e.options.gutters[s];return rt(e,n,e,u,h,t),ut(t)}}}function pl(e,t){return dl(e,t,"gutterClick",!0)}function gl(e,t){Cn(e.display,t)||function(e,t){return!!lt(e,"gutterContextMenu")&&dl(e,t,"gutterContextMenu",!1)}(e,t)||it(e,t,"contextmenu")||e.display.input.onContextMenu(t)}function ml(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),zn(e)}ul.prototype.compare=function(e,t,n){return this.time+400>e&&0==me(t,this.pos)&&n==this.button};var vl={toString:function(){return"CodeMirror.Init"}},bl={},yl={};function xl(e){ui(e),Zr(e),Cr(e)}function wl(e,t,n){var r=n&&n!=vl;if(!t!=!r){var i=e.display.dragFunctions,o=t?et:nt;o(e.display.scroller,"dragstart",i.start),o(e.display.scroller,"dragenter",i.enter),o(e.display.scroller,"dragover",i.over),o(e.display.scroller,"dragleave",i.leave),o(e.display.scroller,"drop",i.drop)}}function Cl(e){e.options.lineWrapping?(F(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(L(e.display.wrapper,"CodeMirror-wrap"),Ye(e)),lr(e),Zr(e),zn(e),setTimeout(function(){return Pr(e)},100)}function kl(e,t){var r=this;if(!(this instanceof kl))return new kl(e,t);this.options=t=t?_(t):{},_(bl,t,!1),hi(t);var i=t.value;"string"==typeof i&&(i=new Lo(i,t.mode,null,t.lineSeparator,t.direction)),this.doc=i;var o=new kl.inputStyles[t.inputStyle](this),c=this.display=new function(e,t,r){var i=this;this.input=r,i.scrollbarFiller=N("div",null,"CodeMirror-scrollbar-filler"),i.scrollbarFiller.setAttribute("cm-not-content","true"),i.gutterFiller=N("div",null,"CodeMirror-gutter-filler"),i.gutterFiller.setAttribute("cm-not-content","true"),i.lineDiv=A("div",null,"CodeMirror-code"),i.selectionDiv=N("div",null,null,"position: relative; z-index: 1"),i.cursorDiv=N("div",null,"CodeMirror-cursors"),i.measure=N("div",null,"CodeMirror-measure"),i.lineMeasure=N("div",null,"CodeMirror-measure"),i.lineSpace=A("div",[i.measure,i.lineMeasure,i.selectionDiv,i.cursorDiv,i.lineDiv],null,"position: relative; outline: none");var o=A("div",[i.lineSpace],"CodeMirror-lines");i.mover=N("div",[o],null,"position: relative"),i.sizer=N("div",[i.mover],"CodeMirror-sizer"),i.sizerWidth=null,i.heightForcer=N("div",null,null,"position: absolute; height: "+B+"px; width: 1px;"),i.gutters=N("div",null,"CodeMirror-gutters"),i.lineGutter=null,i.scroller=N("div",[i.sizer,i.heightForcer,i.gutters],"CodeMirror-scroll"),i.scroller.setAttribute("tabIndex","-1"),i.wrapper=N("div",[i.scrollbarFiller,i.gutterFiller,i.scroller],"CodeMirror"),l&&a<8&&(i.gutters.style.zIndex=-1,i.scroller.style.paddingRight=0),s||n&&v||(i.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(i.wrapper):e(i.wrapper)),i.viewFrom=i.viewTo=t.first,i.reportedViewFrom=i.reportedViewTo=t.first,i.view=[],i.renderedView=null,i.externalMeasured=null,i.viewOffset=0,i.lastWrapHeight=i.lastWrapWidth=0,i.updateLineNumbers=null,i.nativeBarWidth=i.barHeight=i.barWidth=0,i.scrollbarsClipped=!1,i.lineNumWidth=i.lineNumInnerWidth=i.lineNumChars=null,i.alignWidgets=!1,i.cachedCharWidth=i.cachedTextHeight=i.cachedPaddingH=null,i.maxLine=null,i.maxLineLength=0,i.maxLineChanged=!1,i.wheelDX=i.wheelDY=i.wheelStartX=i.wheelStartY=null,i.shift=!1,i.selForContextMenu=null,i.activeTouch=null,r.init(i)}(e,i,o);for(var u in c.wrapper.CodeMirror=this,ui(this),ml(this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),Hr(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:!1,cutIncoming:!1,selectingText:!1,draggingText:!1,highlight:new H,keySeq:null,specialChars:null},t.autofocus&&!v&&c.input.focus(),l&&a<11&&setTimeout(function(){return r.display.input.reset(!0)},20),function(e){var t=e.display;et(t.scroller,"mousedown",Yr(e,hl)),et(t.scroller,"dblclick",l&&a<11?Yr(e,function(t){if(!it(e,t)){var n=ar(e,t);if(n&&!pl(e,t)&&!Cn(e.display,t)){st(t);var r=e.findWordAt(n);ji(e.doc,r.anchor,r.head)}}}):function(t){return it(e,t)||st(t)}),k||et(t.scroller,"contextmenu",function(t){return gl(e,t)});var n,r={end:0};function i(){t.activeTouch&&(n=setTimeout(function(){return t.activeTouch=null},1e3),(r=t.activeTouch).end=+new Date)}function o(e,t){if(null==t.left)return!0;var n=t.left-e.left,r=t.top-e.top;return n*n+r*r>400}et(t.scroller,"touchstart",function(i){if(!it(e,i)&&!function(e){if(1!=e.touches.length)return!1;var t=e.touches[0];return t.radiusX<=1&&t.radiusY<=1}(i)&&!pl(e,i)){t.input.ensurePolled(),clearTimeout(n);var o=+new Date;t.activeTouch={start:o,moved:!1,prev:o-r.end<=300?r:null},1==i.touches.length&&(t.activeTouch.left=i.touches[0].pageX,t.activeTouch.top=i.touches[0].pageY)}}),et(t.scroller,"touchmove",function(){t.activeTouch&&(t.activeTouch.moved=!0)}),et(t.scroller,"touchend",function(n){var r=t.activeTouch;if(r&&!Cn(t,n)&&null!=r.left&&!r.moved&&new Date-r.start<300){var l,a=e.coordsChar(t.activeTouch,"page");l=!r.prev||o(r,r.prev)?new bi(a,a):!r.prev.prev||o(r,r.prev.prev)?e.findWordAt(a):new bi(ge(a.line,0),Ce(e.doc,ge(a.line+1,0))),e.setSelection(l.anchor,l.head),e.focus(),st(n)}i()}),et(t.scroller,"touchcancel",i),et(t.scroller,"scroll",function(){t.scroller.clientHeight&&(Ar(e,t.scroller.scrollTop),Dr(e,t.scroller.scrollLeft,!0),rt(e,"scroll",e))}),et(t.scroller,"mousewheel",function(t){return mi(e,t)}),et(t.scroller,"DOMMouseScroll",function(t){return mi(e,t)}),et(t.wrapper,"scroll",function(){return t.wrapper.scrollTop=t.wrapper.scrollLeft=0}),t.dragFunctions={enter:function(t){it(e,t)||ht(t)},over:function(t){it(e,t)||(function(e,t){var n=ar(e,t);if(n){var r=document.createDocumentFragment();hr(e,n,r),e.display.dragCursor||(e.display.dragCursor=N("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),e.display.lineSpace.insertBefore(e.display.dragCursor,e.display.cursorDiv)),T(e.display.dragCursor,r)}}(e,t),ht(t))},start:function(t){return function(e,t){if(l&&(!e.state.draggingText||+new Date-Eo<100))ht(t);else if(!it(e,t)&&!Cn(e.display,t)&&(t.dataTransfer.setData("Text",e.getSelection()),t.dataTransfer.effectAllowed="copyMove",t.dataTransfer.setDragImage&&!f)){var n=N("img",null,null,"position: fixed; left: 0; top: 0;");n.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",h&&(n.width=n.height=1,e.display.wrapper.appendChild(n),n._top=n.offsetTop),t.dataTransfer.setDragImage(n,0,0),h&&n.parentNode.removeChild(n)}}(e,t)},drop:Yr(e,To),leave:function(t){it(e,t)||No(e)}};var s=t.input.getField();et(s,"keyup",function(t){return ll.call(e,t)}),et(s,"keydown",Yr(e,ol)),et(s,"keypress",Yr(e,al)),et(s,"focus",function(t){return vr(e,t)}),et(s,"blur",function(t){return br(e,t)})}(this),Do(),Br(this),this.curOp.forceUpdate=!0,Ai(this,i),t.autofocus&&!v||this.hasFocus()?setTimeout(P(vr,this),20):br(this),yl)yl.hasOwnProperty(u)&&yl[u](r,t[u],vl);kr(this),t.finishInit&&t.finishInit(this);for(var d=0;d<Ml.length;++d)Ml[d](r);Ur(this),s&&t.lineWrapping&&"optimizelegibility"==getComputedStyle(c.lineDiv).textRendering&&(c.lineDiv.style.textRendering="auto")}kl.defaults=bl,kl.optionHandlers=yl;var Ml=[];function Sl(e,t,n,r){var i,o=e.doc;null==n&&(n="add"),"smart"==n&&(o.mode.indent?i=Rt(e,t).state:n="prev");var l=e.options.tabSize,a=ae(o,t),s=R(a.text,null,l);a.stateAfter&&(a.stateAfter=null);var c,u=a.text.match(/^\s*/)[0];if(r||/\S/.test(a.text)){if("smart"==n&&((c=o.mode.indent(i,a.text.slice(u.length),a.text))==U||c>150)){if(!r)return;n="prev"}}else c=0,n="not";"prev"==n?c=t>o.first?R(ae(o,t-1).text,null,l):0:"add"==n?c=s+e.options.indentUnit:"subtract"==n?c=s-e.options.indentUnit:"number"==typeof n&&(c=s+n),c=Math.max(0,c);var h="",f=0;if(e.options.indentWithTabs)for(var d=Math.floor(c/l);d;--d)f+=l,h+="\t";if(f<c&&(h+=Y(c-f)),h!=u)return co(o,h,ge(t,0),ge(t,u.length),"+input"),a.stateAfter=null,!0;for(var p=0;p<o.sel.ranges.length;p++){var g=o.sel.ranges[p];if(g.head.line==t&&g.head.ch<u.length){var m=ge(t,u.length);Vi(o,p,new bi(m,m));break}}}kl.defineInitHook=function(e){return Ml.push(e)};var Ll=null;function El(e){Ll=e}function Tl(e,t,n,r,i){var o=e.doc;e.display.shift=!1,r||(r=o.sel);var l,a=e.state.pasteIncoming||"paste"==i,s=xt(t),c=null;if(a&&r.ranges.length>1)if(Ll&&Ll.text.join("\n")==t){if(r.ranges.length%Ll.text.length==0){c=[];for(var u=0;u<Ll.text.length;u++)c.push(o.splitLines(Ll.text[u]))}}else s.length==r.ranges.length&&e.options.pasteLinesPerSelection&&(c=q(s,function(e){return[e]}));for(var h=r.ranges.length-1;h>=0;h--){var f=r.ranges[h],d=f.from(),p=f.to();f.empty()&&(n&&n>0?d=ge(d.line,d.ch-n):e.state.overwrite&&!a?p=ge(p.line,Math.min(ae(o,p.line).text.length,p.ch+X(s).length)):Ll&&Ll.lineWise&&Ll.text.join("\n")==t&&(d=p=ge(d.line,0))),l=e.curOp.updateInput;var g={from:d,to:p,text:c?c[h%c.length]:s,origin:i||(a?"paste":e.state.cutIncoming?"cut":"+input")};io(e.doc,g),sn(e,"inputRead",e,g)}t&&!a&&Al(e,t),Lr(e),e.curOp.updateInput=l,e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=!1}function Nl(e,t){var n=e.clipboardData&&e.clipboardData.getData("Text");if(n)return e.preventDefault(),t.isReadOnly()||t.options.disableInput||Kr(t,function(){return Tl(t,n,0,null,"paste")}),!0}function Al(e,t){if(e.options.electricChars&&e.options.smartIndent)for(var n=e.doc.sel,r=n.ranges.length-1;r>=0;r--){var i=n.ranges[r];if(!(i.head.ch>100||r&&n.ranges[r-1].head.line==i.head.line)){var o=e.getModeAt(i.head),l=!1;if(o.electricChars){for(var a=0;a<o.electricChars.length;a++)if(t.indexOf(o.electricChars.charAt(a))>-1){l=Sl(e,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(ae(e.doc,i.head.line).text.slice(0,i.head.ch))&&(l=Sl(e,i.head.line,"smart"));l&&sn(e,"electricInput",e,i.head.line)}}}function Ol(e){for(var t=[],n=[],r=0;r<e.doc.sel.ranges.length;r++){var i=e.doc.sel.ranges[r].head.line,o={anchor:ge(i,0),head:ge(i+1,0)};n.push(o),t.push(e.getRange(o.anchor,o.head))}return{text:t,ranges:n}}function Dl(e,t){e.setAttribute("autocorrect","off"),e.setAttribute("autocapitalize","off"),e.setAttribute("spellcheck",!!t)}function Fl(){var e=N("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; outline: none"),t=N("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return s?e.style.width="1000px":e.setAttribute("wrap","off"),g&&(e.style.border="1px solid black"),Dl(e),t}function Il(e,t,n,r,i){var o=t,l=n,a=ae(e,t.line);function s(r){var o,l;if(null==(o=i?function(e,t,n,r){var i=Qe(t,e.doc.direction);if(!i)return Yo(t,n,r);n.ch>=t.text.length?(n.ch=t.text.length,n.sticky="before"):n.ch<=0&&(n.ch=0,n.sticky="after");var o=qe(i,n.ch,n.sticky),l=i[o];if("ltr"==e.doc.direction&&l.level%2==0&&(r>0?l.to>n.ch:l.from<n.ch))return Yo(t,n,r);var a,s=function(e,n){return Ko(t,e instanceof ge?e.ch:e,n)},c=function(n){return e.options.lineWrapping?(a=a||Dn(e,t),Qn(e,t,a,n)):{begin:0,end:t.text.length}},u=c("before"==n.sticky?s(n,-1):n.ch);if("rtl"==e.doc.direction||1==l.level){var h=1==l.level==r<0,f=s(n,h?1:-1);if(null!=f&&(h?f<=l.to&&f<=u.end:f>=l.from&&f>=u.begin)){var d=h?"before":"after";return new ge(n.line,f,d)}}var p=function(e,t,r){for(var o=function(e,t){return t?new ge(n.line,s(e,1),"before"):new ge(n.line,e,"after")};e>=0&&e<i.length;e+=t){var l=i[e],a=t>0==(1!=l.level),c=a?r.begin:s(r.end,-1);if(l.from<=c&&c<l.to)return o(c,a);if(c=a?l.from:s(l.to,-1),r.begin<=c&&c<r.end)return o(c,a)}},g=p(o+r,r,u);if(g)return g;var m=r>0?u.end:s(u.begin,-1);return null==m||r>0&&m==t.text.length||!(g=p(r>0?0:i.length-1,r,c(m)))?null:g}(e.cm,a,t,n):Yo(a,t,n))){if(r||((l=t.line+n)<e.first||l>=e.first+e.size||(t=new ge(l,t.ch,t.sticky),!(a=ae(e,l)))))return!1;t=Xo(i,e.cm,a,t.line,n)}else t=o;return!0}if("char"==r)s();else if("column"==r)s(!0);else if("word"==r||"group"==r)for(var c=null,u="group"==r,h=e.cm&&e.cm.getHelper(t,"wordChars"),f=!0;!(n<0)||s(!f);f=!1){var d=a.text.charAt(t.ch)||"\n",p=te(d,h)?"w":u&&"\n"==d?"n":!u||/\s/.test(d)?null:"p";if(!u||f||p||(p="s"),c&&c!=p){n<0&&(n=1,s(),t.sticky="after");break}if(p&&(c=p),n>0&&!s(!f))break}var g=eo(e,t,o,l,!0);return ve(o,g)&&(g.hitSide=!0),g}function Wl(e,t,n,r){var i,o,l=e.doc,a=t.left;if("page"==r){var s=Math.min(e.display.wrapper.clientHeight,window.innerHeight||document.documentElement.clientHeight),c=Math.max(s-.5*tr(e.display),3);i=(n>0?t.bottom:t.top)+n*c}else"line"==r&&(i=n>0?t.bottom+3:t.top-3);for(;(o=qn(e,a,i)).outside;){if(n<0?i<=0:i>=l.height){o.hitSide=!0;break}i+=5*n}return o}var Pl=function(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new H,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};function _l(e,t){var n=On(e,t.line);if(!n||n.hidden)return null;var r=ae(e.doc,t.line),i=Nn(n,r,t.line),o=Qe(r,e.doc.direction),l="left";if(o){var a=qe(o,t.ch);l=a%2?"right":"left"}var s=Pn(i.map,t.ch,l);return s.offset="right"==s.collapse?s.end:s.start,s}function Rl(e,t){return t&&(e.bad=!0),e}function Hl(e,t,n){var r;if(t==e.display.lineDiv){if(!(r=e.display.lineDiv.childNodes[n]))return Rl(e.clipPos(ge(e.display.viewTo-1)),!0);t=null,n=0}else for(r=t;;r=r.parentNode){if(!r||r==e.display.lineDiv)return null;if(r.parentNode&&r.parentNode==e.display.lineDiv)break}for(var i=0;i<e.display.view.length;i++){var o=e.display.view[i];if(o.node==r)return zl(o,t,n)}}function zl(e,t,n){var r=e.text.firstChild,i=!1;if(!t||!O(r,t))return Rl(ge(he(e.line),0),!0);if(t==r&&(i=!0,t=r.childNodes[n],n=0,!t)){var o=e.rest?X(e.rest):e.line;return Rl(ge(he(o),o.text.length),i)}var l=3==t.nodeType?t:null,a=t;for(l||1!=t.childNodes.length||3!=t.firstChild.nodeType||(l=t.firstChild,n&&(n=l.nodeValue.length));a.parentNode!=r;)a=a.parentNode;var s=e.measure,c=s.maps;function u(t,n,r){for(var i=-1;i<(c?c.length:0);i++)for(var o=i<0?s.map:c[i],l=0;l<o.length;l+=3){var a=o[l+2];if(a==t||a==n){var u=he(i<0?e.line:e.rest[i]),h=o[l]+r;return(r<0||a!=t)&&(h=o[l+(r?1:0)]),ge(u,h)}}}var h=u(l,a,n);if(h)return Rl(h,i);for(var f=a.nextSibling,d=l?l.nodeValue.length-n:0;f;f=f.nextSibling){if(h=u(f,f.firstChild,0))return Rl(ge(h.line,h.ch-d),i);d+=f.textContent.length}for(var p=a.previousSibling,g=n;p;p=p.previousSibling){if(h=u(p,p.firstChild,-1))return Rl(ge(h.line,h.ch+g),i);g+=p.textContent.length}}Pl.prototype.init=function(e){var t=this,n=this,r=n.cm,i=n.div=e.lineDiv;function o(e){if(!it(r,e)){if(r.somethingSelected())El({lineWise:!1,text:r.getSelections()}),"cut"==e.type&&r.replaceSelection("",null,"cut");else{if(!r.options.lineWiseCopyCut)return;var t=Ol(r);El({lineWise:!0,text:t.text}),"cut"==e.type&&r.operation(function(){r.setSelections(t.ranges,0,j),r.replaceSelection("",null,"cut")})}if(e.clipboardData){e.clipboardData.clearData();var o=Ll.text.join("\n");if(e.clipboardData.setData("Text",o),e.clipboardData.getData("Text")==o)return void e.preventDefault()}var l=Fl(),a=l.firstChild;r.display.lineSpace.insertBefore(l,r.display.lineSpace.firstChild),a.value=Ll.text.join("\n");var s=document.activeElement;W(a),setTimeout(function(){r.display.lineSpace.removeChild(l),s.focus(),s==i&&n.showPrimarySelection()},50)}}Dl(i,r.options.spellcheck),et(i,"paste",function(e){it(r,e)||Nl(e,r)||a<=11&&setTimeout(Yr(r,function(){return t.updateFromDOM()}),20)}),et(i,"compositionstart",function(e){t.composing={data:e.data,done:!1}}),et(i,"compositionupdate",function(e){t.composing||(t.composing={data:e.data,done:!1})}),et(i,"compositionend",function(e){t.composing&&(e.data!=t.composing.data&&t.readFromDOMSoon(),t.composing.done=!0)}),et(i,"touchstart",function(){return n.forceCompositionEnd()}),et(i,"input",function(){t.composing||t.readFromDOMSoon()}),et(i,"copy",o),et(i,"cut",o)},Pl.prototype.prepareSelection=function(){var e=ur(this.cm,!1);return e.focus=this.cm.state.focused,e},Pl.prototype.showSelection=function(e,t){e&&this.cm.display.view.length&&((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},Pl.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},Pl.prototype.showPrimarySelection=function(){var e=this.getSelection(),t=this.cm,r=t.doc.sel.primary(),i=r.from(),o=r.to();if(t.display.viewTo==t.display.viewFrom||i.line>=t.display.viewTo||o.line<t.display.viewFrom)e.removeAllRanges();else{var l=Hl(t,e.anchorNode,e.anchorOffset),a=Hl(t,e.focusNode,e.focusOffset);if(!l||l.bad||!a||a.bad||0!=me(xe(l,a),i)||0!=me(ye(l,a),o)){var s=t.display.view,c=i.line>=t.display.viewFrom&&_l(t,i)||{node:s[0].measure.map[2],offset:0},u=o.line<t.display.viewTo&&_l(t,o);if(!u){var h=s[s.length-1].measure,f=h.maps?h.maps[h.maps.length-1]:h.map;u={node:f[f.length-1],offset:f[f.length-2]-f[f.length-3]}}if(c&&u){var d,p=e.rangeCount&&e.getRangeAt(0);try{d=S(c.node,c.offset,u.offset,u.node)}catch(e){}d&&(!n&&t.state.focused?(e.collapse(c.node,c.offset),d.collapsed||(e.removeAllRanges(),e.addRange(d))):(e.removeAllRanges(),e.addRange(d)),p&&null==e.anchorNode?e.addRange(p):n&&this.startGracePeriod()),this.rememberSelection()}else e.removeAllRanges()}}},Pl.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout(function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation(function(){return e.cm.curOp.selectionChanged=!0})},20)},Pl.prototype.showMultipleSelections=function(e){T(this.cm.display.cursorDiv,e.cursors),T(this.cm.display.selectionDiv,e.selection)},Pl.prototype.rememberSelection=function(){var e=this.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},Pl.prototype.selectionInEditor=function(){var e=this.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return O(this.div,t)},Pl.prototype.focus=function(){"nocursor"!=this.cm.options.readOnly&&(this.selectionInEditor()||this.showSelection(this.prepareSelection(),!0),this.div.focus())},Pl.prototype.blur=function(){this.div.blur()},Pl.prototype.getField=function(){return this.div},Pl.prototype.supportsTouch=function(){return!0},Pl.prototype.receivedFocus=function(){var e=this;this.selectionInEditor()?this.pollSelection():Kr(this.cm,function(){return e.cm.curOp.selectionChanged=!0}),this.polling.set(this.cm.options.pollInterval,function t(){e.cm.state.focused&&(e.pollSelection(),e.polling.set(e.cm.options.pollInterval,t))})},Pl.prototype.selectionChanged=function(){var e=this.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},Pl.prototype.pollSelection=function(){if(null==this.readDOMTimeout&&!this.gracePeriod&&this.selectionChanged()){var e=this.getSelection(),t=this.cm;if(m&&u&&this.cm.options.gutters.length&&function(e){for(var t=e;t;t=t.parentNode)if(/CodeMirror-gutter-wrapper/.test(t.className))return!0;return!1}(e.anchorNode))return this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),void this.focus();if(!this.composing){this.rememberSelection();var n=Hl(t,e.anchorNode,e.anchorOffset),r=Hl(t,e.focusNode,e.focusOffset);n&&r&&Kr(t,function(){Yi(t.doc,xi(n,r),j),(n.bad||r.bad)&&(t.curOp.selectionChanged=!0)})}}},Pl.prototype.pollContent=function(){null!=this.readDOMTimeout&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e,t,n,r=this.cm,i=r.display,o=r.doc.sel.primary(),l=o.from(),a=o.to();if(0==l.ch&&l.line>r.firstLine()&&(l=ge(l.line-1,ae(r.doc,l.line-1).length)),a.ch==ae(r.doc,a.line).text.length&&a.line<r.lastLine()&&(a=ge(a.line+1,0)),l.line<i.viewFrom||a.line>i.viewTo-1)return!1;l.line==i.viewFrom||0==(e=sr(r,l.line))?(t=he(i.view[0].line),n=i.view[0].node):(t=he(i.view[e].line),n=i.view[e-1].node.nextSibling);var s,c,u=sr(r,a.line);if(u==i.view.length-1?(s=i.viewTo-1,c=i.lineDiv.lastChild):(s=he(i.view[u+1].line)-1,c=i.view[u+1].node.previousSibling),!n)return!1;for(var h=r.doc.splitLines(function(e,t,n,r,i){var o="",l=!1,a=e.doc.lineSeparator(),s=!1;function c(){l&&(o+=a,s&&(o+=a),l=s=!1)}function u(e){e&&(c(),o+=e)}function h(t){if(1==t.nodeType){var n=t.getAttribute("cm-text");if(n)return void u(n);var o,f=t.getAttribute("cm-marker");if(f){var d=e.findMarks(ge(r,0),ge(i+1,0),(m=+f,function(e){return e.id==m}));return void(d.length&&(o=d[0].find(0))&&u(se(e.doc,o.from,o.to).join(a)))}if("false"==t.getAttribute("contenteditable"))return;var p=/^(pre|div|p|li|table|br)$/i.test(t.nodeName);if(!/^br$/i.test(t.nodeName)&&0==t.textContent.length)return;p&&c();for(var g=0;g<t.childNodes.length;g++)h(t.childNodes[g]);/^(pre|p)$/i.test(t.nodeName)&&(s=!0),p&&(l=!0)}else 3==t.nodeType&&u(t.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "));var m}for(;h(t),t!=n;)t=t.nextSibling,s=!1;return o}(r,n,c,t,s)),f=se(r.doc,ge(t,0),ge(s,ae(r.doc,s).text.length));h.length>1&&f.length>1;)if(X(h)==X(f))h.pop(),f.pop(),s--;else{if(h[0]!=f[0])break;h.shift(),f.shift(),t++}for(var d=0,p=0,g=h[0],m=f[0],v=Math.min(g.length,m.length);d<v&&g.charCodeAt(d)==m.charCodeAt(d);)++d;for(var b=X(h),y=X(f),x=Math.min(b.length-(1==h.length?d:0),y.length-(1==f.length?d:0));p<x&&b.charCodeAt(b.length-p-1)==y.charCodeAt(y.length-p-1);)++p;if(1==h.length&&1==f.length&&t==l.line)for(;d&&d>l.ch&&b.charCodeAt(b.length-p-1)==y.charCodeAt(y.length-p-1);)d--,p++;h[h.length-1]=b.slice(0,b.length-p).replace(/^\u200b+/,""),h[0]=h[0].slice(d).replace(/\u200b+$/,"");var w=ge(t,d),C=ge(s,f.length?X(f).length-p:0);return h.length>1||h[0]||me(w,C)?(co(r.doc,h,w,C,"+input"),!0):void 0},Pl.prototype.ensurePolled=function(){this.forceCompositionEnd()},Pl.prototype.reset=function(){this.forceCompositionEnd()},Pl.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},Pl.prototype.readFromDOMSoon=function(){var e=this;null==this.readDOMTimeout&&(this.readDOMTimeout=setTimeout(function(){if(e.readDOMTimeout=null,e.composing){if(!e.composing.done)return;e.composing=null}e.updateFromDOM()},80))},Pl.prototype.updateFromDOM=function(){var e=this;!this.cm.isReadOnly()&&this.pollContent()||Kr(this.cm,function(){return Zr(e.cm)})},Pl.prototype.setUneditable=function(e){e.contentEditable="false"},Pl.prototype.onKeyPress=function(e){0==e.charCode||this.composing||(e.preventDefault(),this.cm.isReadOnly()||Yr(this.cm,Tl)(this.cm,String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),0))},Pl.prototype.readOnlyChanged=function(e){this.div.contentEditable=String("nocursor"!=e)},Pl.prototype.onContextMenu=function(){},Pl.prototype.resetPosition=function(){},Pl.prototype.needsContentAttribute=!0;var Bl=function(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new H,this.hasSelection=!1,this.composing=null};Bl.prototype.init=function(e){var t=this,n=this,r=this.cm;this.createField(e);var i=this.textarea;function o(e){if(!it(r,e)){if(r.somethingSelected())El({lineWise:!1,text:r.getSelections()});else{if(!r.options.lineWiseCopyCut)return;var t=Ol(r);El({lineWise:!0,text:t.text}),"cut"==e.type?r.setSelections(t.ranges,null,j):(n.prevInput="",i.value=t.text.join("\n"),W(i))}"cut"==e.type&&(r.state.cutIncoming=!0)}}e.wrapper.insertBefore(this.wrapper,e.wrapper.firstChild),g&&(i.style.width="0px"),et(i,"input",function(){l&&a>=9&&t.hasSelection&&(t.hasSelection=null),n.poll()}),et(i,"paste",function(e){it(r,e)||Nl(e,r)||(r.state.pasteIncoming=!0,n.fastPoll())}),et(i,"cut",o),et(i,"copy",o),et(e.scroller,"paste",function(t){Cn(e,t)||it(r,t)||(r.state.pasteIncoming=!0,n.focus())}),et(e.lineSpace,"selectstart",function(t){Cn(e,t)||st(t)}),et(i,"compositionstart",function(){var e=r.getCursor("from");n.composing&&n.composing.range.clear(),n.composing={start:e,range:r.markText(e,r.getCursor("to"),{className:"CodeMirror-composing"})}}),et(i,"compositionend",function(){n.composing&&(n.poll(),n.composing.range.clear(),n.composing=null)})},Bl.prototype.createField=function(e){this.wrapper=Fl(),this.textarea=this.wrapper.firstChild},Bl.prototype.prepareSelection=function(){var e=this.cm,t=e.display,n=e.doc,r=ur(e);if(e.options.moveInputWithCursor){var i=Kn(e,n.sel.primary().head,"div"),o=t.wrapper.getBoundingClientRect(),l=t.lineDiv.getBoundingClientRect();r.teTop=Math.max(0,Math.min(t.wrapper.clientHeight-10,i.top+l.top-o.top)),r.teLeft=Math.max(0,Math.min(t.wrapper.clientWidth-10,i.left+l.left-o.left))}return r},Bl.prototype.showSelection=function(e){var t=this.cm,n=t.display;T(n.cursorDiv,e.cursors),T(n.selectionDiv,e.selection),null!=e.teTop&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},Bl.prototype.reset=function(e){if(!this.contextMenuPending&&!this.composing){var t=this.cm;if(t.somethingSelected()){this.prevInput="";var n=t.getSelection();this.textarea.value=n,t.state.focused&&W(this.textarea),l&&a>=9&&(this.hasSelection=n)}else e||(this.prevInput=this.textarea.value="",l&&a>=9&&(this.hasSelection=null))}},Bl.prototype.getField=function(){return this.textarea},Bl.prototype.supportsTouch=function(){return!1},Bl.prototype.focus=function(){if("nocursor"!=this.cm.options.readOnly&&(!v||D()!=this.textarea))try{this.textarea.focus()}catch(e){}},Bl.prototype.blur=function(){this.textarea.blur()},Bl.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},Bl.prototype.receivedFocus=function(){this.slowPoll()},Bl.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,function(){e.poll(),e.cm.state.focused&&e.slowPoll()})},Bl.prototype.fastPoll=function(){var e=!1,t=this;t.pollingFast=!0,t.polling.set(20,function n(){var r=t.poll();r||e?(t.pollingFast=!1,t.slowPoll()):(e=!0,t.polling.set(60,n))})},Bl.prototype.poll=function(){var e=this,t=this.cm,n=this.textarea,r=this.prevInput;if(this.contextMenuPending||!t.state.focused||wt(n)&&!r&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var i=n.value;if(i==r&&!t.somethingSelected())return!1;if(l&&a>=9&&this.hasSelection===i||b&&/[\uf700-\uf7ff]/.test(i))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var o=i.charCodeAt(0);if(8203!=o||r||(r="​"),8666==o)return this.reset(),this.cm.execCommand("undo")}for(var s=0,c=Math.min(r.length,i.length);s<c&&r.charCodeAt(s)==i.charCodeAt(s);)++s;return Kr(t,function(){Tl(t,i.slice(s),r.length-s,null,e.composing?"*compose":null),i.length>1e3||i.indexOf("\n")>-1?n.value=e.prevInput="":e.prevInput=i,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))}),!0},Bl.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},Bl.prototype.onKeyPress=function(){l&&a>=9&&(this.hasSelection=null),this.fastPoll()},Bl.prototype.onContextMenu=function(e){var t=this,n=t.cm,r=n.display,i=t.textarea,o=ar(n,e),c=r.scroller.scrollTop;if(o&&!h){var u=n.options.resetSelectionOnContextMenu;u&&-1==n.doc.sel.contains(o)&&Yr(n,Yi)(n.doc,xi(o),j);var f=i.style.cssText,d=t.wrapper.style.cssText;t.wrapper.style.cssText="position: absolute";var p,g=t.wrapper.getBoundingClientRect();if(i.style.cssText="position: absolute; width: 30px; height: 30px;\n      top: "+(e.clientY-g.top-5)+"px; left: "+(e.clientX-g.left-5)+"px;\n      z-index: 1000; background: "+(l?"rgba(255, 255, 255, .05)":"transparent")+";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",s&&(p=window.scrollY),r.input.focus(),s&&window.scrollTo(null,p),r.input.reset(),n.somethingSelected()||(i.value=t.prevInput=" "),t.contextMenuPending=!0,r.selForContextMenu=n.doc.sel,clearTimeout(r.detectingSelectAll),l&&a>=9&&v(),k){ht(e);var m=function(){nt(window,"mouseup",m),setTimeout(b,20)};et(window,"mouseup",m)}else setTimeout(b,50)}function v(){if(null!=i.selectionStart){var e=n.somethingSelected(),o="​"+(e?i.value:"");i.value="⇚",i.value=o,t.prevInput=e?"":"​",i.selectionStart=1,i.selectionEnd=o.length,r.selForContextMenu=n.doc.sel}}function b(){if(t.contextMenuPending=!1,t.wrapper.style.cssText=d,i.style.cssText=f,l&&a<9&&r.scrollbars.setScrollTop(r.scroller.scrollTop=c),null!=i.selectionStart){(!l||l&&a<9)&&v();var e=0,o=function(){r.selForContextMenu==n.doc.sel&&0==i.selectionStart&&i.selectionEnd>0&&"​"==t.prevInput?Yr(n,no)(n):e++<10?r.detectingSelectAll=setTimeout(o,500):(r.selForContextMenu=null,r.input.reset())};r.detectingSelectAll=setTimeout(o,200)}}},Bl.prototype.readOnlyChanged=function(e){e||this.reset(),this.textarea.disabled="nocursor"==e},Bl.prototype.setUneditable=function(){},Bl.prototype.needsContentAttribute=!1,function(e){var t=e.optionHandlers;function n(n,r,i,o){e.defaults[n]=r,i&&(t[n]=o?function(e,t,n){n!=vl&&i(e,t,n)}:i)}e.defineOption=n,e.Init=vl,n("value","",function(e,t){return e.setValue(t)},!0),n("mode",null,function(e,t){e.doc.modeOption=t,Si(e)},!0),n("indentUnit",2,Si,!0),n("indentWithTabs",!1),n("smartIndent",!0),n("tabSize",4,function(e){Li(e),zn(e),Zr(e)},!0),n("lineSeparator",null,function(e,t){if(e.doc.lineSep=t,t){var n=[],r=e.doc.first;e.doc.iter(function(e){for(var i=0;;){var o=e.text.indexOf(t,i);if(-1==o)break;i=o+t.length,n.push(ge(r,o))}r++});for(var i=n.length-1;i>=0;i--)co(e.doc,t,n[i],ge(n[i].line,n[i].ch+t.length))}}),n("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b-\u200f\u2028\u2029\ufeff]/g,function(e,t,n){e.state.specialChars=new RegExp(t.source+(t.test("\t")?"":"|\t"),"g"),n!=vl&&e.refresh()}),n("specialCharPlaceholder",Qt,function(e){return e.refresh()},!0),n("electricChars",!0),n("inputStyle",v?"contenteditable":"textarea",function(){throw new Error("inputStyle can not (yet) be changed in a running editor")},!0),n("spellcheck",!1,function(e,t){return e.getInputField().spellcheck=t},!0),n("rtlMoveVisually",!x),n("wholeLineUpdateBefore",!0),n("theme","default",function(e){ml(e),xl(e)},!0),n("keyMap","default",function(e,t,n){var r=Vo(t),i=n!=vl&&Vo(n);i&&i.detach&&i.detach(e,r),r.attach&&r.attach(e,i||null)}),n("extraKeys",null),n("configureMouse",null),n("lineWrapping",!1,Cl,!0),n("gutters",[],function(e){hi(e.options),xl(e)},!0),n("fixedGutter",!0,function(e,t){e.display.gutters.style.left=t?ir(e.display)+"px":"0",e.refresh()},!0),n("coverGutterNextToScrollbar",!1,function(e){return Pr(e)},!0),n("scrollbarStyle","native",function(e){Hr(e),Pr(e),e.display.scrollbars.setScrollTop(e.doc.scrollTop),e.display.scrollbars.setScrollLeft(e.doc.scrollLeft)},!0),n("lineNumbers",!1,function(e){hi(e.options),xl(e)},!0),n("firstLineNumber",1,xl,!0),n("lineNumberFormatter",function(e){return e},xl,!0),n("showCursorWhenSelecting",!1,cr,!0),n("resetSelectionOnContextMenu",!0),n("lineWiseCopyCut",!0),n("pasteLinesPerSelection",!0),n("readOnly",!1,function(e,t){"nocursor"==t&&(br(e),e.display.input.blur()),e.display.input.readOnlyChanged(t)}),n("disableInput",!1,function(e,t){t||e.display.input.reset()},!0),n("dragDrop",!0,wl),n("allowDropFileTypes",null),n("cursorBlinkRate",530),n("cursorScrollMargin",0),n("cursorHeight",1,cr,!0),n("singleCursorHeightPerLine",!0,cr,!0),n("workTime",100),n("workDelay",100),n("flattenSpans",!0,Li,!0),n("addModeClass",!1,Li,!0),n("pollInterval",100),n("undoDepth",200,function(e,t){return e.doc.history.undoDepth=t}),n("historyEventDelay",1250),n("viewportMargin",10,function(e){return e.refresh()},!0),n("maxHighlightLength",1e4,Li,!0),n("moveInputWithCursor",!0,function(e,t){t||e.display.input.resetPosition()}),n("tabindex",null,function(e,t){return e.display.input.getField().tabIndex=t||""}),n("autofocus",null),n("direction","ltr",function(e,t){return e.doc.setDirection(t)},!0)}(kl),function(e){var t=e.optionHandlers,n=e.helpers={};e.prototype={constructor:e,focus:function(){window.focus(),this.display.input.focus()},setOption:function(e,n){var r=this.options,i=r[e];r[e]==n&&"mode"!=e||(r[e]=n,t.hasOwnProperty(e)&&Yr(this,t[e])(this,n,i),rt(this,"optionChange",this,e))},getOption:function(e){return this.options[e]},getDoc:function(){return this.doc},addKeyMap:function(e,t){this.state.keyMaps[t?"push":"unshift"](Vo(e))},removeKeyMap:function(e){for(var t=this.state.keyMaps,n=0;n<t.length;++n)if(t[n]==e||t[n].name==e)return t.splice(n,1),!0},addOverlay:Xr(function(t,n){var r=t.token?t:e.getMode(this.options,t);if(r.startState)throw new Error("Overlays may not be stateful.");!function(e,t,n){for(var r=0,i=n(t);r<e.length&&n(e[r])<=i;)r++;e.splice(r,0,t)}(this.state.overlays,{mode:r,modeSpec:t,opaque:n&&n.opaque,priority:n&&n.priority||0},function(e){return e.priority}),this.state.modeGen++,Zr(this)}),removeOverlay:Xr(function(e){for(var t=this.state.overlays,n=0;n<t.length;++n){var r=t[n].modeSpec;if(r==e||"string"==typeof e&&r.name==e)return t.splice(n,1),this.state.modeGen++,void Zr(this)}}),indentLine:Xr(function(e,t,n){"string"!=typeof t&&"number"!=typeof t&&(t=null==t?this.options.smartIndent?"smart":"prev":t?"add":"subtract"),de(this.doc,e)&&Sl(this,e,t,n)}),indentSelection:Xr(function(e){for(var t=this.doc.sel.ranges,n=-1,r=0;r<t.length;r++){var i=t[r];if(i.empty())i.head.line>n&&(Sl(this,i.head.line,e,!0),n=i.head.line,r==this.doc.sel.primIndex&&Lr(this));else{var o=i.from(),l=i.to(),a=Math.max(n,o.line);n=Math.min(this.lastLine(),l.line-(l.ch?0:1))+1;for(var s=a;s<n;++s)Sl(this,s,e);var c=this.doc.sel.ranges;0==o.ch&&t.length==c.length&&c[r].from().ch>0&&Vi(this.doc,r,new bi(o,c[r].to()),j)}}}),getTokenAt:function(e,t){return jt(this,e,t)},getLineTokens:function(e,t){return jt(this,ge(e),t,!0)},getTokenTypeAt:function(e){e=Ce(this.doc,e);var t,n=_t(this,ae(this.doc,e.line)),r=0,i=(n.length-1)/2,o=e.ch;if(0==o)t=n[2];else for(;;){var l=r+i>>1;if((l?n[2*l-1]:0)>=o)i=l;else{if(!(n[2*l+1]<o)){t=n[2*l+2];break}r=l+1}}var a=t?t.indexOf("overlay "):-1;return a<0?t:0==a?null:t.slice(0,a-1)},getModeAt:function(t){var n=this.doc.mode;return n.innerMode?e.innerMode(n,this.getTokenAt(t).state).mode:n},getHelper:function(e,t){return this.getHelpers(e,t)[0]},getHelpers:function(e,t){var r=[];if(!n.hasOwnProperty(t))return r;var i=n[t],o=this.getModeAt(e);if("string"==typeof o[t])i[o[t]]&&r.push(i[o[t]]);else if(o[t])for(var l=0;l<o[t].length;l++){var a=i[o[t][l]];a&&r.push(a)}else o.helperType&&i[o.helperType]?r.push(i[o.helperType]):i[o.name]&&r.push(i[o.name]);for(var s=0;s<i._global.length;s++){var c=i._global[s];c.pred(o,this)&&-1==z(r,c.val)&&r.push(c.val)}return r},getStateAfter:function(e,t){var n=this.doc;return Rt(this,(e=we(n,null==e?n.first+n.size-1:e))+1,t).state},cursorCoords:function(e,t){var n=this.doc.sel.primary();return Kn(this,null==e?n.head:"object"==typeof e?Ce(this.doc,e):e?n.from():n.to(),t||"page")},charCoords:function(e,t){return $n(this,Ce(this.doc,e),t||"page")},coordsChar:function(e,t){return qn(this,(e=Vn(this,e,t||"page")).left,e.top)},lineAtHeight:function(e,t){return e=Vn(this,{top:e,left:0},t||"page").top,fe(this.doc,e+this.display.viewOffset)},heightAtLine:function(e,t,n){var r,i=!1;if("number"==typeof e){var o=this.doc.first+this.doc.size-1;e<this.doc.first?e=this.doc.first:e>o&&(e=o,i=!0),r=ae(this.doc,e)}else r=e;return Gn(this,r,{top:0,left:0},t||"page",n||i).top+(i?this.doc.height-$e(r):0)},defaultTextHeight:function(){return tr(this.display)},defaultCharWidth:function(){return nr(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(e,t,n,r,i){var o,l,a,s=this.display,c=(e=Kn(this,Ce(this.doc,e))).bottom,u=e.left;if(t.style.position="absolute",t.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(t),s.sizer.appendChild(t),"over"==r)c=e.top;else if("above"==r||"near"==r){var h=Math.max(s.wrapper.clientHeight,this.doc.height),f=Math.max(s.sizer.clientWidth,s.lineSpace.clientWidth);("above"==r||e.bottom+t.offsetHeight>h)&&e.top>t.offsetHeight?c=e.top-t.offsetHeight:e.bottom+t.offsetHeight<=h&&(c=e.bottom),u+t.offsetWidth>f&&(u=f-t.offsetWidth)}t.style.top=c+"px",t.style.left=t.style.right="","right"==i?(u=s.sizer.clientWidth-t.offsetWidth,t.style.right="0px"):("left"==i?u=0:"middle"==i&&(u=(s.sizer.clientWidth-t.offsetWidth)/2),t.style.left=u+"px"),n&&(o=this,l={left:u,top:c,right:u+t.offsetWidth,bottom:c+t.offsetHeight},null!=(a=Mr(o,l)).scrollTop&&Ar(o,a.scrollTop),null!=a.scrollLeft&&Dr(o,a.scrollLeft))},triggerOnKeyDown:Xr(ol),triggerOnKeyPress:Xr(al),triggerOnKeyUp:ll,triggerOnMouseDown:Xr(hl),execCommand:function(e){if(qo.hasOwnProperty(e))return qo[e].call(null,this)},triggerElectric:Xr(function(e){Al(this,e)}),findPosH:function(e,t,n,r){var i=1;t<0&&(i=-1,t=-t);for(var o=Ce(this.doc,e),l=0;l<t&&!(o=Il(this.doc,o,i,n,r)).hitSide;++l);return o},moveH:Xr(function(e,t){var n=this;this.extendSelectionsBy(function(r){return n.display.shift||n.doc.extend||r.empty()?Il(n.doc,r.head,e,t,n.options.rtlMoveVisually):e<0?r.from():r.to()},V)}),deleteH:Xr(function(e,t){var n=this.doc.sel,r=this.doc;n.somethingSelected()?r.replaceSelection("",null,"+delete"):$o(this,function(n){var i=Il(r,n.head,e,t,!1);return e<0?{from:i,to:n.head}:{from:n.head,to:i}})}),findPosV:function(e,t,n,r){var i=1,o=r;t<0&&(i=-1,t=-t);for(var l=Ce(this.doc,e),a=0;a<t;++a){var s=Kn(this,l,"div");if(null==o?o=s.left:s.left=o,(l=Wl(this,s,i,n)).hitSide)break}return l},moveV:Xr(function(e,t){var n=this,r=this.doc,i=[],o=!this.display.shift&&!r.extend&&r.sel.somethingSelected();if(r.extendSelectionsBy(function(l){if(o)return e<0?l.from():l.to();var a=Kn(n,l.head,"div");null!=l.goalColumn&&(a.left=l.goalColumn),i.push(a.left);var s=Wl(n,a,e,t);return"page"==t&&l==r.sel.primary()&&Sr(n,$n(n,s,"div").top-a.top),s},V),i.length)for(var l=0;l<r.sel.ranges.length;l++)r.sel.ranges[l].goalColumn=i[l]}),findWordAt:function(e){var t=this.doc,n=ae(t,e.line).text,r=e.ch,i=e.ch;if(n){var o=this.getHelper(e,"wordChars");"before"!=e.sticky&&i!=n.length||!r?++i:--r;for(var l=n.charAt(r),a=te(l,o)?function(e){return te(e,o)}:/\s/.test(l)?function(e){return/\s/.test(e)}:function(e){return!/\s/.test(e)&&!te(e)};r>0&&a(n.charAt(r-1));)--r;for(;i<n.length&&a(n.charAt(i));)++i}return new bi(ge(e.line,r),ge(e.line,i))},toggleOverwrite:function(e){null!=e&&e==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?F(this.display.cursorDiv,"CodeMirror-overwrite"):L(this.display.cursorDiv,"CodeMirror-overwrite"),rt(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==D()},isReadOnly:function(){return!(!this.options.readOnly&&!this.doc.cantEdit)},scrollTo:Xr(function(e,t){Er(this,e,t)}),getScrollInfo:function(){var e=this.display.scroller;return{left:e.scrollLeft,top:e.scrollTop,height:e.scrollHeight-Ln(this)-this.display.barHeight,width:e.scrollWidth-Ln(this)-this.display.barWidth,clientHeight:Tn(this),clientWidth:En(this)}},scrollIntoView:Xr(function(e,t){null==e?(e={from:this.doc.sel.primary().head,to:null},null==t&&(t=this.options.cursorScrollMargin)):"number"==typeof e?e={from:ge(e,0),to:null}:null==e.from&&(e={from:e,to:null}),e.to||(e.to=e.from),e.margin=t||0,null!=e.from.line?function(e,t){Tr(e),e.curOp.scrollToPos=t}(this,e):Nr(this,e.from,e.to,e.margin)}),setSize:Xr(function(e,t){var n=this,r=function(e){return"number"==typeof e||/^\d+$/.test(String(e))?e+"px":e};null!=e&&(this.display.wrapper.style.width=r(e)),null!=t&&(this.display.wrapper.style.height=r(t)),this.options.lineWrapping&&Hn(this);var i=this.display.viewFrom;this.doc.iter(i,this.display.viewTo,function(e){if(e.widgets)for(var t=0;t<e.widgets.length;t++)if(e.widgets[t].noHScroll){Qr(n,i,"widget");break}++i}),this.curOp.forceUpdate=!0,rt(this,"refresh",this)}),operation:function(e){return Kr(this,e)},startOperation:function(){return Br(this)},endOperation:function(){return Ur(this)},refresh:Xr(function(){var e=this.display.cachedTextHeight;Zr(this),this.curOp.forceUpdate=!0,zn(this),Er(this,this.doc.scrollLeft,this.doc.scrollTop),si(this),(null==e||Math.abs(e-tr(this.display))>.5)&&lr(this),rt(this,"refresh",this)}),swapDoc:Xr(function(e){var t=this.doc;return t.cm=null,Ai(this,e),zn(this),this.display.input.reset(),Er(this,e.scrollLeft,e.scrollTop),this.curOp.forceScroll=!0,sn(this,"swapDoc",this,t),t}),getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},at(e),e.registerHelper=function(t,r,i){n.hasOwnProperty(t)||(n[t]=e[t]={_global:[]}),n[t][r]=i},e.registerGlobalHelper=function(t,r,i,o){e.registerHelper(t,r,o),n[t]._global.push({pred:i,val:o})}}(kl);var Ul,jl="iter insert remove copy getEditor constructor".split(" ");for(var Gl in Lo.prototype)Lo.prototype.hasOwnProperty(Gl)&&z(jl,Gl)<0&&(kl.prototype[Gl]=function(e){return function(){return e.apply(this.doc,arguments)}}(Lo.prototype[Gl]));return at(Lo),kl.inputStyles={textarea:Bl,contenteditable:Pl},kl.defineMode=function(e){kl.defaults.mode||"null"==e||(kl.defaults.mode=e),function(e,t){arguments.length>2&&(t.dependencies=Array.prototype.slice.call(arguments,2)),Mt[e]=t}.apply(this,arguments)},kl.defineMIME=function(e,t){St[e]=t},kl.defineMode("null",function(){return{token:function(e){return e.skipToEnd()}}}),kl.defineMIME("text/plain","null"),kl.defineExtension=function(e,t){kl.prototype[e]=t},kl.defineDocExtension=function(e,t){Lo.prototype[e]=t},kl.fromTextArea=function(e,t){if((t=t?_(t):{}).value=e.value,!t.tabindex&&e.tabIndex&&(t.tabindex=e.tabIndex),!t.placeholder&&e.placeholder&&(t.placeholder=e.placeholder),null==t.autofocus){var n=D();t.autofocus=n==e||null!=e.getAttribute("autofocus")&&n==document.body}function r(){e.value=a.getValue()}var i;if(e.form&&(et(e.form,"submit",r),!t.leaveSubmitMethodAlone)){var o=e.form;i=o.submit;try{var l=o.submit=function(){r(),o.submit=i,o.submit(),o.submit=l}}catch(e){}}t.finishInit=function(t){t.save=r,t.getTextArea=function(){return e},t.toTextArea=function(){t.toTextArea=isNaN,r(),e.parentNode.removeChild(t.getWrapperElement()),e.style.display="",e.form&&(nt(e.form,"submit",r),"function"==typeof e.form.submit&&(e.form.submit=i))}},e.style.display="none";var a=kl(function(t){return e.parentNode.insertBefore(t,e.nextSibling)},t);return a},(Ul=kl).off=nt,Ul.on=et,Ul.wheelEventPixels=gi,Ul.Doc=Lo,Ul.splitLines=xt,Ul.countColumn=R,Ul.findColumn=$,Ul.isWordChar=ee,Ul.Pass=U,Ul.signal=rt,Ul.Line=$t,Ul.changeEnd=wi,Ul.scrollbarModel=Rr,Ul.Pos=ge,Ul.cmpPos=me,Ul.modes=Mt,Ul.mimeModes=St,Ul.resolveMode=Lt,Ul.getMode=Et,Ul.modeExtensions=Tt,Ul.extendMode=Nt,Ul.copyState=At,Ul.startState=Dt,Ul.innerMode=Ot,Ul.commands=qo,Ul.keyMap=Ro,Ul.keyName=Go,Ul.isModifierKey=Uo,Ul.lookupKey=Bo,Ul.normalizeKeyMap=zo,Ul.StringStream=Ft,Ul.SharedTextMarker=Co,Ul.TextMarker=xo,Ul.LineWidget=vo,Ul.e_preventDefault=st,Ul.e_stopPropagation=ct,Ul.e_stop=ht,Ul.addClass=F,Ul.contains=O,Ul.rmClass=L,Ul.keyNames=Io,kl.version="5.39.0",kl}()},function(e,t,n){var r;
/*!
  Copyright (c) 2017 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/
/*!
  Copyright (c) 2017 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/
!function(){"use strict";var n={}.hasOwnProperty;function i(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var o=typeof r;if("string"===o||"number"===o)e.push(r);else if(Array.isArray(r)&&r.length){var l=i.apply(null,r);l&&e.push(l)}else if("object"===o)for(var a in r)n.call(r,a)&&r[a]&&e.push(a)}}return e.join(" ")}void 0!==e&&e.exports?(i.default=i,e.exports=i):void 0===(r=function(){return i}.apply(t,[]))||(e.exports=r)}()},function(e,t,n){"use strict";n.r(t);var r=n(0),i=n.n(r),o=n(1),l=n.n(o);n(51);const{Dialog:a,Button:s,Notification:c}=n(4);var u;!function(e){e.READY="ready",e.UPLOADING="uploading",e.DONE="done",e.ABORT="abort",e.ERROR="error"}(u||(u={}));class h extends i.a.Component{constructor(){super(...arguments),this.state={files:[],uploadStatus:u.READY,uploadIndex:0,errorMsg:""},this.onInputChange=(e=>{const t=e.target.files,n=l.a.cloneDeep(this.state.files);let r=0;if(t&&t.length>0)for(let e=0;e<t.length;e++){const r=t.item(e);if(r){const e=f(r.size);n.unshift({id:r.lastModified.toString(),file:r,name:r.name,rawSize:r.size,size:`${e.size} ${e.unit}`,status:u.READY,percent:0})}}n.forEach(e=>r+=e.rawSize);const i=l.a.uniqBy(n,"name");n.length>0&&this.props.onChange&&this.props.onChange(i),this.setState({files:i},()=>{this.checkOverLimit(r)}),this.input.value=""}),this.onFileUpload=(()=>{if(!this.props.visible)return;const{files:e=[],uploadIndex:t}=this.state,n=e[t].file,r=t===e.length-1;let i="";this.setState({uploadStatus:u.UPLOADING}),this.props.onStart&&this.props.onStart(),this.currentUploadXHR=window.uw.upload({file:n,url:this.props.url,params:this.props.params,onProgress:(n,o)=>{const a=l.a.cloneDeep(e);switch(l.a.set(a,[t,"status"],n),n){case u.UPLOADING:l.a.set(a,[t,"percent"],o);break;case u.DONE:l.a.set(a,[t,"percent"],100),0!==o.code&&(i=o.msg,l.a.set(a,[t,"error"],o.msg||"错误"));break;case u.ERROR:l.a.set(a,[t,"error"],o.message||"错误");break;case u.ABORT:}n!==u.UPLOADING?r?this.setState({files:a,uploadIndex:0,uploadStatus:u.DONE,errorMsg:i}):this.setState({files:a,uploadIndex:t+1},()=>{this.onFileUpload()}):this.setState({files:a})}})}),this.onDelete=(e=>()=>{const t=l.a.cloneDeep(this.state.files);l.a.remove(t,t=>t.id===e);let n=0;t.forEach(e=>n+=e.rawSize),this.setState({files:t},()=>{this.checkOverLimit(n),this.props.onChange&&this.props.onChange(t)})}),this.onCloseUpload=(()=>{this.cleanState(),this.props.onComplete&&this.props.onComplete()}),this.onCancel=(()=>{this.cleanState(),this.props.onCancel&&this.props.onCancel()}),this.onCancelUpload=(()=>{this.currentUploadXHR.abort()}),this.cleanState=(()=>{this.setState({files:[],uploadStatus:u.READY,uploadIndex:0,errorMsg:""}),this.currentUploadXHR&&this.currentUploadXHR.abort()}),this.checkOverLimit=(e=>{const{limit:t=1/0}=this.props;e>=t?this.setState({errorMsg:`上传文件大小超过了 ${l.a.toInteger(f(t).size)}${f(t).unit}`}):this.setState({errorMsg:""})}),this.genUploadFooter=(()=>{if(this.state.uploadStatus===u.DONE)return i.a.createElement(s,{type:"primary",onClick:this.onCloseUpload},"完成上传");const{files:e,uploadStatus:t,errorMsg:n}=this.state;return i.a.createElement("span",null,t!==u.UPLOADING&&i.a.createElement(s,{type:"primary",disabled:0===e.length||n,style:{marginRight:5},onClick:this.onFileUpload},"开始上传"),i.a.createElement(s,{onClick:this.onCancel},"取消"))})}componentWillReceiveProps(e){!0===e.visible&&!1===this.props.visible&&this.setState({files:[],uploadStatus:u.READY,uploadIndex:0})}render(){const e=this.state.uploadStatus!==u.READY;return i.a.createElement(a,{title:"上传文件",style:{width:600},visible:this.props.visible,maskClosable:!1,footer:this.genUploadFooter(),onCancel:this.onCancel},i.a.createElement("div",{className:"file-upload-dialog"},this.state.errorMsg&&i.a.createElement(c,{type:"error"},this.state.errorMsg),e||i.a.createElement("div",{className:"upload-helper"},i.a.createElement("a",{className:`upload-btn-wrap ${e?"disabled":""}`,title:"选择文件"},i.a.createElement("div",{className:"file-input-wrap"},i.a.createElement("input",{type:"file",title:this.props.inputHelp,disabled:e,multiple:!0,onChange:this.onInputChange,ref:e=>{this.input=e}}),i.a.createElement("span",null,"选择文件"))),i.a.createElement("span",{className:"text-label"},this.props.inputHelp)),i.a.createElement("div",{className:"tc-15-table-panel"},i.a.createElement("div",{className:"tc-15-table-fixed-head"},i.a.createElement("table",{className:"tc-15-table-box"},i.a.createElement("colgroup",null,i.a.createElement("col",{style:{width:230}}),i.a.createElement("col",null),i.a.createElement("col",{style:{width:125}}),i.a.createElement("col",{style:{width:75}})),i.a.createElement("thead",null,i.a.createElement("tr",null,i.a.createElement("th",null,i.a.createElement("div",null,i.a.createElement("span",{className:"text-overflow"},"文件名")),i.a.createElement("i",{className:"resize-line-icon"})),i.a.createElement("th",null,i.a.createElement("div",null,i.a.createElement("span",{className:"text-overflow"},"大小")),i.a.createElement("i",{className:"resize-line-icon"})),i.a.createElement("th",null,i.a.createElement("div",null,i.a.createElement("span",{className:"text-overflow"},"状态")),i.a.createElement("i",{className:"resize-line-icon"})),i.a.createElement("th",null,i.a.createElement("div",null,i.a.createElement("span",{className:"text-overflow"},"操作"))))))),i.a.createElement("div",{className:"tc-15-table-fixed-body"},i.a.createElement("table",{className:"tc-15-table-box tc-15-table-rowhover"},i.a.createElement("colgroup",null,i.a.createElement("col",{style:{width:230}}),i.a.createElement("col",null),i.a.createElement("col",{style:{width:125}}),i.a.createElement("col",{style:{width:75}})),i.a.createElement("tbody",null,i.a.createElement("tr",{className:"drag-tr"},i.a.createElement("td",null,i.a.createElement("div",{className:"drag-helper"},i.a.createElement("p",{className:"text-label"},"点击上方“选择文件”按钮开始上传文件")))),this.state.files.map(e=>i.a.createElement("tr",{key:e.id},i.a.createElement("td",null,i.a.createElement("div",null,i.a.createElement("span",{className:"text-overflow"},e.name))),i.a.createElement("td",null,i.a.createElement("div",null,i.a.createElement("span",{className:"text-overflow"},e.size))),i.a.createElement("td",null,i.a.createElement("div",null,function(e,t){switch(e){case"ready":return i.a.createElement("span",{className:"text-overflow"},i.a.createElement("i",{className:"n-restart-icon"}),"待上传");case"uploading":return i.a.createElement("span",{className:"text-overflow"},i.a.createElement("i",{className:"n-loading-icon"}),"上传",t.percent,"%");case"done":return i.a.createElement("span",{className:"text-overflow"},i.a.createElement("i",{className:"n-success-icon"}),"上传成功");case"abort":return i.a.createElement("span",{className:"text-overflow"},i.a.createElement("i",{className:"n-error-icon"}),"已取消");default:return i.a.createElement("span",{className:"text-overflow"},i.a.createElement("i",{className:"n-error-icon"}),t.error)}}(e.status,e))),i.a.createElement("td",null,i.a.createElement("div",null,"ready"===e.status&&this.state.uploadStatus!==u.UPLOADING&&i.a.createElement("a",{href:"javascript:void(0)",onClick:this.onDelete(e.id)},"删除"),"uploading"===e.status&&i.a.createElement("a",{href:"javascript:void(0)",onClick:this.onCancelUpload},"取消")))))))))),this.props.children)}}function f(e){if(0===e)return{size:0,unit:"B"};const t=Math.floor(Math.log(e)/Math.log(1024));return{size:(e/Math.pow(1024,Math.floor(t))).toFixed(2),unit:["B","KB","MB","GB","TB","PB"][t]}}n(48);const{Panel:d}=n(4);class p extends i.a.Component{render(){const{image:e,title:t,content:n,footer:r}=this.props;return i.a.createElement("div",{className:"uw-placeholder"},i.a.createElement(d,null,i.a.createElement("div",{className:"content"},e&&i.a.createElement("i",{className:"image",style:{backgroundImage:`url(${e})`}}),i.a.createElement("h3",null,t),i.a.createElement("p",null,n),i.a.createElement("div",{className:"footer"},r))))}}n(46);class g extends i.a.Component{render(){return i.a.createElement("span",{className:"uw-plugins-rawtext",dangerouslySetInnerHTML:window.uw.toReactString(this.props.text)})}}n(44);const{Dialog:m}=n(4),v=130,b=5,y=v/2,x=2*Math.PI*(y-b),w={processing:"#006EFF",error:"#E54545",success:"#0ABF5B"};class C extends i.a.Component{constructor(){super(...arguments),this.state={percent:0,status:"processing",title:""},this.autoProcessing=(()=>{const{percent:e}=this.state;e>=95?(clearInterval(this.timer),this.timer=null):this.setState({percent:e>=95?95:e+1})}),this.setCircleAttribute=((e,t)=>{const n="processing"===t?e/100:1;return`${x*n} ${x*(1-n)}`})}componentWillReceiveProps(e){const{percent:t,status:n,title:r}=e;if(l.a.isNumber(t)){if(l.a.isNumber(this.timer)||"processing"!==n||(this.autoProcessing(),this.timer=setInterval(()=>{this.autoProcessing()},500)),"processing"!==e.status&&"processing"===this.props.status)return void this.setState({percent:100},()=>{setTimeout(()=>{this.setState({percent:0,status:n,title:r})},1e3)});t>this.state.percent&&this.setState({percent:t})}this.setState({status:n,title:r}),!0===e.visible&&!1===this.props.visible&&this.setState({percent:!1,status:"processing"})}render(){const{status:e,percent:t,title:n}=this.state,r="success"===e||"error"===e;return i.a.createElement(m,{visible:this.props.visible,maskClosable:!1,footer:r&&this.props.footer},i.a.createElement("div",{className:"uw-plugins-process"},i.a.createElement("div",{className:"process",style:{width:v}},r||i.a.createElement("svg",{width:v,height:v,viewBox:`0 0 ${v} ${v}`},l.a.isNumber(this.props.percent)?[i.a.createElement("text",{key:"text",x:"50%",y:"50%",dy:".5em",textAnchor:"middle"},t?`${t} %`:""),i.a.createElement("circle",{key:"bg",cx:y,cy:y,r:y-b,strokeWidth:b,stroke:"#f5f5f5",fill:"none"}),i.a.createElement("circle",{className:"processing"===e?"processing":void 0,key:"color-circle",cx:y,cy:y,r:y-b,strokeWidth:b,stroke:w[e],fill:"none",transform:`matrix(0,-1,1,0,0,${v})`,strokeDasharray:this.setCircleAttribute(t,e)})]:i.a.createElement("circle",{className:"rotate",cx:y,cy:y,r:y-b,strokeWidth:b})),"success"===e&&i.a.createElement("svg",{className:"animation",xmlns:"http://www.w3.org/2000/svg",width:v,height:v,viewBox:`0 0 ${v} ${v}`},i.a.createElement("circle",{className:"successAnimationCircle",cx:y,cy:y,r:y-b,fill:"transparent",strokeWidth:b}),i.a.createElement("polyline",{className:"successAnimationCheck",points:"33.67 64.71 54.75 85.79 96.33 44.21",fill:"transparent",strokeWidth:b})),"error"===e&&i.a.createElement("svg",{className:"animation",xmlns:"http://www.w3.org/2000/svg",width:v,height:v,viewBox:`0 0 ${v} ${v}`},i.a.createElement("circle",{className:"errorAnimationCircle",cx:y,cy:y,r:y-b,fill:"transparent",strokeWidth:b}),i.a.createElement("line",{className:"errorAnimationCross",x1:"42.83",y1:"42.34",x2:"89.83",y2:"89.67",fill:"transparent",strokeWidth:b}),i.a.createElement("line",{className:"errorAnimationCross",x1:"89.83",y1:"42.34",x2:"42.83",y2:"89.67",fill:"transparent",strokeWidth:b}))),i.a.createElement("h2",null,n),this.props.content&&i.a.createElement("div",{className:"content"},this.props.content)))}}n(42);const{Panel:k,Icon:M}=n(4);var S=e=>i.a.createElement(k,null,e.error?i.a.createElement("div",{className:"uw-plugins-page-loading"},i.a.createElement(M,{type:"error-small"})," ",e.error):i.a.createElement("div",{className:"uw-plugins-page-loading"},i.a.createElement(M,{type:"loading"})," ",e.loadingInfo||"加载中"));n(40);const{Icon:L}=n(4);function E(e){const{list:t=[],onClick:n=(()=>{})}=e,r=t.length,o=(e,t)=>()=>{n(e,t)},l=e.rootText||"根目录";return 0===r?null:1===r?i.a.createElement("div",{className:"uw-plugins-breadcrumb"},i.a.createElement("ul",{className:"breadcrumb clear"},i.a.createElement("li",{className:"cur"},l))):r<6?i.a.createElement("div",{className:"uw-plugins-breadcrumb"},i.a.createElement("ul",{className:"breadcrumb clear"},i.a.createElement("li",{className:"root"},i.a.createElement("a",{onClick:o(t[0],0)},l)),t.slice(1).map((e,t)=>{const n=r-2===t;return i.a.createElement("li",{key:t,className:n?"cur":void 0},i.a.createElement(L,{type:"arrow-right"}),i.a.createElement("a",{onClick:n?void 0:o(e,t+1),title:e},e))}))):i.a.createElement("div",{className:"uw-plugins-breadcrumb"},i.a.createElement("ul",{className:"breadcrumb clear"},i.a.createElement("li",{className:"root"},i.a.createElement("a",{onClick:o(t[0],0)},l)),i.a.createElement("li",{className:"more"},i.a.createElement(L,{type:"arrow-right"}),i.a.createElement("a",null,"...")),t.slice(-4).map((e,t)=>{const n=r-4,l=r-n-1===t;return i.a.createElement("li",{key:t,className:l?"cur":void 0},i.a.createElement(L,{type:"arrow-right"}),i.a.createElement("a",{onClick:l?void 0:o(e,t+n),title:e},e))})))}n(38);const{Icon:T}=n(4);function N(e){const{style:t}=e;return e.children?i.a.createElement("span",{className:"uw-plugins-error-message",title:e.children,style:t},i.a.createElement(T,{type:"error-small"})," ",e.children):null}const{Icon:A}=n(4);function O(e){return i.a.createElement("a",{href:e.href,style:Object.assign({display:"inline-block"},e.style),target:"_blank"},e.children," ",i.a.createElement(A,{type:"external-link"}))}n(36);const{Button:D,Icon:F}=n(4);class I extends i.a.Component{render(){const{okText:e="确定",cancelText:t="取消",okLoading:n,info:r,onOk:o,onCancel:l,okDisabled:a=!1}=this.props;return i.a.createElement("div",null,i.a.createElement("div",{className:"uw-dialog-footer-info"},r),o&&i.a.createElement(D,{type:"primary",onClick:o,style:{margin:"0 5px"},disabled:n||a},n&&i.a.createElement("span",null,i.a.createElement(F,{type:"loading"})," "),e),l&&i.a.createElement(D,{onClick:l,style:{margin:"0 5px"}},t))}}var W=n(6),P=n.n(W);n(34);const{Row:_,Col:R,Button:H}=n(4);class z extends i.a.Component{constructor(){super(...arguments),this.onQuery=(()=>{const{onQuery:e}=this.props;e&&e()}),this.getFilterContent=(e=>{if(Object(o.isArray)(e)){const t=[],n=Object(o.chunk)(e,3);return Object(o.forEach)(n,(e,n)=>{const r=i.a.createElement(_,{key:n},e.map((e,t)=>i.a.createElement(R,{col:8,key:t},i.a.createElement("div",{className:"form-label"},i.a.createElement("label",null,e.label)),i.a.createElement("div",{className:"form-component"},e.component))));t.push(r)}),i.a.createElement("div",null,t)}return e}),this.onResultClose=(e=>{const{onResultClose:t}=this.props;t&&t(e)}),this.onResultClear=(()=>{const{onResultClear:e}=this.props;e&&e()})}render(){const{expand:e,filterContent:t,filterResult:n=[]}=this.props,r=P()({"filter-content-show":e&&t}),o=P()({"filter-result-show":!e&&n&&n.length>0});return i.a.createElement("div",null,i.a.createElement("div",{className:r},i.a.createElement("div",{className:"filter-form"},this.getFilterContent(t),i.a.createElement("div",{className:"query-button"},i.a.createElement(H,{type:"primary",onClick:this.onQuery},"搜索")))),i.a.createElement("div",{className:o},i.a.createElement("div",{className:"tc-15-tag-list"},n.map(e=>i.a.createElement("div",{className:"tc-15-tag",tabIndex:0,key:e.label},i.a.createElement("span",{className:"uw-filter-result-tag",title:`${e.label}：${e.value}`},`${e.label}：${e.value}`),i.a.createElement("span",{className:"tc-15-btn-close",onClick:this.onResultClose.bind(this,e)}))),i.a.createElement("div",{className:"tc-15-tag-clear",role:"button",tabIndex:0,onClick:this.onResultClear},"清空筛选项"))))}}n(32);const{Dropdown:B,Icon:U}=n(4);n(30),n(28),n(26),n(24),n(23),n(22),n(21),n(20),n(18),n(17),n(16);const j=n(5);class G extends i.a.Component{componentDidMount(){if(this.editorIns){if(!1===this.props.compare)this.editor=j(this.editorIns,{value:this.props.content,lineNumbers:!0,readOnly:!!this.props.readOnly&&"nocursor"});else if(!0===this.props.compare){const e=j.MergeView(this.editorIns,{value:this.props.content,origRight:this.props.compareContent,lineNumbers:!0,revertButtons:!1,readOnly:!!this.props.readOnly&&"nocursor"});this.editor=e.edit,this.rightEditor=e.right.orig}j.on(this.editor,"blur",(e,t)=>{this.props.onBlur&&this.props.onBlur(!this.editor.getValue(),this.editor.getValue())}),j.on(this.editor,"focus",(e,t)=>{this.props.onFocus&&this.props.onFocus()})}}componentWillReceiveProps(e){if(!0===e.compare&&this.props.compare!==e.compare){this.editor&&(this.editor.getWrapperElement().parentNode.removeChild(this.editor.getWrapperElement()),this.editor=null);const t=j.MergeView(this.editorIns,{value:e.content,origRight:e.compareContent,lineNumbers:!0,revertButtons:!1,readOnly:!!e.readOnly&&"nocursor"});this.editor=t.edit,this.rightEditor=t.right.orig,j.on(this.editor,"blur",(e,t)=>{this.props.onBlur&&this.props.onBlur(!this.editor.getValue(),this.editor.getValue())}),j.on(this.editor,"focus",(e,t)=>{this.props.onFocus&&this.props.onFocus()})}setTimeout(()=>{this.editor.refresh()},0),this.editor&&this.editor.setValue(e.content),this.rightEditor&&this.rightEditor.setValue(e.compareContent)}componentWillUnmount(){this.editor=void 0,this.editorIns=void 0,this.rightEditor=void 0}render(){const{compare:e,versionList:t=[],version:n}=this.props,r=t.map(e=>({label:e,value:e,key:e}));return i.a.createElement("div",{style:{position:"relative"}},this.props.loading&&i.a.createElement("div",{className:"loading-area"},i.a.createElement("div",{className:"loading-text"},i.a.createElement(U,{type:"loading"}),this.props.loadingInfo||"加载中")),!0===e&&i.a.createElement("div",{className:"text-area"},i.a.createElement("span",null,this.props.title,": "),i.a.createElement("span",{style:{float:"right"}},this.props.compareTitle,":  ",i.a.createElement(B,{value:n,options:r,onChange:e=>{this.props.onVersionChange&&this.props.onVersionChange(e)}}))),i.a.createElement("div",{className:"config-upload",ref:e=>{this.editorIns=e}}))}}G.defaultProps={compare:!1,content:"",originContent:"",compareContent:"",versionList:[],title:"当前版本",compareTitle:"比对版本",readOnly:!0,loading:!1};n(15);const{Icon:V,Button:$}=n(4);class K extends i.a.Component{constructor(){super(...arguments),this.state={errMsg:""},this.getExt=(e=>{const t=e.split(".");return t[t.length-1]}),this.onInputChange=(e=>{const t=e.target.files,{help:n,emptyText:r,onUpload:i}=this.props;t&&t.length>0&&(-1===(this.props.fileTypes||[]).indexOf(this.getExt(t[0].name))?this.props.onUploadErrorFile?this.props.onUploadErrorFile(!0,n||""):this.setState({errMsg:n}):0===t[0].size?this.props.onUploadErrorFile?this.props.onUploadErrorFile(!0,r||""):this.setState({errMsg:r}):(this.setState({errMsg:""}),i&&i(t[0]))),this.input.value=""})}componentWillMount(){this.input=void 0}render(){const{disabled:e,help:t,title:n,loading:r,errMsg:o}=this.props;return i.a.createElement("div",{className:"upload-helper"},e||r?i.a.createElement($,{disabled:"true"},r?i.a.createElement(V,{type:"loading"}):n):i.a.createElement("a",{className:"upload-btn-wrap",title:"选择文件"},i.a.createElement("div",{className:"file-input-wrap"},i.a.createElement("input",{type:"file",title:t,disabled:e,multiple:!1,onChange:this.onInputChange,ref:e=>{this.input=e}}),i.a.createElement("span",null,n))),this.state.errMsg||o?i.a.createElement(N,null,this.state.errMsg||o):i.a.createElement("span",{className:"text-label"},t))}}K.defaultProps={disabled:!1,help:"文件类型不符合要求",title:"选择文件",fileTypes:[],loading:!1};n(13);const{Dialog:Y,Checkbox:X,Col:q,Row:Z,Notification:Q,Icon:J}=n(4),ee=e=>{let t=[];return Object(o.forEach)(e,e=>{t=t.concat(e.items)}),t=Object(o.filter)(t,e=>e.isSelected),Object(o.map)(t,e=>e.englishName)};class te extends i.a.Component{constructor(e){super(e),this.handleShowModel=(e=>{e&&e.stopPropagation(),this.setState({visible:!0,selectValues:this.state.oldSelectValues})}),this.handleHideModel=(()=>{this.setState({visible:!1})}),this.handleSave=(()=>{const{confirmClick:e}=this.props;let t=[];Object(o.forEach)(this.props.extendItems,e=>{t=t.concat(e.items)}),t=Object(o.filter)(t,e=>Object(o.includes)(this.state.selectValues,e.englishName)),t=Object(o.cloneDeep)(t),Object(o.forEach)(t,e=>{e.isSelected=!0}),e(t),this.setState({visible:!1,oldSelectValues:this.state.selectValues})}),this.handleCheckChange=((e,t)=>{let n=Object(o.cloneDeep)(this.state.selectValues);t?n=Object(o.union)(n,[e]):Object(o.remove)(n,t=>t===e),this.setState({selectValues:n})});let t=ee(e.extendItems);this.state={visible:!1,oldSelectValues:t||[],selectValues:t}}componentWillReceiveProps(e){let t=ee(e.extendItems);this.setState({oldSelectValues:t,selectValues:t})}render(){const{extendItems:e,disabledItems:t,maxCount:n,extendDisabled:r}=this.props;let l=[];for(let r=0;r<e.length;r+=3){let a=[];for(let l=0;l<3;l++){let s=e[r+l];s&&s.items&&s.items.length&&-1===["网络","虚拟化"].indexOf(s.groupName)&&a.push(i.a.createElement(q,{col:8,key:s.groupName},i.a.createElement("div",{title:s.groupName,className:"uw-group-title"},i.a.createElement("span",null,s.groupName)),s.items.map(e=>i.a.createElement("div",{key:e.englishName,style:{marginTop:5}},Object(o.find)(t,{englishName:e.englishName})||this.state.selectValues.length>=n&&n&&!Object(o.includes)(this.state.selectValues,e.englishName)?i.a.createElement(X,{disabled:!0,key:e.englishName,checked:-1!==this.state.selectValues.indexOf(e.englishName)},i.a.createElement("span",{title:e.extraInfo?`${e.chineseName}(${e.extraInfo})`:e.chineseName},e.chineseName)):i.a.createElement(X,{key:e.englishName,value:e.englishName,checked:-1!==this.state.selectValues.indexOf(e.englishName),onChange:t=>{this.handleCheckChange(e.englishName,t)}},i.a.createElement("span",{title:e.extraInfo?`${e.chineseName}(${e.extraInfo})`:e.chineseName},e.chineseName))))))}l.push(i.a.createElement(Z,{style:{marginBottom:10},key:r},a))}return i.a.createElement("div",{className:"uw-extend-layout"},r?i.a.createElement("div",{style:{cursor:"not-allowed"}},i.a.createElement(J,{type:"setting"})):i.a.createElement("div",{onClick:this.handleShowModel.bind(this)},i.a.createElement(J,{type:"setting"})),i.a.createElement(Y,{style:{width:600},title:"自定义列表字段",visible:this.state.visible,maskClosable:!1,onOk:this.handleSave.bind(this),onCancel:this.handleHideModel.bind(this)},i.a.createElement("div",{className:"uw-extend-content"},n>0&&i.a.createElement(Q,null,`请选择你想展示的列表详细信息，最多勾选 ${n} 个，已勾选 ${this.state.selectValues.length} 个`),l)))}}te.defaultProps={extendItems:[],disabledItems:[],maxCount:0,extendDisabled:!1,confirmClick:()=>{}};n(11);class ne extends i.a.Component{constructor(){super(...arguments),this.state={searchText:""},this.onSeachTextChange=(e=>{this.setState({searchText:e.target.value})}),this.onClearSearchText=(()=>{this.setState({searchText:""},()=>{this.props.onSearchClear&&this.props.onSearchClear()})})}render(){const{leftTitle:e,rightTitle:t,searchPlaceholder:n,style:r,rightList:o=[],placeholder:a,customLeft:s,leftExtra:c,rightExtra:u,onRemove:h}=this.props;return i.a.createElement("div",{className:"tc-15-mod-selector",style:r},i.a.createElement("div",{className:"tc-15-mod-selector-tb"},i.a.createElement("div",{className:"tc-15-option-cell options-left"},i.a.createElement("div",{className:"tc-15-option-hd"},i.a.createElement("h4",null,e),c&&i.a.createElement("span",{className:"extra"},c)),i.a.createElement("div",{className:"tc-15-option-bd"},i.a.createElement("div",{className:"search-box multi-search-box"},i.a.createElement("div",{className:"search-input-wrap"},i.a.createElement("textarea",{value:this.state.searchText,onChange:this.onSeachTextChange,className:"tc-15-input-text search-input",placeholder:n}),i.a.createElement("a",{role:"button",className:"btn-rm-txt",onClick:this.onClearSearchText})),i.a.createElement("button",{type:"button",className:"search-btn",onClick:()=>{this.props.onSearch&&this.props.onSearch(this.state.searchText)}})),i.a.createElement("div",{className:"tc-15-option-box tc-scroll"},a&&i.a.createElement("div",{className:"info-box"},a),!a&&s))),i.a.createElement("div",{className:"tc-15-option-cell separator-cell"},i.a.createElement("i",{className:"icon-sep"})),i.a.createElement("div",{className:"tc-15-option-cell options-right"},i.a.createElement("div",{className:"tc-15-option-hd"},i.a.createElement("h4",null,t),u&&i.a.createElement("span",{className:"extra"},u)),i.a.createElement("div",{className:"tc-15-option-bd"},i.a.createElement("div",{className:"tc-15-option-box tc-scroll"},i.a.createElement("ul",{className:"tc-15-option-list"},o.map(e=>i.a.createElement("li",{key:e.key},i.a.createElement("span",{className:"opt-txt"},l.a.isString(e)?i.a.createElement("span",{className:"opt-txt-inner"},i.a.createElement("span",{className:"item-name"},e)):i.a.createElement("span",{className:"opt-txt-inner"},i.a.createElement("span",{className:"item-name"},e.name),e.desc&&i.a.createElement("span",{className:"item-descr"},e.desc))),i.a.createElement("a",{role:"button",className:"opt-act",onClick:()=>{h&&h(e.key)}},i.a.createElement("i",{className:"icon-del"}))))))))))}}n(9);const{Table:re,Icon:ie}=n(4);class oe extends i.a.Component{constructor(){super(...arguments),this.state={expand:!1}}onShowDetailClick(){this.setState({expand:!this.state.expand})}getDataSource(){const{dataSource:e=[]}=this.props;let t=[];return Object(o.forEach)(e,(e,n)=>{"index"in e||(e.index=n+1),t.push(e)}),t}render(){const{info:e,columns:t=[],loading:n=!1,emptyText:r="列表为空"}=this.props,l=Object(o.cloneDeep)(t),a=this.getDataSource();return-1===Object(o.findIndex)(l,["key","index"])&&l.unshift({key:"index",dataIndex:"index",title:"序号",width:65}),i.a.createElement("div",{className:"uw-detail-list"},i.a.createElement("div",null,i.a.createElement("div",{className:"uw-detail-info"},e,a.length>0&&i.a.createElement("span",{onClick:this.onShowDetailClick.bind(this)},i.a.createElement("a",null,"查看详情 ",this.state.expand?i.a.createElement(ie,{type:"arrow-up"}):i.a.createElement(ie,{type:"arrow-down"})))),this.state.expand?i.a.createElement(re,{loading:n,size:"middle",columns:l,dataSource:a,rowKey:"index",pagination:!1,scroll:{y:200},placeholder:!n&&0===a.length&&r}):void 0))}}n.d(t,"Upload",function(){return h}),n.d(t,"Placeholder",function(){return p}),n.d(t,"RawText",function(){return g}),n.d(t,"Process",function(){return C}),n.d(t,"PageLoading",function(){return S}),n.d(t,"Breadcrumb",function(){return E}),n.d(t,"ErrorMessage",function(){return N}),n.d(t,"Helper",function(){return O}),n.d(t,"DialogFooter",function(){return I}),n.d(t,"Filter",function(){return z}),n.d(t,"FileReader",function(){return G}),n.d(t,"FileUpload",function(){return K}),n.d(t,"ExtendInfo",function(){return te}),n.d(t,"ResourceSelector",function(){return ne}),n.d(t,"DetailList",function(){return oe})},function(e,t,n){(e.exports=n(3)(!1)).push([e.i,".uw-detail-list {\n  margin-bottom: 20px;\n}\n.uw-detail-list .uw-detail-info {\n  margin-bottom: 10px;\n}\n.uw-detail-list a {\n  margin-left: 5px;\n  text-decoration: none;\n}\n.uw-detail-list .tc-15-table-panel .tc-15-table-box th > div {\n  height: 30px;\n  line-height: 30px;\n}\n.uw-detail-list .tc-15-table-panel .tc-15-table-box td {\n  padding: 6px 20px;\n}\n",""])},function(e,t,n){var r=n(8);"string"==typeof r&&(r=[[e.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};n(2)(r,i);r.locals&&(e.exports=r.locals)},function(e,t,n){(e.exports=n(3)(!1)).push([e.i,".tc-15-mod-selector {\n  max-width: 950px;\n}\n.tc-15-option-cell .tc-15-option-hd .extra {\n  float: right;\n  text-align: right;\n}\n",""])},function(e,t,n){var r=n(10);"string"==typeof r&&(r=[[e.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};n(2)(r,i);r.locals&&(e.exports=r.locals)},function(e,t,n){(e.exports=n(3)(!1)).push([e.i,".uw-extend-layout {\n  display: inline-block;\n}\n.uw-group-title {\n  font-size: 12px;\n  margin-bottom: 5px;\n  margin-top: 20px;\n  color: #888888;\n}\n.uw-group-title > span {\n  display: inline-block;\n  width: 90%;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.uw-extend-content {\n  overflow-x: hidden;\n  overflow-y: auto;\n  max-height: 450px;\n}\n.uw-extend-content .tc-15-msg {\n  margin-bottom: 0;\n}\n",""])},function(e,t,n){var r=n(12);"string"==typeof r&&(r=[[e.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};n(2)(r,i);r.locals&&(e.exports=r.locals)},function(e,t,n){(e.exports=n(3)(!1)).push([e.i,".upload-helper {\n  margin-bottom: 10px;\n}\n.upload-helper .text-label {\n  height: 30px;\n  line-height: 30px;\n}\n.upload-helper .uw-plugins-error-message {\n  vertical-align: top;\n  margin-left: 10px;\n  height: 30px;\n  line-height: 30px;\n}\n.upload-btn-wrap {\n  background: #006eff;\n  border: 1px solid #006eff;\n  color: #fff;\n}\n.upload-btn-wrap:hover {\n  border-color: #0063e5;\n  background: #0063e5;\n  text-decoration: none;\n  color: #fff;\n}\n",""])},function(e,t,n){var r=n(14);"string"==typeof r&&(r=[[e.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};n(2)(r,i);r.locals&&(e.exports=r.locals)},function(e,t){!function(){function e(){this.Diff_Timeout=1,this.Diff_EditCost=4,this.Match_Threshold=.5,this.Match_Distance=1e3,this.Patch_DeleteThreshold=.5,this.Patch_Margin=4,this.Match_MaxBits=32}e.prototype.diff_main=function(e,t,n,r){if(void 0===r&&(r=this.Diff_Timeout<=0?Number.MAX_VALUE:(new Date).getTime()+1e3*this.Diff_Timeout),null==e||null==t)throw Error("Null input. (diff_main)");if(e==t)return e?[[0,e]]:[];void 0===n&&(n=!0);var i=n,o=this.diff_commonPrefix(e,t);n=e.substring(0,o),e=e.substring(o),t=t.substring(o);o=this.diff_commonSuffix(e,t);var l=e.substring(e.length-o);return e=e.substring(0,e.length-o),t=t.substring(0,t.length-o),e=this.diff_compute_(e,t,i,r),n&&e.unshift([0,n]),l&&e.push([0,l]),this.diff_cleanupMerge(e),e},e.prototype.diff_compute_=function(e,t,n,r){if(!e)return[[1,t]];if(!t)return[[-1,e]];let i=e.length>t.length?e:t,o=e.length>t.length?t:e,l=i.indexOf(o);return-1!=l?(n=[[1,i.substring(0,l)],[0,o],[1,i.substring(l+o.length)]],e.length>t.length&&(n[0][0]=n[2][0]=-1),n):1==o.length?[[-1,e],[1,t]]:(i=this.diff_halfMatch_(e,t))?(o=i[0],e=i[1],l=i[2],t=i[3],i=i[4],o=this.diff_main(o,l,n,r),n=this.diff_main(e,t,n,r),o.concat([[0,i]],n)):n&&e.length>100&&t.length>100?this.diff_lineMode_(e,t,r):this.diff_bisect_(e,t,r)},e.prototype.diff_lineMode_=function(e,t,n){let r=this.diff_linesToChars_(e,t);e=r.chars1,t=r.chars2,r=r.lineArray,e=this.diff_main(e,t,!1,n),this.diff_charsToLines_(e,r),this.diff_cleanupSemantic(e),e.push([0,""]);for(let i=r=t=0,o="",l="";t<e.length;){switch(e[t][0]){case 1:i++,l+=e[t][1];break;case-1:r++,o+=e[t][1];break;case 0:if(r>=1&&i>=1){for(e.splice(t-r-i,r+i),t=t-r-i,i=(r=this.diff_main(o,l,!1,n)).length-1;i>=0;i--)e.splice(t,0,r[i]);t+=r.length}r=i=0,l=o=""}t++}return e.pop(),e},e.prototype.diff_bisect_=function(e,t,n){for(var r=e.length,i=t.length,o=Math.ceil((r+i)/2),l=o,a=2*o,s=Array(a),c=Array(a),u=0;u<a;u++)s[u]=-1,c[u]=-1;s[l+1]=0,c[l+1]=0;for(var h=(u=r-i)%2!=0,f=0,d=0,p=0,g=0,m=0;m<o&&!((new Date).getTime()>n);m++){for(var v=-m+f;v<=m-d;v+=2){for(var b,y=l+v,x=(b=v==-m||v!=m&&s[y-1]<s[y+1]?s[y+1]:s[y-1]+1)-v;b<r&&x<i&&e.charAt(b)==t.charAt(x);)b++,x++;if(s[y]=b,b>r)d+=2;else if(x>i)f+=2;else if(h&&((y=l+u-v)>=0&&y<a&&-1!=c[y])){var w=r-c[y];if(b>=w)return this.diff_bisectSplit_(e,t,b,x,n)}}for(v=-m+p;v<=m-g;v+=2){for(y=l+v,b=(w=v==-m||v!=m&&c[y-1]<c[y+1]?c[y+1]:c[y-1]+1)-v;w<r&&b<i&&e.charAt(r-w-1)==t.charAt(i-b-1);)w++,b++;if(c[y]=w,w>r)g+=2;else if(b>i)p+=2;else if(!h&&((y=l+u-v)>=0&&y<a&&-1!=s[y]&&(x=l+(b=s[y])-y,b>=(w=r-w))))return this.diff_bisectSplit_(e,t,b,x,n)}}return[[-1,e],[1,t]]},e.prototype.diff_bisectSplit_=function(e,t,n,r,i){let o=e.substring(0,n),l=t.substring(0,r);return e=e.substring(n),t=t.substring(r),o=this.diff_main(o,l,!1,i),i=this.diff_main(e,t,!1,i),o.concat(i)},e.prototype.diff_linesToChars_=function(e,t){function n(e){for(var t="",n=0,o=-1,l=r.length;o<e.length-1;){-1==(o=e.indexOf("\n",n))&&(o=e.length-1);var a=e.substring(n,o+1);n=o+1;(i.hasOwnProperty?i.hasOwnProperty(a):void 0!==i[a])?t+=String.fromCharCode(i[a]):(t+=String.fromCharCode(l),i[a]=l,r[l++]=a)}return t}var r=[],i={};return r[0]="",{chars1:n(e),chars2:n(t),lineArray:r}},e.prototype.diff_charsToLines_=function(e,t){for(let o=0;o<e.length;o++){for(var n=e[o][1],r=[],i=0;i<n.length;i++)r[i]=t[n.charCodeAt(i)];e[o][1]=r.join("")}},e.prototype.diff_commonPrefix=function(e,t){if(!e||!t||e.charAt(0)!=t.charAt(0))return 0;for(var n=0,r=Math.min(e.length,t.length),i=r,o=0;n<i;)e.substring(o,i)==t.substring(o,i)?o=n=i:r=i,i=Math.floor((r-n)/2+n);return i},e.prototype.diff_commonSuffix=function(e,t){if(!e||!t||e.charAt(e.length-1)!=t.charAt(t.length-1))return 0;for(var n=0,r=Math.min(e.length,t.length),i=r,o=0;n<i;)e.substring(e.length-i,e.length-o)==t.substring(t.length-i,t.length-o)?o=n=i:r=i,i=Math.floor((r-n)/2+n);return i},e.prototype.diff_commonOverlap_=function(e,t){var n=e.length,r=t.length;if(0==n||0==r)return 0;if(n>r?e=e.substring(n-r):n<r&&(t=t.substring(0,n)),n=Math.min(n,r),e==t)return n;r=0;for(var i=1;;){var o=e.substring(n-i);if(-1==(o=t.indexOf(o)))return r;i+=o,0!=o&&e.substring(n-i)!=t.substring(0,i)||(r=i,i++)}},e.prototype.diff_halfMatch_=function(e,t){function n(e,t,n){for(var r,i,o,a,s=e.substring(n,n+Math.floor(e.length/4)),c=-1,u="";-1!=(c=t.indexOf(s,c+1));){let s=l.diff_commonPrefix(e.substring(n),t.substring(c)),h=l.diff_commonSuffix(e.substring(0,n),t.substring(0,c));u.length<h+s&&(u=t.substring(c-h,c)+t.substring(c,c+s),r=e.substring(0,n-h),i=e.substring(n+s),o=t.substring(0,c-h),a=t.substring(c+s))}return 2*u.length>=e.length?[r,i,o,a,u]:null}if(this.Diff_Timeout<=0)return null;var r=e.length>t.length?e:t,i=e.length>t.length?t:e;if(r.length<4||2*i.length<r.length)return null;var o,l=this,a=n(r,i,Math.ceil(r.length/4));r=n(r,i,Math.ceil(r.length/2));if(!a&&!r)return null;let s;return o=r?a&&a[4].length>r[4].length?a:r:a,e.length>t.length?(a=o[0],r=o[1],i=o[2],s=o[3]):(i=o[0],s=o[1],a=o[2],r=o[3]),[a,r,i,s,o=o[4]]},e.prototype.diff_cleanupSemantic=function(e){for(var t=!1,n=[],r=0,i=null,o=0,l=0,a=0,s=0,c=0;o<e.length;)0==e[o][0]?(n[r++]=o,l=s,a=c,c=s=0,i=e[o][1]):(1==e[o][0]?s+=e[o][1].length:c+=e[o][1].length,i&&i.length<=Math.max(l,a)&&i.length<=Math.max(s,c)&&(e.splice(n[r-1],0,[-1,i]),e[n[r-1]+1][0]=1,r--,o=--r>0?n[r-1]:-1,c=s=a=l=0,i=null,t=!0)),o++;for(t&&this.diff_cleanupMerge(e),this.diff_cleanupSemanticLossless(e),o=1;o<e.length;)-1==e[o-1][0]&&1==e[o][0]&&(t=e[o-1][1],n=e[o][1],(r=this.diff_commonOverlap_(t,n))>=(i=this.diff_commonOverlap_(n,t))?(r>=t.length/2||r>=n.length/2)&&(e.splice(o,0,[0,n.substring(0,r)]),e[o-1][1]=t.substring(0,t.length-r),e[o+1][1]=n.substring(r),o++):(i>=t.length/2||i>=n.length/2)&&(e.splice(o,0,[0,t.substring(0,i)]),e[o-1][0]=1,e[o-1][1]=n.substring(0,n.length-i),e[o+1][0]=-1,e[o+1][1]=t.substring(i),o++),o++),o++},e.prototype.diff_cleanupSemanticLossless=function(t){function n(t,n){if(!t||!n)return 6;var r=t.charAt(t.length-1),i=n.charAt(0),o=r.match(e.nonAlphaNumericRegex_),l=i.match(e.nonAlphaNumericRegex_),a=o&&r.match(e.whitespaceRegex_),s=l&&i.match(e.whitespaceRegex_),c=(r=a&&r.match(e.linebreakRegex_),i=s&&i.match(e.linebreakRegex_),r&&t.match(e.blanklineEndRegex_)),u=i&&n.match(e.blanklineStartRegex_);return c||u?5:r||i?4:o&&!a&&s?3:a||s?2:o||l?1:0}for(let e=1;e<t.length-1;){if(0==t[e-1][0]&&0==t[e+1][0]){var r=t[e-1][1],i=t[e][1],o=t[e+1][1];if(a=this.diff_commonSuffix(r,i)){var l=i.substring(i.length-a);r=r.substring(0,r.length-a),i=l+i.substring(0,i.length-a),o=l+o}for(var a=r,s=(l=i,o),c=n(r,i)+n(i,o);i.charAt(0)===o.charAt(0);){r=r+i.charAt(0),i=i.substring(1)+o.charAt(0),o=o.substring(1);var u=n(r,i)+n(i,o);u>=c&&(c=u,a=r,l=i,s=o)}t[e-1][1]!=a&&(a?t[e-1][1]=a:(t.splice(e-1,1),e--),t[e][1]=l,s?t[e+1][1]=s:(t.splice(e+1,1),e--))}e++}},e.nonAlphaNumericRegex_=/[^a-zA-Z0-9]/,e.whitespaceRegex_=/\s/,e.linebreakRegex_=/[\r\n]/,e.blanklineEndRegex_=/\n\r?\n$/,e.blanklineStartRegex_=/^\r?\n\r?\n/,e.prototype.diff_cleanupEfficiency=function(e){for(var t=!1,n=[],r=0,i=null,o=0,l=!1,a=!1,s=!1,c=!1;o<e.length;)0==e[o][0]?(e[o][1].length<this.Diff_EditCost&&(s||c)?(n[r++]=o,l=s,a=c,i=e[o][1]):(r=0,i=null),s=c=!1):(-1==e[o][0]?c=!0:s=!0,i&&(l&&a&&s&&c||i.length<this.Diff_EditCost/2&&l+a+s+c==3)&&(e.splice(n[r-1],0,[-1,i]),e[n[r-1]+1][0]=1,r--,i=null,l&&a?(s=c=!0,r=0):(o=--r>0?n[r-1]:-1,s=c=!1),t=!0)),o++;t&&this.diff_cleanupMerge(e)},e.prototype.diff_cleanupMerge=function(e){e.push([0,""]);for(var t,n=0,r=0,i=0,o="",l="";n<e.length;)switch(e[n][0]){case 1:i++,l+=e[n][1],n++;break;case-1:r++,o+=e[n][1],n++;break;case 0:r+i>1?(0!==r&&0!==i&&(0!==(t=this.diff_commonPrefix(l,o))&&(n-r-i>0&&0==e[n-r-i-1][0]?e[n-r-i-1][1]+=l.substring(0,t):(e.splice(0,0,[0,l.substring(0,t)]),n++),l=l.substring(t),o=o.substring(t)),0!==(t=this.diff_commonSuffix(l,o))&&(e[n][1]=l.substring(l.length-t)+e[n][1],l=l.substring(0,l.length-t),o=o.substring(0,o.length-t))),0===r?e.splice(n-i,r+i,[1,l]):0===i?e.splice(n-r,r+i,[-1,o]):e.splice(n-r-i,r+i,[-1,o],[1,l]),n=n-r-i+(r?1:0)+(i?1:0)+1):0!==n&&0==e[n-1][0]?(e[n-1][1]+=e[n][1],e.splice(n,1)):n++,r=i=0,l=o=""}for(""===e[e.length-1][1]&&e.pop(),r=!1,n=1;n<e.length-1;)0==e[n-1][0]&&0==e[n+1][0]&&(e[n][1].substring(e[n][1].length-e[n-1][1].length)==e[n-1][1]?(e[n][1]=e[n-1][1]+e[n][1].substring(0,e[n][1].length-e[n-1][1].length),e[n+1][1]=e[n-1][1]+e[n+1][1],e.splice(n-1,1),r=!0):e[n][1].substring(0,e[n+1][1].length)==e[n+1][1]&&(e[n-1][1]+=e[n+1][1],e[n][1]=e[n][1].substring(e[n+1][1].length)+e[n+1][1],e.splice(n+1,1),r=!0)),n++;r&&this.diff_cleanupMerge(e)},e.prototype.diff_xIndex=function(e,t){let n,r=0,i=0,o=0,l=0;for(n=0;n<e.length&&(1!==e[n][0]&&(r+=e[n][1].length),-1!==e[n][0]&&(i+=e[n][1].length),!(r>t));n++)o=r,l=i;return e.length!=n&&-1===e[n][0]?l:l+(t-o)},e.prototype.diff_prettyHtml=function(e){for(var t=[],n=/&/g,r=/</g,i=/>/g,o=/\n/g,l=0;l<e.length;l++){var a=e[l][0],s=(s=e[l][1]).replace(n,"&amp;").replace(r,"&lt;").replace(i,"&gt;").replace(o,"&para;<br>");switch(a){case 1:t[l]=`<ins style="background:#e6ffe6;">${s}</ins>`;break;case-1:t[l]=`<del style="background:#ffe6e6;">${s}</del>`;break;case 0:t[l]=`<span>${s}</span>`}}return t.join("")},e.prototype.diff_text1=function(e){for(var t=[],n=0;n<e.length;n++)1!==e[n][0]&&(t[n]=e[n][1]);return t.join("")},e.prototype.diff_text2=function(e){for(var t=[],n=0;n<e.length;n++)-1!==e[n][0]&&(t[n]=e[n][1]);return t.join("")},e.prototype.diff_levenshtein=function(e){for(var t=0,n=0,r=0,i=0;i<e.length;i++){let o=e[i][0],l=e[i][1];switch(o){case 1:n+=l.length;break;case-1:r+=l.length;break;case 0:t+=Math.max(n,r),r=n=0}}return t+Math.max(n,r)},e.prototype.diff_toDelta=function(e){for(var t=[],n=0;n<e.length;n++)switch(e[n][0]){case 1:t[n]=`+${encodeURI(e[n][1])}`;break;case-1:t[n]=`-${e[n][1].length}`;break;case 0:t[n]=`=${e[n][1].length}`}return t.join("\t").replace(/%20/g," ")},e.prototype.diff_fromDelta=function(e,t){for(var n=[],r=0,i=0,o=t.split(/\t/g),l=0;l<o.length;l++){let t=o[l].substring(1);switch(o[l].charAt(0)){case"+":try{n[r++]=[1,decodeURI(t)]}catch(e){throw Error(`Illegal escape in diff_fromDelta: ${t}`)}break;case"-":case"=":var a=parseInt(t,10);if(isNaN(a)||a<0)throw Error(`Invalid number in diff_fromDelta: ${t}`);t=e.substring(i,i+=a),"="==o[l].charAt(0)?n[r++]=[0,t]:n[r++]=[-1,t];break;default:if(o[l])throw Error(`Invalid diff operation in diff_fromDelta: ${o[l]}`)}}if(i!=e.length)throw Error(`Delta length (${i}) does not equal source text length (${e.length}).`);return n},e.prototype.match_main=function(e,t,n){if(null==e||null==t||null==n)throw Error("Null input. (match_main)");return n=Math.max(0,Math.min(n,e.length)),e==t?0:e.length?e.substring(n,n+t.length)==t?n:this.match_bitap_(e,t,n):-1},e.prototype.match_bitap_=function(e,t,n){function r(e,r){let i=e/t.length,l=Math.abs(n-r);return o.Match_Distance?i+l/o.Match_Distance:l?1:i}if(t.length>this.Match_MaxBits)throw Error("Pattern too long for this browser.");var i=this.match_alphabet_(t),o=this,l=this.Match_Threshold;-1!=(h=e.indexOf(t,n))&&(l=Math.min(r(0,h),l),-1!=(h=e.lastIndexOf(t,n+t.length))&&(l=Math.min(r(0,h),l)));for(var a,s,c,u=1<<t.length-1,h=-1,f=t.length+e.length,d=0;d<t.length;d++){for(a=0,s=f;a<s;)r(d,n+s)<=l?a=s:f=s,s=Math.floor((f-a)/2+a);f=s,a=Math.max(1,n-s+1);let o=Math.min(n+s,e.length)+t.length;for((s=Array(o+2))[o+1]=(1<<d)-1;o>=a;o--){let t=i[e.charAt(o-1)];if(s[o]=0===d?(s[o+1]<<1|1)&t:(s[o+1]<<1|1)&t|(c[o+1]|c[o])<<1|1|c[o+1],s[o]&u&&(t=r(d,o-1))<=l){if(l=t,!((h=o-1)>n))break;a=Math.max(1,2*n-h)}}if(r(d+1,n)>l)break;c=s}return h},e.prototype.match_alphabet_=function(e){for(var t={},n=0;n<e.length;n++)t[e.charAt(n)]=0;for(n=0;n<e.length;n++)t[e.charAt(n)]|=1<<e.length-n-1;return t},e.prototype.patch_addContext_=function(e,t){if(0!=t.length){for(var n=t.substring(e.start2,e.start2+e.length1),r=0;t.indexOf(n)!=t.lastIndexOf(n)&&n.length<this.Match_MaxBits-this.Patch_Margin-this.Patch_Margin;)r+=this.Patch_Margin,n=t.substring(e.start2-r,e.start2+e.length1+r);r+=this.Patch_Margin,(n=t.substring(e.start2-r,e.start2))&&e.diffs.unshift([0,n]),(r=t.substring(e.start2+e.length1,e.start2+e.length1+r))&&e.diffs.push([0,r]),e.start1-=n.length,e.start2-=n.length,e.length1+=n.length+r.length,e.length2+=n.length+r.length}},e.prototype.patch_make=function(t,n,r){let i;if("string"==typeof t&&"string"==typeof n&&void 0===r)i=t,(n=this.diff_main(i,n,!0)).length>2&&(this.diff_cleanupSemantic(n),this.diff_cleanupEfficiency(n));else if(t&&"object"==typeof t&&void 0===n&&void 0===r)n=t,i=this.diff_text1(n);else if("string"==typeof t&&n&&"object"==typeof n&&void 0===r)i=t;else{if("string"!=typeof t||"string"!=typeof n||!r||"object"!=typeof r)throw Error("Unknown call format to patch_make.");i=t,n=r}if(0===n.length)return[];r=[],t=new e.patch_obj;for(var o=0,l=0,a=0,s=i,c=0;c<n.length;c++){let u=n[c][0],h=n[c][1];switch(!o&&0!==u&&(t.start1=l,t.start2=a),u){case 1:t.diffs[o++]=n[c],t.length2+=h.length,i=i.substring(0,a)+h+i.substring(a);break;case-1:t.length1+=h.length,t.diffs[o++]=n[c],i=i.substring(0,a)+i.substring(a+h.length);break;case 0:h.length<=2*this.Patch_Margin&&o&&n.length!=c+1?(t.diffs[o++]=n[c],t.length1+=h.length,t.length2+=h.length):h.length>=2*this.Patch_Margin&&o&&(this.patch_addContext_(t,s),r.push(t),t=new e.patch_obj,o=0,s=i,l=a)}1!==u&&(l+=h.length),-1!==u&&(a+=h.length)}return o&&(this.patch_addContext_(t,s),r.push(t)),r},e.prototype.patch_deepCopy=function(t){for(var n=[],r=0;r<t.length;r++){let i=t[r],o=new e.patch_obj;o.diffs=[];for(let e=0;e<i.diffs.length;e++)o.diffs[e]=i.diffs[e].slice();o.start1=i.start1,o.start2=i.start2,o.length1=i.length1,o.length2=i.length2,n[r]=o}return n},e.prototype.patch_apply=function(e,t){if(0==e.length)return[t,[]];e=this.patch_deepCopy(e);let n=this.patch_addPadding(e);t=n+t+n,this.patch_splitMax(e);for(var r=0,i=[],o=0;o<e.length;o++){var l,a=e[o].start2+r,s=-1;if((u=this.diff_text1(e[o].diffs)).length>this.Match_MaxBits?-1!=(l=this.match_main(t,u.substring(0,this.Match_MaxBits),a))&&(-1==(s=this.match_main(t,u.substring(u.length-this.Match_MaxBits),a+u.length-this.Match_MaxBits))||l>=s)&&(l=-1):l=this.match_main(t,u,a),-1==l)i[o]=!1,r-=e[o].length2-e[o].length1;else if(i[o]=!0,r=l-a,u==(a=-1==s?t.substring(l,l+u.length):t.substring(l,s+this.Match_MaxBits)))t=t.substring(0,l)+this.diff_text2(e[o].diffs)+t.substring(l+u.length);else if(a=this.diff_main(u,a,!1),u.length>this.Match_MaxBits&&this.diff_levenshtein(a)/u.length>this.Patch_DeleteThreshold)i[o]=!1;else{this.diff_cleanupSemanticLossless(a);var c,u=0;for(s=0;s<e[o].diffs.length;s++){let n=e[o].diffs[s];0!==n[0]&&(c=this.diff_xIndex(a,u)),1===n[0]?t=t.substring(0,l+c)+n[1]+t.substring(l+c):-1===n[0]&&(t=t.substring(0,l+c)+t.substring(l+this.diff_xIndex(a,u+n[1].length))),-1!==n[0]&&(u+=n[1].length)}}}return[t=t.substring(n.length,t.length-n.length),i]},e.prototype.patch_addPadding=function(e){for(var t=this.Patch_Margin,n="",r=1;r<=t;r++)n+=String.fromCharCode(r);for(r=0;r<e.length;r++)e[r].start1+=t,e[r].start2+=t;var i=(r=e[0]).diffs;if(0==i.length||0!=i[0][0])i.unshift([0,n]),r.start1-=t,r.start2-=t,r.length1+=t,r.length2+=t;else if(t>i[0][1].length){var o=t-i[0][1].length;i[0][1]=n.substring(i[0][1].length)+i[0][1],r.start1-=o,r.start2-=o,r.length1+=o,r.length2+=o}return 0==(i=(r=e[e.length-1]).diffs).length||0!=i[i.length-1][0]?(i.push([0,n]),r.length1+=t,r.length2+=t):t>i[i.length-1][1].length&&(o=t-i[i.length-1][1].length,i[i.length-1][1]+=n.substring(0,o),r.length1+=o,r.length2+=o),n},e.prototype.patch_splitMax=function(t){for(let l=this.Match_MaxBits,a=0;a<t.length;a++)if(!(t[a].length1<=l)){let s=t[a];t.splice(a--,1);for(var n=s.start1,r=s.start2,i="";0!==s.diffs.length;){let c=new e.patch_obj,u=!0;for(c.start1=n-i.length,c.start2=r-i.length,""!==i&&(c.length1=c.length2=i.length,c.diffs.push([0,i]));0!==s.diffs.length&&c.length1<l-this.Patch_Margin;){i=s.diffs[0][0];var o=s.diffs[0][1];1===i?(c.length2+=o.length,r+=o.length,c.diffs.push(s.diffs.shift()),u=!1):-1===i&&1==c.diffs.length&&0==c.diffs[0][0]&&o.length>2*l?(c.length1+=o.length,n+=o.length,u=!1,c.diffs.push([i,o]),s.diffs.shift()):(o=o.substring(0,l-c.length1-this.Patch_Margin),c.length1+=o.length,n+=o.length,0===i?(c.length2+=o.length,r+=o.length):u=!1,c.diffs.push([i,o]),o==s.diffs[0][1]?s.diffs.shift():s.diffs[0][1]=s.diffs[0][1].substring(o.length))}i=(i=this.diff_text2(c.diffs)).substring(i.length-this.Patch_Margin),""!==(o=this.diff_text1(s.diffs).substring(0,this.Patch_Margin))&&(c.length1+=o.length,c.length2+=o.length,0!==c.diffs.length&&0===c.diffs[c.diffs.length-1][0]?c.diffs[c.diffs.length-1][1]+=o:c.diffs.push([0,o])),u||t.splice(++a,0,c)}}},e.prototype.patch_toText=function(e){for(var t=[],n=0;n<e.length;n++)t[n]=e[n];return t.join("")},e.prototype.patch_fromText=function(t){let n=[];if(!t)return n;t=t.split("\n");for(let i=0,o=/^@@ -(\d+),?(\d*) \+(\d+),?(\d*) @@$/;i<t.length;){let l=t[i].match(o);if(!l)throw Error(`Invalid patch string: ${t[i]}`);let a=new e.patch_obj;for(n.push(a),a.start1=parseInt(l[1],10),""===l[2]?(a.start1--,a.length1=1):"0"==l[2]?a.length1=0:(a.start1--,a.length1=parseInt(l[2],10)),a.start2=parseInt(l[3],10),""===l[4]?(a.start2--,a.length2=1):"0"==l[4]?a.length2=0:(a.start2--,a.length2=parseInt(l[4],10)),i++;i<t.length;){l=t[i].charAt(0);try{var r=decodeURI(t[i].substring(1))}catch(e){throw Error(`Illegal escape in patch_fromText: ${r}`)}if("-"==l)a.diffs.push([-1,r]);else if("+"==l)a.diffs.push([1,r]);else if(" "==l)a.diffs.push([0,r]);else{if("@"==l)break;if(""!==l)throw Error(`Invalid patch mode "${l}" in: ${r}`)}i++}}return n},e.patch_obj=function(){this.diffs=[],this.start2=this.start1=null,this.length2=this.length1=0},e.patch_obj.prototype.toString=function(){let e,t,n;for(e=[`@@ -${e=0===this.length1?`${this.start1},0`:1==this.length1?this.start1+1:`${this.start1+1},${this.length1}`} +${t=0===this.length2?`${this.start2},0`:1==this.length2?this.start2+1:`${this.start2+1},${this.length2}`} @@\n`],t=0;t<this.diffs.length;t++){switch(this.diffs[t][0]){case 1:n="+";break;case-1:n="-";break;case 0:n=" "}e[t+1]=`${n+encodeURI(this.diffs[t][1])}\n`}return e.join("").replace(/%20/g," ")},window.diff_match_patch=e,window.DIFF_DELETE=-1,window.DIFF_INSERT=1,window.DIFF_EQUAL=0}()},function(e,t,n){!function(e){"use strict";var t=e.Pos,n="http://www.w3.org/2000/svg";function r(e,t){this.mv=e,this.type=t,this.classes="left"==t?{chunk:"CodeMirror-merge-l-chunk",start:"CodeMirror-merge-l-chunk-start",end:"CodeMirror-merge-l-chunk-end",insert:"CodeMirror-merge-l-inserted",del:"CodeMirror-merge-l-deleted",connect:"CodeMirror-merge-l-connect"}:{chunk:"CodeMirror-merge-r-chunk",start:"CodeMirror-merge-r-chunk-start",end:"CodeMirror-merge-r-chunk-end",insert:"CodeMirror-merge-r-inserted",del:"CodeMirror-merge-r-deleted",connect:"CodeMirror-merge-r-connect"}}function i(t){t.diffOutOfDate&&(t.diff=E(t.orig.getValue(),t.edit.getValue(),t.mv.options.ignoreWhitespace),t.chunks=T(t.diff),t.diffOutOfDate=!1,e.signal(t.edit,"updateDiff",t.diff))}r.prototype={constructor:r,init:function(t,n,r){this.edit=this.mv.edit,(this.edit.state.diffViews||(this.edit.state.diffViews=[])).push(this),this.orig=e(t,_({value:n,readOnly:!this.mv.options.allowEditingOriginals},_(r))),"align"==this.mv.options.connect&&(this.edit.state.trackAlignable||(this.edit.state.trackAlignable=new U(this.edit)),this.orig.state.trackAlignable=new U(this.orig)),this.orig.state.diffViews=[this];var i=r.chunkClassLocation||"background";"[object Array]"!=Object.prototype.toString.call(i)&&(i=[i]),this.classes.classLocation=i,this.diff=E(L(n),L(r.value),this.mv.options.ignoreWhitespace),this.chunks=T(this.diff),this.diffOutOfDate=this.dealigned=!1,this.needsScrollSync=null,this.showDifferences=!1!==r.showDifferences},registerEvents:function(t){this.forceUpdate=function(t){var n,r={from:0,to:0,marked:[]},a={from:0,to:0,marked:[]},s=!1;function c(e){o=!0,s=!1,"full"==e&&(t.svg&&W(t.svg),t.copyButtons&&W(t.copyButtons),u(t.edit,r.marked,t.classes),u(t.orig,a.marked,t.classes),r.from=r.to=a.from=a.to=0),i(t),t.showDifferences&&(h(t.edit,t.diff,r,DIFF_INSERT,t.classes),h(t.orig,t.diff,a,DIFF_DELETE,t.classes)),"align"==t.mv.options.connect&&b(t),p(t),null!=t.needsScrollSync&&l(t,t.needsScrollSync),o=!1}function f(e){o||(t.dealigned=!0,d(e))}function d(e){o||s||(clearTimeout(n),!0===e&&(s=!0),n=setTimeout(c,!0===e?20:250))}function g(e,n){t.diffOutOfDate||(t.diffOutOfDate=!0,r.from=r.to=a.from=a.to=0),f(n.text.length-1!=n.to.line-n.from.line)}function m(){t.diffOutOfDate=!0,t.dealigned=!0,c("full")}return t.edit.on("change",g),t.orig.on("change",g),t.edit.on("swapDoc",m),t.orig.on("swapDoc",m),"align"==t.mv.options.connect&&(e.on(t.edit.state.trackAlignable,"realign",f),e.on(t.orig.state.trackAlignable,"realign",f)),t.edit.on("viewportChange",function(){d(!1)}),t.orig.on("viewportChange",function(){d(!1)}),c(),c}(this),s(this,!0,!1),function(e,t){e.edit.on("scroll",function(){l(e,!0)&&p(e)}),e.orig.on("scroll",function(){l(e,!1)&&p(e),t&&l(t,!0)&&p(t)})}(this,t)},setShowDifferences:function(e){(e=!1!==e)!=this.showDifferences&&(this.showDifferences=e,this.forceUpdate("full"))}};var o=!1;function l(e,t){if(e.diffOutOfDate)return e.lockScroll&&null==e.needsScrollSync&&(e.needsScrollSync=t),!1;if(e.needsScrollSync=null,!e.lockScroll)return!0;var n,r,i=+new Date;if(t?(n=e.edit,r=e.orig):(n=e.orig,r=e.edit),n.state.scrollSetBy==e&&(n.state.scrollSetAt||0)+250>i)return!1;var o=n.getScrollInfo();if("align"==e.mv.options.connect)m=o.top;else{var l,s,c=.5*o.clientHeight,u=o.top+c,h=n.lineAtHeight(u,"local"),f=function(e,t,n){for(var r,i,o,l,a=0;a<e.length;a++){var s=e[a],c=n?s.editFrom:s.origFrom,u=n?s.editTo:s.origTo;null==i&&(c>t?(i=s.editFrom,l=s.origFrom):u>t&&(i=s.editTo,l=s.origTo)),u<=t?(r=s.editTo,o=s.origTo):c<=t&&(r=s.editFrom,o=s.origFrom)}return{edit:{before:r,after:i},orig:{before:o,after:l}}}(e.chunks,h,t),d=a(n,t?f.edit:f.orig),p=a(r,t?f.orig:f.edit),g=(u-d.top)/(d.bot-d.top),m=p.top-c+g*(p.bot-p.top);if(m>o.top&&(s=o.top/c)<1)m=m*s+o.top*(1-s);else if((l=o.height-o.clientHeight-o.top)<c){var v=r.getScrollInfo(),b=v.height-v.clientHeight-m;b>l&&(s=l/c)<1&&(m=m*s+(v.height-v.clientHeight-l)*(1-s))}}return r.scrollTo(o.left,m),r.state.scrollSetAt=i,r.state.scrollSetBy=e,!0}function a(e,t){var n=t.after;return null==n&&(n=e.lastLine()+1),{top:e.heightAtLine(t.before||0,"local"),bot:e.heightAtLine(n,"local")}}function s(t,n,r){t.lockScroll=n,n&&0!=r&&l(t,DIFF_INSERT)&&p(t),(n?e.addClass:e.rmClass)(t.lockButton,"CodeMirror-merge-scrolllock-enabled")}function c(e,t,n){for(var r=n.classLocation,i=0;i<r.length;i++)e.removeLineClass(t,r[i],n.chunk),e.removeLineClass(t,r[i],n.start),e.removeLineClass(t,r[i],n.end)}function u(t,n,r){for(var i=0;i<n.length;++i){var o=n[i];o instanceof e.TextMarker?o.clear():o.parent&&c(t,o,r)}n.length=0}function h(e,t,n,r,i){var o=e.getViewport();e.operation(function(){n.from==n.to||o.from-n.to>20||n.from-o.to>20?(u(e,n.marked,i),d(e,t,r,n.marked,o.from,o.to,i),n.from=o.from,n.to=o.to):(o.from<n.from&&(d(e,t,r,n.marked,o.from,n.from,i),n.from=o.from),o.to>n.to&&(d(e,t,r,n.marked,n.to,o.to,i),n.to=o.to))})}function f(e,t,n,r,i,o){for(var l=n.classLocation,a=e.getLineHandle(t),s=0;s<l.length;s++)r&&e.addLineClass(a,l[s],n.chunk),i&&e.addLineClass(a,l[s],n.start),o&&e.addLineClass(a,l[s],n.end);return a}function d(e,n,r,i,o,l,a){var s=t(0,0),c=t(o,0),u=e.clipPos(t(l-1)),h=r==DIFF_DELETE?a.del:a.insert;function d(t,n){for(var r=Math.max(o,t),s=Math.min(l,n),c=r;c<s;++c)i.push(f(e,c,a,!0,c==t,c==n-1));t==n&&r==n&&s==n&&(r?i.push(f(e,r-1,a,!1,!1,!0)):i.push(f(e,r,a,!1,!0,!1)))}for(var p=0,g=!1,m=0;m<n.length;++m){var v=n[m],b=v[0],y=v[1];if(b==DIFF_EQUAL){var x=s.line+(A(n,m)?0:1);R(s,y);var w=s.line+(N(n,m)?1:0);w>x&&(g&&(d(p,x),g=!1),p=w)}else if(g=!0,b==r){var C=R(s,y,!0),k=G(c,s),M=j(u,C);V(k,M)||i.push(e.markText(k,M,{className:h})),s=C}}g&&d(p,s.line+1)}function p(e){if(e.showDifferences){if(e.svg){W(e.svg);var t=e.gap.offsetWidth;P(e.svg,"width",t,"height",e.gap.offsetHeight)}e.copyButtons&&W(e.copyButtons);for(var n=e.edit.getViewport(),r=e.orig.getViewport(),i=e.mv.wrap.getBoundingClientRect().top,o=i-e.edit.getScrollerElement().getBoundingClientRect().top+e.edit.getScrollInfo().top,l=i-e.orig.getScrollerElement().getBoundingClientRect().top+e.orig.getScrollInfo().top,a=0;a<e.chunks.length;a++){var s=e.chunks[a];s.editFrom<=n.to&&s.editTo>=n.from&&s.origFrom<=r.to&&s.origTo>=r.from&&w(e,s,l,o,t)}}}function g(e,t){for(var n=0,r=0,i=0;i<t.length;i++){var o=t[i];if(o.editTo>e&&o.editFrom<=e)return null;if(o.editFrom>e)break;n=o.editTo,r=o.origTo}return r+(e-n)}function m(e,t,n){for(var r=e.state.trackAlignable,i=e.firstLine(),o=0,l=[],a=0;;a++){for(var s=t[a],c=s?n?s.origFrom:s.editFrom:1e9;o<r.alignable.length;o+=2){var u=r.alignable[o]+1;if(!(u<=i)){if(!(u<=c))break;l.push(u)}}if(!s)break;l.push(i=n?s.origTo:s.editTo)}return l}function v(e,t,n,r){var i=0,o=0,l=0,a=0;e:for(;;i++){var s=e[i],c=t[o];if(!s&&null==c)break;for(var u=s?s[0]:1e9,h=null==c?1e9:c;l<n.length;){var f=n[l];if(f.origFrom<=h&&f.origTo>h){o++,i--;continue e}if(f.editTo>u){if(f.editFrom<=u)continue e;break}a+=f.origTo-f.origFrom-(f.editTo-f.editFrom),l++}if(u==h-a)s[r]=h,o++;else if(u<h-a)s[r]=u+a;else{var d=[h-a,null,null];d[r]=h,e.splice(i,0,d),o++}}}function b(e,t){if(e.dealigned||t){if(!e.orig.curOp)return e.orig.operation(function(){b(e,t)});e.dealigned=!1;var n=e.mv.left==e?e.mv.right:e.mv.left;n&&(i(n),n.dealigned=!1);for(var r=function(e,t){var n=m(e.edit,e.chunks,!1),r=[];if(t)for(var i=0,o=0;i<t.chunks.length;i++){for(var l=t.chunks[i].editTo;o<n.length&&n[o]<l;)o++;o!=n.length&&n[o]==l||n.splice(o++,0,l)}for(var i=0;i<n.length;i++)r.push([n[i],null,null]);return v(r,m(e.orig,e.chunks,!0),e.chunks,1),t&&v(r,m(t.orig,t.chunks,!0),t.chunks,2),r}(e,n),o=e.mv.aligners,l=0;l<o.length;l++)o[l].clear();o.length=0;var a=[e.edit,e.orig],s=[];n&&a.push(n.orig);for(var l=0;l<a.length;l++)s.push(a[l].getScrollInfo().top);for(var c=0;c<r.length;c++)y(a,r[c],o);for(var l=0;l<a.length;l++)a[l].scrollTo(null,s[l])}}function y(e,t,n){for(var r=0,i=[],o=0;o<e.length;o++)if(null!=t[o]){var l=e[o].heightAtLine(t[o],"local");i[o]=l,r=Math.max(r,l)}for(var o=0;o<e.length;o++)if(null!=t[o]){var a=r-i[o];a>1&&n.push(x(e[o],t[o],a))}}function x(e,t,n){var r=!0;t>e.lastLine()&&(t--,r=!1);var i=document.createElement("div");return i.className="CodeMirror-merge-spacer",i.style.height=n+"px",i.style.minWidth="1px",e.addLineWidget(t,i,{height:n,above:r,mergeSpacer:!0,handleMouseEvents:!0})}function w(e,t,r,i,o){var l="left"==e.type,a=e.orig.heightAtLine(t.origFrom,"local",!0)-r;if(e.svg){var s=a,c=e.edit.heightAtLine(t.editFrom,"local",!0)-i;if(l){var u=s;s=c,c=u}var h=e.orig.heightAtLine(t.origTo,"local",!0)-r,f=e.edit.heightAtLine(t.editTo,"local",!0)-i;if(l){var u=h;h=f,f=u}var d=" C "+o/2+" "+c+" "+o/2+" "+s+" "+(o+2)+" "+s,p=" C "+o/2+" "+h+" "+o/2+" "+f+" -1 "+f;P(e.svg.appendChild(document.createElementNS(n,"path")),"d","M -1 "+c+d+" L "+(o+2)+" "+h+p+" z","class",e.classes.connect)}if(e.copyButtons){var g=e.copyButtons.appendChild(I("div","left"==e.type?"⇝":"⇜","CodeMirror-merge-copy")),m=e.mv.options.allowEditingOriginals;if(g.title=m?"Push to left":"Revert chunk",g.chunk=t,g.style.top=(t.origTo>t.origFrom?a:e.edit.heightAtLine(t.editFrom,"local")-i)+"px",m){var v=e.edit.heightAtLine(t.editFrom,"local")-i,b=e.copyButtons.appendChild(I("div","right"==e.type?"⇝":"⇜","CodeMirror-merge-copy-reverse"));b.title="Push to right",b.chunk={editFrom:t.origFrom,editTo:t.origTo,origFrom:t.editFrom,origTo:t.editTo},b.style.top=v+"px","right"==e.type?b.style.left="2px":b.style.right="2px"}}}function C(e,n,r,i){if(!e.diffOutOfDate){var o=i.origTo>r.lastLine()?t(i.origFrom-1):t(i.origFrom,0),l=t(i.origTo,0),a=i.editTo>n.lastLine()?t(i.editFrom-1):t(i.editFrom,0),s=t(i.editTo,0),c=e.mv.options.revertChunk;c?c(e.mv,r,o,l,n,a,s):n.replaceRange(r.getRange(o,l),a,s)}}var k,M=e.MergeView=function(t,n){if(!(this instanceof M))return new M(t,n);this.options=n;var i=n.origLeft,o=null==n.origRight?n.orig:n.origRight,l=null!=i,a=null!=o,s=1+(l?1:0)+(a?1:0),c=[],u=this.left=null,h=this.right=null,f=this;if(l){u=this.left=new r(this,"left");var d=I("div",null,"CodeMirror-merge-pane CodeMirror-merge-left");c.push(d),c.push(S(u))}var m=I("div",null,"CodeMirror-merge-pane CodeMirror-merge-editor");if(c.push(m),a){h=this.right=new r(this,"right"),c.push(S(h));var v=I("div",null,"CodeMirror-merge-pane CodeMirror-merge-right");c.push(v)}(a?v:m).className+=" CodeMirror-merge-pane-rightmost",c.push(I("div",null,null,"height: 0; clear: both;"));var y=this.wrap=t.appendChild(I("div",c,"CodeMirror-merge CodeMirror-merge-"+s+"pane"));this.edit=e(m,_(n)),u&&u.init(d,i,n),h&&h.init(v,o,n),n.collapseIdentical&&this.editor().operation(function(){!function(e,t){"number"!=typeof t&&(t=2);for(var n=[],r=e.editor(),i=r.firstLine(),o=i,l=r.lastLine();o<=l;o++)n.push(!0);e.left&&F(e.left,t,i,n),e.right&&F(e.right,t,i,n);for(var a=0;a<n.length;a++)if(n[a]){for(var s=a+i,c=1;a<n.length-1&&n[a+1];a++,c++);if(c>t){var u=[{line:s,cm:r}];e.left&&u.push({line:g(s,e.left.chunks),cm:e.left.orig}),e.right&&u.push({line:g(s,e.right.chunks),cm:e.right.orig});var h=D(c,u);e.options.onCollapse&&e.options.onCollapse(e,s,c,h)}}}(f,n.collapseIdentical)}),"align"==n.connect&&(this.aligners=[],b(this.left||this.right,!0)),u&&u.registerEvents(h),h&&h.registerEvents(u);var x=function(){u&&p(u),h&&p(h)};e.on(window,"resize",x);var w=setInterval(function(){for(var t=y.parentNode;t&&t!=document.body;t=t.parentNode);t||(clearInterval(w),e.off(window,"resize",x))},5e3)};function S(t){var r=t.lockButton=I("div",null,"CodeMirror-merge-scrolllock");r.title="Toggle locked scrolling";var i=I("div",[r],"CodeMirror-merge-scrolllock-wrap");e.on(r,"click",function(){s(t,!t.lockScroll)});var o=[i];if(!1!==t.mv.options.revertButtons&&(t.copyButtons=I("div",null,"CodeMirror-merge-copybuttons-"+t.type),e.on(t.copyButtons,"click",function(e){var n=e.target||e.srcElement;n.chunk&&("CodeMirror-merge-copy-reverse"!=n.className?C(t,t.edit,t.orig,n.chunk):C(t,t.orig,t.edit,n.chunk))}),o.unshift(t.copyButtons)),"align"!=t.mv.options.connect){var l=document.createElementNS&&document.createElementNS(n,"svg");l&&!l.createSVGRect&&(l=null),t.svg=l,l&&o.push(l)}return t.gap=I("div",o,"CodeMirror-merge-gap")}function L(e){return"string"==typeof e?e:e.getValue()}function E(e,t,n){k||(k=new diff_match_patch);for(var r=k.diff_main(e,t),i=0;i<r.length;++i){var o=r[i];(n?/[^ \t]/.test(o[1]):o[1])?i&&r[i-1][0]==o[0]&&(r.splice(i--,1),r[i][1]+=o[1]):r.splice(i--,1)}return r}function T(e){var n=[];if(!e.length)return n;for(var r=0,i=0,o=t(0,0),l=t(0,0),a=0;a<e.length;++a){var s=e[a],c=s[0];if(c==DIFF_EQUAL){var u=!A(e,a)||o.line<r||l.line<i?1:0,h=o.line+u,f=l.line+u;R(o,s[1],null,l);var d=N(e,a)?1:0,p=o.line+d,g=l.line+d;p>h&&(a&&n.push({origFrom:i,origTo:f,editFrom:r,editTo:h}),r=p,i=g)}else R(c==DIFF_INSERT?o:l,s[1])}return(r<=o.line||i<=l.line)&&n.push({origFrom:i,origTo:l.line+1,editFrom:r,editTo:o.line+1}),n}function N(e,t){if(t==e.length-1)return!0;var n=e[t+1][1];return!(1==n.length&&t<e.length-2||10!=n.charCodeAt(0))&&(t==e.length-2||((n=e[t+2][1]).length>1||t==e.length-3)&&10==n.charCodeAt(0))}function A(e,t){if(0==t)return!0;var n=e[t-1][1];return 10==n.charCodeAt(n.length-1)&&(1==t||10==(n=e[t-2][1]).charCodeAt(n.length-1))}function O(n,r,i){n.addLineClass(r,"wrap","CodeMirror-merge-collapsed-line");var o=document.createElement("span");o.className="CodeMirror-merge-collapsed-widget",o.title="Identical text collapsed. Click to expand.";var l=n.markText(t(r,0),t(i-1),{inclusiveLeft:!0,inclusiveRight:!0,replacedWith:o,clearOnEnter:!0});function a(){l.clear(),n.removeLineClass(r,"wrap","CodeMirror-merge-collapsed-line")}return l.explicitlyCleared&&a(),e.on(o,"click",a),l.on("clear",a),e.on(o,"click",a),{mark:l,clear:a}}function D(e,t){var n=[];function r(){for(var e=0;e<n.length;e++)n[e].clear()}for(var i=0;i<t.length;i++){var o=t[i],l=O(o.cm,o.line,o.line+e);n.push(l),l.mark.on("clear",r)}return n[0].mark}function F(e,t,n,r){for(var i=0;i<e.chunks.length;i++)for(var o=e.chunks[i],l=o.editFrom-t;l<o.editTo+t;l++){var a=l+n;a>=0&&a<r.length&&(r[a]=!1)}}function I(e,t,n,r){var i=document.createElement(e);if(n&&(i.className=n),r&&(i.style.cssText=r),"string"==typeof t)i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function W(e){for(var t=e.childNodes.length;t>0;--t)e.removeChild(e.firstChild)}function P(e){for(var t=1;t<arguments.length;t+=2)e.setAttribute(arguments[t],arguments[t+1])}function _(e,t){for(var n in t||(t={}),e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}function R(e,n,r,i){for(var o=r?t(e.line,e.ch):e,l=0;;){var a=n.indexOf("\n",l);if(-1==a)break;++o.line,i&&++i.line,l=a+1}return o.ch=(l?0:o.ch)+(n.length-l),i&&(i.ch=(l?0:i.ch)+(n.length-l)),o}M.prototype={constructor:M,editor:function(){return this.edit},rightOriginal:function(){return this.right&&this.right.orig},leftOriginal:function(){return this.left&&this.left.orig},setShowDifferences:function(e){this.right&&this.right.setShowDifferences(e),this.left&&this.left.setShowDifferences(e)},rightChunks:function(){if(this.right)return i(this.right),this.right.chunks},leftChunks:function(){if(this.left)return i(this.left),this.left.chunks}};var H=1,z=2,B=4;function U(e){this.cm=e,this.alignable=[],this.height=e.doc.height;var t=this;e.on("markerAdded",function(e,n){if(n.collapsed){var r=n.find(1);null!=r&&t.set(r.line,B)}}),e.on("markerCleared",function(e,n,r,i){null!=i&&n.collapsed&&t.check(i,B,t.hasMarker)}),e.on("markerChanged",this.signal.bind(this)),e.on("lineWidgetAdded",function(e,n,r){n.mergeSpacer||(n.above?t.set(r-1,z):t.set(r,H))}),e.on("lineWidgetCleared",function(e,n,r){n.mergeSpacer||(n.above?t.check(r-1,z,t.hasWidgetBelow):t.check(r,H,t.hasWidget))}),e.on("lineWidgetChanged",this.signal.bind(this)),e.on("change",function(e,n){var r=n.from.line,i=n.to.line-n.from.line,o=n.text.length-1,l=r+o;(i||o)&&t.map(r,i,o),t.check(l,B,t.hasMarker),(i||o)&&t.check(n.from.line,B,t.hasMarker)}),e.on("viewportChange",function(){t.cm.doc.height!=t.height&&t.signal()})}function j(e,t){return(e.line-t.line||e.ch-t.ch)<0?e:t}function G(e,t){return(e.line-t.line||e.ch-t.ch)>0?e:t}function V(e,t){return e.line==t.line&&e.ch==t.ch}function $(e,t,n){for(var r=e.length-1;r>=0;r--){var i=e[r],o=(n?i.origTo:i.editTo)-1;if(o<t)return o}}function K(e,t,n){for(var r=0;r<e.length;r++){var i=e[r],o=n?i.origFrom:i.editFrom;if(o>t)return o}}function Y(t,n){var r=null,o=t.state.diffViews,l=t.getCursor().line;if(o)for(var a=0;a<o.length;a++){var s=o[a],c=t==s.orig;i(s);var u=n<0?$(s.chunks,l,c):K(s.chunks,l,c);null==u||null!=r&&!(n<0?u>r:u<r)||(r=u)}if(null==r)return e.Pass;t.setCursor(r,0)}U.prototype={signal:function(){e.signal(this,"realign"),this.height=this.cm.doc.height},set:function(e,t){for(var n=-1;n<this.alignable.length;n+=2){var r=this.alignable[n]-e;if(0==r){if((this.alignable[n+1]&t)==t)return;return this.alignable[n+1]|=t,void this.signal()}if(r>0)break}this.signal(),this.alignable.splice(n,0,e,t)},find:function(e){for(var t=0;t<this.alignable.length;t+=2)if(this.alignable[t]==e)return t;return-1},check:function(e,t,n){var r=this.find(e);if(-1!=r&&this.alignable[r+1]&t&&!n.call(this,e)){this.signal();var i=this.alignable[r+1]&~t;i?this.alignable[r+1]=i:this.alignable.splice(r,2)}},hasMarker:function(e){var t=this.cm.getLineHandle(e);if(t.markedSpans)for(var n=0;n<t.markedSpans.length;n++)if(t.markedSpans[n].mark.collapsed&&null!=t.markedSpans[n].to)return!0;return!1},hasWidget:function(e){var t=this.cm.getLineHandle(e);if(t.widgets)for(var n=0;n<t.widgets.length;n++)if(!t.widgets[n].above&&!t.widgets[n].mergeSpacer)return!0;return!1},hasWidgetBelow:function(e){if(e==this.cm.lastLine())return!1;var t=this.cm.getLineHandle(e+1);if(t.widgets)for(var n=0;n<t.widgets.length;n++)if(t.widgets[n].above&&!t.widgets[n].mergeSpacer)return!0;return!1},map:function(e,t,n){for(var r=n-t,i=e+t,o=-1,l=-1,a=0;a<this.alignable.length;a+=2){var s=this.alignable[a];s==e&&this.alignable[a+1]&z&&(o=a),s==i&&this.alignable[a+1]&z&&(l=a),s<=e||(s<i?this.alignable.splice(a--,2):this.alignable[a]+=r)}if(o>-1){var c=this.alignable[o+1];c==z?this.alignable.splice(o,2):this.alignable[o+1]=c&~z}l>-1&&n&&this.set(e+n,z)}},e.commands.goNextDiff=function(e){return Y(e,1)},e.commands.goPrevDiff=function(e){return Y(e,-1)}}(n(5))},function(e,t,n){!function(e){"use strict";e.defineMode("shell",function(){var e={};function t(t,n){for(var r=n.split(" "),i=0;i<r.length;i++)e[r[i]]=t}function n(e,t){var l="("==e?")":"{"==e?"}":e;return function(a,s){for(var c,u=!1;null!=(c=a.next());){if(c===l&&!u){s.tokens.shift();break}if("$"===c&&!u&&"'"!==e&&a.peek()!=l){u=!0,a.backUp(1),s.tokens.unshift(i);break}if(!u&&e!==l&&c===e)return s.tokens.unshift(n(e,t)),o(a,s);if(!u&&/['"]/.test(c)&&!/['"]/.test(e)){s.tokens.unshift(r(c,"string")),a.backUp(1);break}u=!u&&"\\"===c}return t}}function r(e,t){return function(r,i){return i.tokens[0]=n(e,t),r.next(),o(r,i)}}t("atom","true false"),t("keyword","if then do else elif while until for in esac fi fin fil done exit set unset export function"),t("builtin","ab awk bash beep cat cc cd chown chmod chroot clear cp curl cut diff echo find gawk gcc get git grep hg kill killall ln ls make mkdir openssl mv nc node npm ping ps restart rm rmdir sed service sh shopt shred source sort sleep ssh start stop su sudo svn tee telnet top touch vi vim wall wc wget who write yes zsh");var i=function(e,t){t.tokens.length>1&&e.eat("$");var r=e.next();return/['"({]/.test(r)?(t.tokens[0]=n(r,"("==r?"quote":"{"==r?"def":"string"),o(e,t)):(/\d/.test(r)||e.eatWhile(/\w/),t.tokens.shift(),"def")};function o(t,r){return(r.tokens[0]||function(t,r){if(t.eatSpace())return null;var l=t.sol(),a=t.next();if("\\"===a)return t.next(),null;if("'"===a||'"'===a||"`"===a)return r.tokens.unshift(n(a,"`"===a?"quote":"string")),o(t,r);if("#"===a)return l&&t.eat("!")?(t.skipToEnd(),"meta"):(t.skipToEnd(),"comment");if("$"===a)return r.tokens.unshift(i),o(t,r);if("+"===a||"="===a)return"operator";if("-"===a)return t.eat("-"),t.eatWhile(/\w/),"attribute";if(/\d/.test(a)&&(t.eatWhile(/\d/),t.eol()||!/\w/.test(t.peek())))return"number";t.eatWhile(/[\w-]/);var s=t.current();return"="===t.peek()&&/\w+/.test(s)?"def":e.hasOwnProperty(s)?e[s]:null})(t,r)}return{startState:function(){return{tokens:[]}},token:function(e,t){return o(e,t)},closeBrackets:"()[]{}''\"\"``",lineComment:"#",fold:"brace"}}),e.defineMIME("text/x-sh","shell"),e.defineMIME("application/x-sh","shell")}(n(5))},function(e,t,n){(e.exports=n(3)(!1)).push([e.i,'.CodeMirror-foldmarker {\n  color: blue;\n  text-shadow: #b9f 1px 1px 2px, #b9f -1px -1px 2px, #b9f 1px -1px 2px, #b9f -1px 1px 2px;\n  font-family: arial;\n  line-height: .3;\n  cursor: pointer;\n}\n.CodeMirror-foldgutter {\n  width: .7em;\n}\n.CodeMirror-foldgutter-open,\n.CodeMirror-foldgutter-folded {\n  cursor: pointer;\n}\n.CodeMirror-foldgutter-open:after {\n  content: "\\25BE";\n}\n.CodeMirror-foldgutter-folded:after {\n  content: "\\25B8";\n}\n',""])},function(e,t,n){var r=n(19);"string"==typeof r&&(r=[[e.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};n(2)(r,i);r.locals&&(e.exports=r.locals)},function(e,t,n){!function(e){"use strict";e.registerGlobalHelper("fold","comment",function(e){return e.blockCommentStart&&e.blockCommentEnd},function(t,n){var r=t.getModeAt(n),i=r.blockCommentStart,o=r.blockCommentEnd;if(i&&o){for(var l,a=n.line,s=t.getLine(a),c=n.ch,u=0;;){var h=c<=0?-1:s.lastIndexOf(i,c-1);if(-1!=h){if(1==u&&h<n.ch)return;if(/comment/.test(t.getTokenTypeAt(e.Pos(a,h+1)))&&(0==h||s.slice(h-o.length,h)==o||!/comment/.test(t.getTokenTypeAt(e.Pos(a,h))))){l=h+i.length;break}c=h-1}else{if(1==u)return;u=1,c=s.length}}var f,d,p=1,g=t.lastLine();e:for(var m=a;m<=g;++m)for(var v=t.getLine(m),b=m==a?l:0;;){var y=v.indexOf(i,b),x=v.indexOf(o,b);if(y<0&&(y=v.length),x<0&&(x=v.length),(b=Math.min(y,x))==v.length)break;if(b==y)++p;else if(!--p){f=m,d=b;break e}++b}if(null!=f&&(a!=f||d!=l))return{from:e.Pos(a,l),to:e.Pos(f,d)}}})}(n(5))},function(e,t,n){!function(e){"use strict";e.registerHelper("fold","markdown",function(t,n){var r=100;function i(n){var r=t.getTokenTypeAt(e.Pos(n,0));return r&&/\bheader\b/.test(r)}function o(e,t,n){var o=t&&t.match(/^#+/);return o&&i(e)?o[0].length:(o=n&&n.match(/^[=\-]+\s*$/))&&i(e+1)?"="==n[0]?1:2:r}var l=t.getLine(n.line),a=t.getLine(n.line+1),s=o(n.line,l,a);if(s!==r){for(var c=t.lastLine(),u=n.line,h=t.getLine(u+2);u<c&&!(o(u+1,a,h)<=s);)++u,a=h,h=t.getLine(u+2);return{from:e.Pos(n.line,l.length),to:e.Pos(u,t.getLine(u).length)}}})}(n(5))},function(e,t,n){!function(e){"use strict";var t=e.Pos;function n(e,t){return e.line-t.line||e.ch-t.ch}var r="A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",i=new RegExp("<(/?)(["+r+"]["+r+"-:.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*)","g");function o(e,t,n,r){this.line=t,this.ch=n,this.cm=e,this.text=e.getLine(t),this.min=r?Math.max(r.from,e.firstLine()):e.firstLine(),this.max=r?Math.min(r.to-1,e.lastLine()):e.lastLine()}function l(e,n){var r=e.cm.getTokenTypeAt(t(e.line,n));return r&&/\btag\b/.test(r)}function a(e){if(!(e.line>=e.max))return e.ch=0,e.text=e.cm.getLine(++e.line),!0}function s(e){if(!(e.line<=e.min))return e.text=e.cm.getLine(--e.line),e.ch=e.text.length,!0}function c(e){for(;;){var t=e.text.indexOf(">",e.ch);if(-1==t){if(a(e))continue;return}if(l(e,t+1)){var n=e.text.lastIndexOf("/",t),r=n>-1&&!/\S/.test(e.text.slice(n+1,t));return e.ch=t+1,r?"selfClose":"regular"}e.ch=t+1}}function u(e){for(;;){var t=e.ch?e.text.lastIndexOf("<",e.ch-1):-1;if(-1==t){if(s(e))continue;return}if(l(e,t+1)){i.lastIndex=t,e.ch=t;var n=i.exec(e.text);if(n&&n.index==t)return n}else e.ch=t}}function h(e){for(;;){i.lastIndex=e.ch;var t=i.exec(e.text);if(!t){if(a(e))continue;return}if(l(e,t.index+1))return e.ch=t.index+t[0].length,t;e.ch=t.index+1}}function f(e){for(;;){var t=e.ch?e.text.lastIndexOf(">",e.ch-1):-1;if(-1==t){if(s(e))continue;return}if(l(e,t+1)){var n=e.text.lastIndexOf("/",t),r=n>-1&&!/\S/.test(e.text.slice(n+1,t));return e.ch=t+1,r?"selfClose":"regular"}e.ch=t}}function d(e,n){for(var r=[];;){var i,o=h(e),l=e.line,a=e.ch-(o?o[0].length:0);if(!o||!(i=c(e)))return;if("selfClose"!=i)if(o[1]){for(var s=r.length-1;s>=0;--s)if(r[s]==o[2]){r.length=s;break}if(s<0&&(!n||n==o[2]))return{tag:o[2],from:t(l,a),to:t(e.line,e.ch)}}else r.push(o[2])}}function p(e,n){for(var r=[];;){var i=f(e);if(!i)return;if("selfClose"!=i){var o=e.line,l=e.ch,a=u(e);if(!a)return;if(a[1])r.push(a[2]);else{for(var s=r.length-1;s>=0;--s)if(r[s]==a[2]){r.length=s;break}if(s<0&&(!n||n==a[2]))return{tag:a[2],from:t(e.line,e.ch),to:t(o,l)}}}else u(e)}}e.registerHelper("fold","xml",function(e,r){for(var i=new o(e,r.line,0);;){var l=h(i);if(!l||i.line!=r.line)return;var a=c(i);if(!a)return;if(!l[1]&&"selfClose"!=a){var s=t(i.line,i.ch),u=d(i,l[2]);return u&&n(u.from,s)>0?{from:s,to:u.from}:null}}}),e.findMatchingTag=function(e,r,i){var l=new o(e,r.line,r.ch,i);if(-1!=l.text.indexOf(">")||-1!=l.text.indexOf("<")){var a=c(l),s=a&&t(l.line,l.ch),h=a&&u(l);if(a&&h&&!(n(l,r)>0)){var f={from:t(l.line,l.ch),to:s,tag:h[2]};return"selfClose"==a?{open:f,close:null,at:"open"}:h[1]?{open:p(l,h[2]),close:f,at:"close"}:(l=new o(e,s.line,s.ch,i),{open:f,close:d(l,h[2]),at:"open"})}}},e.findEnclosingTag=function(e,t,n,r){for(var i=new o(e,t.line,t.ch,n);;){var l=p(i,r);if(!l)break;var a=new o(e,t.line,t.ch,n),s=d(a,l.tag);if(s)return{open:l,close:s}}},e.scanForClosingTag=function(e,t,n,r){var i=new o(e,t.line,t.ch,r?{from:0,to:r}:null);return d(i,n)}}(n(5))},function(e,t,n){!function(e){"use strict";e.registerHelper("fold","brace",function(t,n){var r,i=n.line,o=t.getLine(i);function l(l){for(var a=n.ch,s=0;;){var c=a<=0?-1:o.lastIndexOf(l,a-1);if(-1!=c){if(1==s&&c<n.ch)break;if(r=t.getTokenTypeAt(e.Pos(i,c+1)),!/^(comment|string)/.test(r))return c+1;a=c-1}else{if(1==s)break;s=1,a=o.length}}}var a="{",s="}",c=l("{");if(null==c&&(a="[",s="]",c=l("[")),null!=c){var u,h,f=1,d=t.lastLine();e:for(var p=i;p<=d;++p)for(var g=t.getLine(p),m=p==i?c:0;;){var v=g.indexOf(a,m),b=g.indexOf(s,m);if(v<0&&(v=g.length),b<0&&(b=g.length),(m=Math.min(v,b))==g.length)break;if(t.getTokenTypeAt(e.Pos(p,m+1))==r)if(m==v)++f;else if(!--f){u=p,h=m;break e}++m}if(null!=u&&(i!=u||h!=c))return{from:e.Pos(i,c),to:e.Pos(u,h)}}}),e.registerHelper("fold","import",function(t,n){function r(n){if(n<t.firstLine()||n>t.lastLine())return null;var r=t.getTokenAt(e.Pos(n,1));if(/\S/.test(r.string)||(r=t.getTokenAt(e.Pos(n,r.end+1))),"keyword"!=r.type||"import"!=r.string)return null;for(var i=n,o=Math.min(t.lastLine(),n+10);i<=o;++i){var l=t.getLine(i),a=l.indexOf(";");if(-1!=a)return{startCh:r.end,end:e.Pos(i,a)}}}var i,o=n.line,l=r(o);if(!l||r(o-1)||(i=r(o-2))&&i.end.line==o-1)return null;for(var a=l.end;;){var s=r(a.line+1);if(null==s)break;a=s.end}return{from:t.clipPos(e.Pos(o,l.startCh+1)),to:a}}),e.registerHelper("fold","include",function(t,n){function r(n){if(n<t.firstLine()||n>t.lastLine())return null;var r=t.getTokenAt(e.Pos(n,1));return/\S/.test(r.string)||(r=t.getTokenAt(e.Pos(n,r.end+1))),"meta"==r.type&&"#include"==r.string.slice(0,8)?r.start+8:void 0}var i=n.line,o=r(i);if(null==o||null!=r(i-1))return null;for(var l=i;;){var a=r(l+1);if(null==a)break;++l}return{from:e.Pos(i,o+1),to:t.clipPos(e.Pos(l))}})}(n(5))},function(e,t,n){!function(e){"use strict";function t(t,n,i,o){if(i&&i.call){var l=i;i=null}else var l=r(t,i,"rangeFinder");"number"==typeof n&&(n=e.Pos(n,0));var a=r(t,i,"minFoldSize");function s(e){var r=l(t,n);if(!r||r.to.line-r.from.line<a)return null;for(var i=t.findMarksAt(r.from),s=0;s<i.length;++s)if(i[s].__isFold&&"fold"!==o){if(!e)return null;r.cleared=!0,i[s].clear()}return r}var c=s(!0);if(r(t,i,"scanUp"))for(;!c&&n.line>t.firstLine();)n=e.Pos(n.line-1,0),c=s(!1);if(c&&!c.cleared&&"unfold"!==o){var u=function(e,t){var n=r(e,t,"widget");if("string"==typeof n){var i=document.createTextNode(n);(n=document.createElement("span")).appendChild(i),n.className="CodeMirror-foldmarker"}else n&&(n=n.cloneNode(!0));return n}(t,i);e.on(u,"mousedown",function(t){h.clear(),e.e_preventDefault(t)});var h=t.markText(c.from,c.to,{replacedWith:u,clearOnEnter:r(t,i,"clearOnEnter"),__isFold:!0});h.on("clear",function(n,r){e.signal(t,"unfold",t,n,r)}),e.signal(t,"fold",t,c.from,c.to)}}e.newFoldFunction=function(e,n){return function(r,i){t(r,i,{rangeFinder:e,widget:n})}},e.defineExtension("foldCode",function(e,n,r){t(this,e,n,r)}),e.defineExtension("isFolded",function(e){for(var t=this.findMarksAt(e),n=0;n<t.length;++n)if(t[n].__isFold)return!0}),e.commands.toggleFold=function(e){e.foldCode(e.getCursor())},e.commands.fold=function(e){e.foldCode(e.getCursor(),null,"fold")},e.commands.unfold=function(e){e.foldCode(e.getCursor(),null,"unfold")},e.commands.foldAll=function(t){t.operation(function(){for(var n=t.firstLine(),r=t.lastLine();n<=r;n++)t.foldCode(e.Pos(n,0),null,"fold")})},e.commands.unfoldAll=function(t){t.operation(function(){for(var n=t.firstLine(),r=t.lastLine();n<=r;n++)t.foldCode(e.Pos(n,0),null,"unfold")})},e.registerHelper("fold","combine",function(){var e=Array.prototype.slice.call(arguments,0);return function(t,n){for(var r=0;r<e.length;++r){var i=e[r](t,n);if(i)return i}}}),e.registerHelper("fold","auto",function(e,t){for(var n=e.getHelpers(t,"fold"),r=0;r<n.length;r++){var i=n[r](e,t);if(i)return i}});var n={rangeFinder:e.fold.auto,widget:"↔",minFoldSize:0,scanUp:!1,clearOnEnter:!0};function r(e,t,r){if(t&&void 0!==t[r])return t[r];var i=e.options.foldOptions;return i&&void 0!==i[r]?i[r]:n[r]}e.defineOption("foldOptions",null),e.defineExtension("foldOption",function(e,t){return r(this,e,t)})}(n(5))},function(e,t,n){!function(e){"use strict";e.defineOption("foldGutter",!1,function(t,n,r){var i;r&&r!=e.Init&&(t.clearGutter(t.state.foldGutter.options.gutter),t.state.foldGutter=null,t.off("gutterClick",l),t.off("change",a),t.off("viewportChange",s),t.off("fold",c),t.off("unfold",c),t.off("swapDoc",a)),n&&(t.state.foldGutter=new function(e){this.options=e,this.from=this.to=0}((!0===(i=n)&&(i={}),null==i.gutter&&(i.gutter="CodeMirror-foldgutter"),null==i.indicatorOpen&&(i.indicatorOpen="CodeMirror-foldgutter-open"),null==i.indicatorFolded&&(i.indicatorFolded="CodeMirror-foldgutter-folded"),i)),o(t),t.on("gutterClick",l),t.on("change",a),t.on("viewportChange",s),t.on("fold",c),t.on("unfold",c),t.on("swapDoc",a))});var t=e.Pos;function n(e,n){for(var r=e.findMarks(t(n,0),t(n+1,0)),i=0;i<r.length;++i)if(r[i].__isFold&&r[i].find().from.line==n)return r[i]}function r(e){if("string"==typeof e){var t=document.createElement("div");return t.className=e+" CodeMirror-guttermarker-subtle",t}return e.cloneNode(!0)}function i(e,i,o){var l=e.state.foldGutter.options,a=i,s=e.foldOption(l,"minFoldSize"),c=e.foldOption(l,"rangeFinder");e.eachLine(i,o,function(i){var o=null;if(n(e,a))o=r(l.indicatorFolded);else{var u=t(a,0),h=c&&c(e,u);h&&h.to.line-h.from.line>=s&&(o=r(l.indicatorOpen))}e.setGutterMarker(i,l.gutter,o),++a})}function o(e){var t=e.getViewport(),n=e.state.foldGutter;n&&(e.operation(function(){i(e,t.from,t.to)}),n.from=t.from,n.to=t.to)}function l(e,r,i){var o=e.state.foldGutter;if(o){var l=o.options;if(i==l.gutter){var a=n(e,r);a?a.clear():e.foldCode(t(r,0),l.rangeFinder)}}}function a(e){var t=e.state.foldGutter;if(t){var n=t.options;t.from=t.to=0,clearTimeout(t.changeUpdate),t.changeUpdate=setTimeout(function(){o(e)},n.foldOnChangeTimeSpan||600)}}function s(e){var t=e.state.foldGutter;if(t){var n=t.options;clearTimeout(t.changeUpdate),t.changeUpdate=setTimeout(function(){var n=e.getViewport();t.from==t.to||n.from-t.to>20||t.from-n.to>20?o(e):e.operation(function(){n.from<t.from&&(i(e,n.from,t.from),t.from=n.from),n.to>t.to&&(i(e,t.to,n.to),t.to=n.to)})},n.updateViewportTimeSpan||400)}}function c(e,t){var n=e.state.foldGutter;if(n){var r=t.line;r>=n.from&&r<n.to&&i(e,r,r+1)}}}(n(5),n(25))},function(e,t,n){(e.exports=n(3)(!1)).push([e.i,'.CodeMirror-merge {\n  position: relative;\n  border: 1px solid #ddd;\n  white-space: pre;\n}\n\n.CodeMirror-merge, .CodeMirror-merge .CodeMirror {\n  height: 350px;\n}\n\n.CodeMirror-merge-2pane .CodeMirror-merge-pane { width: 47%; }\n.CodeMirror-merge-2pane .CodeMirror-merge-gap { width: 6%; }\n.CodeMirror-merge-3pane .CodeMirror-merge-pane { width: 31%; }\n.CodeMirror-merge-3pane .CodeMirror-merge-gap { width: 3.5%; }\n\n.CodeMirror-merge-pane {\n  display: inline-block;\n  white-space: normal;\n  vertical-align: top;\n}\n.CodeMirror-merge-pane-rightmost {\n  position: absolute;\n  right: 0px;\n  z-index: 1;\n}\n\n.CodeMirror-merge-gap {\n  z-index: 2;\n  display: inline-block;\n  height: 100%;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n  overflow: hidden;\n  border-left: 1px solid #ddd;\n  border-right: 1px solid #ddd;\n  position: relative;\n  background: #f8f8f8;\n}\n\n.CodeMirror-merge-scrolllock-wrap {\n  position: absolute;\n  bottom: 0; left: 50%;\n}\n.CodeMirror-merge-scrolllock {\n  position: relative;\n  left: -50%;\n  cursor: pointer;\n  color: #555;\n  line-height: 1;\n}\n.CodeMirror-merge-scrolllock:after {\n  content: "\\21DB\\A0\\A0\\21DA";\n}\n.CodeMirror-merge-scrolllock.CodeMirror-merge-scrolllock-enabled:after {\n  content: "\\21DB\\21DA";\n}\n\n.CodeMirror-merge-copybuttons-left, .CodeMirror-merge-copybuttons-right {\n  position: absolute;\n  left: 0; top: 0;\n  right: 0; bottom: 0;\n  line-height: 1;\n}\n\n.CodeMirror-merge-copy {\n  position: absolute;\n  cursor: pointer;\n  color: #44c;\n  z-index: 3;\n}\n\n.CodeMirror-merge-copy-reverse {\n  position: absolute;\n  cursor: pointer;\n  color: #44c;\n}\n\n.CodeMirror-merge-copybuttons-left .CodeMirror-merge-copy { left: 2px; }\n.CodeMirror-merge-copybuttons-right .CodeMirror-merge-copy { right: 2px; }\n\n.CodeMirror-merge-r-inserted, .CodeMirror-merge-l-inserted {\n  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAACCAYAAACddGYaAAAAGUlEQVQI12MwuCXy3+CWyH8GBgYGJgYkAABZbAQ9ELXurwAAAABJRU5ErkJggg==);\n  background-position: bottom left;\n  background-repeat: repeat-x;\n}\n\n.CodeMirror-merge-r-deleted, .CodeMirror-merge-l-deleted {\n  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAACCAYAAACddGYaAAAAGUlEQVQI12M4Kyb2/6yY2H8GBgYGJgYkAABURgPz6Ks7wQAAAABJRU5ErkJggg==);\n  background-position: bottom left;\n  background-repeat: repeat-x;\n}\n\n.CodeMirror-merge-r-chunk { background: #ffffe0; }\n.CodeMirror-merge-r-chunk-start { border-top: 1px solid #ee8; }\n.CodeMirror-merge-r-chunk-end { border-bottom: 1px solid #ee8; }\n.CodeMirror-merge-r-connect { fill: #ffffe0; stroke: #ee8; stroke-width: 1px; }\n\n.CodeMirror-merge-l-chunk { background: #eef; }\n.CodeMirror-merge-l-chunk-start { border-top: 1px solid #88e; }\n.CodeMirror-merge-l-chunk-end { border-bottom: 1px solid #88e; }\n.CodeMirror-merge-l-connect { fill: #eef; stroke: #88e; stroke-width: 1px; }\n\n.CodeMirror-merge-l-chunk.CodeMirror-merge-r-chunk { background: #dfd; }\n.CodeMirror-merge-l-chunk-start.CodeMirror-merge-r-chunk-start { border-top: 1px solid #4e4; }\n.CodeMirror-merge-l-chunk-end.CodeMirror-merge-r-chunk-end { border-bottom: 1px solid #4e4; }\n\n.CodeMirror-merge-collapsed-widget:before {\n  content: "(...)";\n}\n.CodeMirror-merge-collapsed-widget {\n  cursor: pointer;\n  color: #88b;\n  background: #eef;\n  border: 1px solid #ddf;\n  font-size: 90%;\n  padding: 0 3px;\n  border-radius: 4px;\n}\n.CodeMirror-merge-collapsed-line .CodeMirror-gutter-elt { display: none; }\n',""])},function(e,t,n){var r=n(27);"string"==typeof r&&(r=[[e.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};n(2)(r,i);r.locals&&(e.exports=r.locals)},function(e,t,n){(e.exports=n(3)(!1)).push([e.i,"/* BASICS */\n\n.CodeMirror {\n  /* Set height, width, borders, and global font properties here */\n  font-family: monospace;\n  height: 300px;\n  color: black;\n  direction: ltr;\n}\n\n/* PADDING */\n\n.CodeMirror-lines {\n  padding: 4px 0; /* Vertical padding around content */\n}\n.CodeMirror pre {\n  padding: 0 4px; /* Horizontal padding of content */\n}\n\n.CodeMirror-scrollbar-filler, .CodeMirror-gutter-filler {\n  background-color: white; /* The little square between H and V scrollbars */\n}\n\n/* GUTTER */\n\n.CodeMirror-gutters {\n  border-right: 1px solid #ddd;\n  background-color: #f7f7f7;\n  white-space: nowrap;\n}\n.CodeMirror-linenumbers {}\n.CodeMirror-linenumber {\n  padding: 0 3px 0 5px;\n  min-width: 20px;\n  text-align: right;\n  color: #999;\n  white-space: nowrap;\n}\n\n.CodeMirror-guttermarker { color: black; }\n.CodeMirror-guttermarker-subtle { color: #999; }\n\n/* CURSOR */\n\n.CodeMirror-cursor {\n  border-left: 1px solid black;\n  border-right: none;\n  width: 0;\n}\n/* Shown when moving in bi-directional text */\n.CodeMirror div.CodeMirror-secondarycursor {\n  border-left: 1px solid silver;\n}\n.cm-fat-cursor .CodeMirror-cursor {\n  width: auto;\n  border: 0 !important;\n  background: #7e7;\n}\n.cm-fat-cursor div.CodeMirror-cursors {\n  z-index: 1;\n}\n.cm-fat-cursor-mark {\n  background-color: rgba(20, 255, 20, 0.5);\n  -webkit-animation: blink 1.06s steps(1) infinite;\n  -moz-animation: blink 1.06s steps(1) infinite;\n  animation: blink 1.06s steps(1) infinite;\n}\n.cm-animate-fat-cursor {\n  width: auto;\n  border: 0;\n  -webkit-animation: blink 1.06s steps(1) infinite;\n  -moz-animation: blink 1.06s steps(1) infinite;\n  animation: blink 1.06s steps(1) infinite;\n  background-color: #7e7;\n}\n@-moz-keyframes blink {\n  0% {}\n  50% { background-color: transparent; }\n  100% {}\n}\n@-webkit-keyframes blink {\n  0% {}\n  50% { background-color: transparent; }\n  100% {}\n}\n@keyframes blink {\n  0% {}\n  50% { background-color: transparent; }\n  100% {}\n}\n\n/* Can style cursor different in overwrite (non-insert) mode */\n.CodeMirror-overwrite .CodeMirror-cursor {}\n\n.cm-tab { display: inline-block; text-decoration: inherit; }\n\n.CodeMirror-rulers {\n  position: absolute;\n  left: 0; right: 0; top: -50px; bottom: -20px;\n  overflow: hidden;\n}\n.CodeMirror-ruler {\n  border-left: 1px solid #ccc;\n  top: 0; bottom: 0;\n  position: absolute;\n}\n\n/* DEFAULT THEME */\n\n.cm-s-default .cm-header {color: blue;}\n.cm-s-default .cm-quote {color: #090;}\n.cm-negative {color: #d44;}\n.cm-positive {color: #292;}\n.cm-header, .cm-strong {font-weight: bold;}\n.cm-em {font-style: italic;}\n.cm-link {text-decoration: underline;}\n.cm-strikethrough {text-decoration: line-through;}\n\n.cm-s-default .cm-keyword {color: #708;}\n.cm-s-default .cm-atom {color: #219;}\n.cm-s-default .cm-number {color: #164;}\n.cm-s-default .cm-def {color: #00f;}\n.cm-s-default .cm-variable,\n.cm-s-default .cm-punctuation,\n.cm-s-default .cm-property,\n.cm-s-default .cm-operator {}\n.cm-s-default .cm-variable-2 {color: #05a;}\n.cm-s-default .cm-variable-3, .cm-s-default .cm-type {color: #085;}\n.cm-s-default .cm-comment {color: #a50;}\n.cm-s-default .cm-string {color: #a11;}\n.cm-s-default .cm-string-2 {color: #f50;}\n.cm-s-default .cm-meta {color: #555;}\n.cm-s-default .cm-qualifier {color: #555;}\n.cm-s-default .cm-builtin {color: #30a;}\n.cm-s-default .cm-bracket {color: #997;}\n.cm-s-default .cm-tag {color: #170;}\n.cm-s-default .cm-attribute {color: #00c;}\n.cm-s-default .cm-hr {color: #999;}\n.cm-s-default .cm-link {color: #00c;}\n\n.cm-s-default .cm-error {color: #f00;}\n.cm-invalidchar {color: #f00;}\n\n.CodeMirror-composing { border-bottom: 2px solid; }\n\n/* Default styles for common addons */\n\ndiv.CodeMirror span.CodeMirror-matchingbracket {color: #0b0;}\ndiv.CodeMirror span.CodeMirror-nonmatchingbracket {color: #a22;}\n.CodeMirror-matchingtag { background: rgba(255, 150, 0, .3); }\n.CodeMirror-activeline-background {background: #e8f2ff;}\n\n/* STOP */\n\n/* The rest of this file contains styles related to the mechanics of\n   the editor. You probably shouldn't touch them. */\n\n.CodeMirror {\n  position: relative;\n  overflow: hidden;\n  background: white;\n}\n\n.CodeMirror-scroll {\n  overflow: scroll !important; /* Things will break if this is overridden */\n  /* 30px is the magic margin used to hide the element's real scrollbars */\n  /* See overflow: hidden in .CodeMirror */\n  margin-bottom: -30px; margin-right: -30px;\n  padding-bottom: 30px;\n  height: 100%;\n  outline: none; /* Prevent dragging from highlighting the element */\n  position: relative;\n}\n.CodeMirror-sizer {\n  position: relative;\n  border-right: 30px solid transparent;\n}\n\n/* The fake, visible scrollbars. Used to force redraw during scrolling\n   before actual scrolling happens, thus preventing shaking and\n   flickering artifacts. */\n.CodeMirror-vscrollbar, .CodeMirror-hscrollbar, .CodeMirror-scrollbar-filler, .CodeMirror-gutter-filler {\n  position: absolute;\n  z-index: 6;\n  display: none;\n}\n.CodeMirror-vscrollbar {\n  right: 0; top: 0;\n  overflow-x: hidden;\n  overflow-y: scroll;\n}\n.CodeMirror-hscrollbar {\n  bottom: 0; left: 0;\n  overflow-y: hidden;\n  overflow-x: scroll;\n}\n.CodeMirror-scrollbar-filler {\n  right: 0; bottom: 0;\n}\n.CodeMirror-gutter-filler {\n  left: 0; bottom: 0;\n}\n\n.CodeMirror-gutters {\n  position: absolute; left: 0; top: 0;\n  min-height: 100%;\n  z-index: 3;\n}\n.CodeMirror-gutter {\n  white-space: normal;\n  height: 100%;\n  display: inline-block;\n  vertical-align: top;\n  margin-bottom: -30px;\n}\n.CodeMirror-gutter-wrapper {\n  position: absolute;\n  z-index: 4;\n  background: none !important;\n  border: none !important;\n}\n.CodeMirror-gutter-background {\n  position: absolute;\n  top: 0; bottom: 0;\n  z-index: 4;\n}\n.CodeMirror-gutter-elt {\n  position: absolute;\n  cursor: default;\n  z-index: 4;\n}\n.CodeMirror-gutter-wrapper ::selection { background-color: transparent }\n.CodeMirror-gutter-wrapper ::-moz-selection { background-color: transparent }\n\n.CodeMirror-lines {\n  cursor: text;\n  min-height: 1px; /* prevents collapsing before first draw */\n}\n.CodeMirror pre {\n  /* Reset some styles that the rest of the page might have set */\n  -moz-border-radius: 0; -webkit-border-radius: 0; border-radius: 0;\n  border-width: 0;\n  background: transparent;\n  font-family: inherit;\n  font-size: inherit;\n  margin: 0;\n  white-space: pre;\n  word-wrap: normal;\n  line-height: inherit;\n  color: inherit;\n  z-index: 2;\n  position: relative;\n  overflow: visible;\n  -webkit-tap-highlight-color: transparent;\n  -webkit-font-variant-ligatures: contextual;\n  font-variant-ligatures: contextual;\n}\n.CodeMirror-wrap pre {\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  word-break: normal;\n}\n\n.CodeMirror-linebackground {\n  position: absolute;\n  left: 0; right: 0; top: 0; bottom: 0;\n  z-index: 0;\n}\n\n.CodeMirror-linewidget {\n  position: relative;\n  z-index: 2;\n  padding: 0.1px; /* Force widget margins to stay inside of the container */\n}\n\n.CodeMirror-widget {}\n\n.CodeMirror-rtl pre { direction: rtl; }\n\n.CodeMirror-code {\n  outline: none;\n}\n\n/* Force content-box sizing for the elements where we expect it */\n.CodeMirror-scroll,\n.CodeMirror-sizer,\n.CodeMirror-gutter,\n.CodeMirror-gutters,\n.CodeMirror-linenumber {\n  -moz-box-sizing: content-box;\n  box-sizing: content-box;\n}\n\n.CodeMirror-measure {\n  position: absolute;\n  width: 100%;\n  height: 0;\n  overflow: hidden;\n  visibility: hidden;\n}\n\n.CodeMirror-cursor {\n  position: absolute;\n  pointer-events: none;\n}\n.CodeMirror-measure pre { position: static; }\n\ndiv.CodeMirror-cursors {\n  visibility: hidden;\n  position: relative;\n  z-index: 3;\n}\ndiv.CodeMirror-dragcursors {\n  visibility: visible;\n}\n\n.CodeMirror-focused div.CodeMirror-cursors {\n  visibility: visible;\n}\n\n.CodeMirror-selected { background: #d9d9d9; }\n.CodeMirror-focused .CodeMirror-selected { background: #d7d4f0; }\n.CodeMirror-crosshair { cursor: crosshair; }\n.CodeMirror-line::selection, .CodeMirror-line > span::selection, .CodeMirror-line > span > span::selection { background: #d7d4f0; }\n.CodeMirror-line::-moz-selection, .CodeMirror-line > span::-moz-selection, .CodeMirror-line > span > span::-moz-selection { background: #d7d4f0; }\n\n.cm-searching {\n  background-color: #ffa;\n  background-color: rgba(255, 255, 0, .4);\n}\n\n/* Used to force a border model for a node */\n.cm-force-border { padding-right: .1px; }\n\n@media print {\n  /* Hide the cursor when printing */\n  .CodeMirror div.CodeMirror-cursors {\n    visibility: hidden;\n  }\n}\n\n/* See issue #2901 */\n.cm-tab-wrap-hack:after { content: ''; }\n\n/* Help users use markselection to safely style text background */\nspan.CodeMirror-selectedtext { background: none; }\n",""])},function(e,t,n){var r=n(29);"string"==typeof r&&(r=[[e.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};n(2)(r,i);r.locals&&(e.exports=r.locals)},function(e,t,n){(e.exports=n(3)(!1)).push([e.i,".config-upload .CodeMirror,\n.CodeMirror-merge {\n  height: 600px !important;\n  font-size: 18px;\n  line-height: 24px;\n  font-family: PingFangSC-Light, Consolas, 'Courier New', monospace;\n  border: 1px solid #ddd;\n}\n.config-upload .CodeMirror .CodeMirror-merge-copy,\n.CodeMirror-merge .CodeMirror-merge-copy {\n  font-size: 28px;\n}\n.config-upload .CodeMirror .CodeMirror-merge-pane.CodeMirror-merge-right.CodeMirror-merge-pane-rightmost .CodeMirror,\n.CodeMirror-merge .CodeMirror-merge-pane.CodeMirror-merge-right.CodeMirror-merge-pane-rightmost .CodeMirror {\n  background-color: #f8f8f8;\n}\n.text-area {\n  height: 30px;\n  line-height: 30px;\n  margin-bottom: 10px;\n}\n.loading-area {\n  width: 100%;\n  height: 100%;\n  position: absolute;\n  top: 0px;\n  bottom: 0px;\n  right: 0px;\n  left: 0px;\n  background: #ffffff;\n  z-index: 99;\n}\n.loading-area .loading-text {\n  text-align: center;\n}\n",""])},function(e,t,n){var r=n(31);"string"==typeof r&&(r=[[e.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};n(2)(r,i);r.locals&&(e.exports=r.locals)},function(e,t,n){(e.exports=n(3)(!1)).push([e.i,".filter-form {\n  position: relative;\n  padding: 20px;\n  background-color: #fff;\n  display: none;\n}\n.filter-form .form-label {\n  max-width: 100px;\n  display: inline-block;\n  color: #888;\n  padding-right: 20px;\n  white-space: nowrap;\n  box-sizing: border-box;\n}\n.filter-form .form-component {\n  display: inline-block;\n}\n.filter-form .query-button {\n  margin-top: 4px;\n}\n.tc-15-tag-list {\n  padding: 20px;\n  display: none;\n}\n.filter-content-show .filter-form {\n  display: block;\n}\n.filter-result-show .tc-15-tag-list {\n  display: block;\n}\n.uw-filter-result-tag {\n  display: inline-block;\n  vertical-align: top;\n  max-width: 15em;\n  word-break: keep-all;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n",""])},function(e,t,n){var r=n(33);"string"==typeof r&&(r=[[e.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};n(2)(r,i);r.locals&&(e.exports=r.locals)},function(e,t,n){(e.exports=n(3)(!1)).push([e.i,".uw-dialog-footer-info {\n  text-align: center;\n  margin-bottom: 4px;\n  font-size: 12px;\n}\n",""])},function(e,t,n){var r=n(35);"string"==typeof r&&(r=[[e.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};n(2)(r,i);r.locals&&(e.exports=r.locals)},function(e,t,n){(e.exports=n(3)(!1)).push([e.i,".uw-plugins-error-message {\n  max-width: 450px;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  display: inline-block;\n  color: #E54545;\n}\n",""])},function(e,t,n){var r=n(37);"string"==typeof r&&(r=[[e.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};n(2)(r,i);r.locals&&(e.exports=r.locals)},function(e,t,n){(e.exports=n(3)(!1)).push([e.i,".uw-plugins-breadcrumb {\n  overflow: hidden;\n  font-size: 13px;\n}\n.uw-plugins-breadcrumb ul {\n  display: flex;\n  margin-right: 20px;\n}\n.uw-plugins-breadcrumb ul li {\n  transition: flex-shrink 0.3s;\n  flex-basis: auto;\n  flex-shrink: 1;\n  min-width: 41px;\n  display: inline-block;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  cursor: pointer;\n}\n.uw-plugins-breadcrumb ul li.root {\n  min-width: 40px;\n}\n.uw-plugins-breadcrumb ul li.more {\n  min-width: 28px;\n}\n.uw-plugins-breadcrumb ul li.more a {\n  color: #444;\n  cursor: default;\n  text-decoration: none;\n}\n.uw-plugins-breadcrumb ul li.cur > a {\n  color: #444;\n  font-weight: 600;\n  cursor: default;\n  text-decoration: none;\n}\n",""])},function(e,t,n){var r=n(39);"string"==typeof r&&(r=[[e.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};n(2)(r,i);r.locals&&(e.exports=r.locals)},function(e,t,n){(e.exports=n(3)(!1)).push([e.i,".uw-plugins-page-loading {\n  text-align: center;\n}\n",""])},function(e,t,n){var r=n(41);"string"==typeof r&&(r=[[e.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};n(2)(r,i);r.locals&&(e.exports=r.locals)},function(e,t,n){(e.exports=n(3)(!1)).push([e.i,"@keyframes scaleAnimation {\n  0% {\n    opacity: 0;\n    transform: scale(1.5);\n  }\n  100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n@keyframes drawCircle {\n  0% {\n    stroke-dashoffset: 408.40704497;\n  }\n  100% {\n    stroke-dashoffset: 0;\n  }\n}\n@keyframes drawCheck {\n  0% {\n    stroke-dashoffset: 90px;\n  }\n  100% {\n    stroke-dashoffset: 0;\n  }\n}\n@keyframes drawCross {\n  0% {\n    stroke-dashoffset: 66px;\n  }\n  100% {\n    stroke-dashoffset: 0;\n  }\n}\n.successAnimationCircle {\n  stroke-dasharray: 408.40704497 408.40704497;\n  stroke: #0ABF5B;\n}\n.successAnimationCheck {\n  stroke-dasharray: 90px 90px;\n  stroke: #0ABF5B;\n}\n.errorAnimationCircle {\n  stroke-dasharray: 408.40704497 408.40704497;\n  stroke: #E54545;\n}\n.errorAnimationCross {\n  stroke-dasharray: 66px 66px;\n  stroke: #E54545;\n}\n.animation {\n  animation: 1s ease-out 0s 1 both scaleAnimation;\n}\n.animation .successAnimationCircle,\n.animation .errorAnimationCircle {\n  animation: 1s cubic-bezier(0.77, 0, 0.175, 1) 0s 1 both drawCircle;\n}\n.animation .successAnimationCheck {\n  animation: 1s cubic-bezier(0.77, 0, 0.175, 1) 0s 1 both drawCheck;\n}\n.animation .errorAnimationCross {\n  animation: 1s cubic-bezier(0.77, 0, 0.175, 1) 0s 1 both drawCross;\n}\n.uw-plugins-process .footer {\n  margin-top: 20px;\n  text-align: center;\n}\n.uw-plugins-process .process {\n  margin: 0 auto 20px;\n}\n.uw-plugins-process .process svg circle {\n  -webkit-transition: stroke-dasharray 0.25s;\n  transition: stroke-dasharray 0.25s;\n}\n.uw-plugins-process .process svg text {\n  font-size: 28px;\n}\n.uw-plugins-process .process svg circle.rotate {\n  fill: transparent;\n  stroke: #006EFF;\n  stroke-linecap: round;\n  stroke-dasharray: 0, 408.40704497;\n  -webkit-animation: stroke-dash 2000ms linear infinite, stroke-width 2000ms linear infinite, stroke-color 8000ms steps(4) infinite;\n  animation: stroke-dash 2000ms linear infinite, stroke-width 2000ms linear infinite, stroke-color 8000ms steps(4) infinite;\n}\n.uw-plugins-process .process svg circle.processing {\n  fill: transparent;\n  stroke-linecap: round;\n  -webkit-animation: stroke-width 1600ms linear infinite;\n  animation: stroke-width 1600ms linear infinite;\n}\n@-webkit-keyframes svg-rotate {\n  to {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n@keyframes svg-rotate {\n  to {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n@-webkit-keyframes stroke-width {\n  0%,\n  100% {\n    stroke-width: 1;\n  }\n  45%,\n  55% {\n    stroke-width: 4;\n  }\n  50% {\n    stroke-width: 5;\n  }\n}\n@keyframes stroke-width {\n  0%,\n  100% {\n    stroke-width: 1;\n  }\n  45%,\n  55% {\n    stroke-width: 4;\n  }\n  50% {\n    stroke-width: 5;\n  }\n}\n@-webkit-keyframes stroke-dash {\n  0% {\n    stroke-dasharray: 0, 408.40704497;\n    stroke-dashoffset: 0;\n  }\n  50% {\n    stroke-dasharray: 408.40704497, 0;\n    stroke-dashoffset: 0;\n  }\n  100% {\n    stroke-dasharray: 408.40704497, 408.40704497;\n    stroke-dashoffset: -408.40704497;\n  }\n}\n@keyframes stroke-dash {\n  0% {\n    stroke-dasharray: 0, 408.40704497;\n    stroke-dashoffset: 0;\n  }\n  50% {\n    stroke-dasharray: 408.40704497, 0;\n    stroke-dashoffset: 0;\n  }\n  100% {\n    stroke-dasharray: 408.40704497, 408.40704497;\n    stroke-dashoffset: -408.40704497;\n  }\n}\n.uw-plugins-process h2 {\n  text-align: center;\n}\n.uw-plugins-process .content {\n  margin-top: 20px;\n  text-align: center;\n}\n",""])},function(e,t,n){var r=n(43);"string"==typeof r&&(r=[[e.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};n(2)(r,i);r.locals&&(e.exports=r.locals)},function(e,t,n){(e.exports=n(3)(!1)).push([e.i,".uw-plugins-rawtext {\n  word-break: break-all;\n}\n",""])},function(e,t,n){var r=n(45);"string"==typeof r&&(r=[[e.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};n(2)(r,i);r.locals&&(e.exports=r.locals)},function(e,t,n){(e.exports=n(3)(!1)).push([e.i,".uw-placeholder .content {\n  text-align: center;\n  margin-top: 25px;\n}\n.uw-placeholder .content .image {\n  display: inline-block;\n  width: 310px;\n  height: 125px;\n  text-align: left;\n  background-repeat: no-repeat;\n  background-position: center;\n}\n.uw-placeholder .content h3 {\n  font-size: 16px;\n  color: #333;\n  margin-top: 25px;\n}\n.uw-placeholder .content p {\n  font-size: 14px;\n  color: #666;\n  margin-top: 20px;\n}\n.uw-placeholder .content .footer {\n  margin-top: 20px;\n  margin-bottom: 25px;\n}\n",""])},function(e,t,n){var r=n(47);"string"==typeof r&&(r=[[e.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};n(2)(r,i);r.locals&&(e.exports=r.locals)},function(e,t){e.exports=function(e){var t="undefined"!=typeof window&&window.location;if(!t)throw new Error("fixUrls requires window.location");if(!e||"string"!=typeof e)return e;var n=t.protocol+"//"+t.host,r=n+t.pathname.replace(/\/[^\/]*$/,"/");return e.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,function(e,t){var i,o=t.trim().replace(/^"(.*)"$/,function(e,t){return t}).replace(/^'(.*)'$/,function(e,t){return t});return/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(o)?e:(i=0===o.indexOf("//")?o:0===o.indexOf("/")?n+o:r+o.replace(/^\.\//,""),"url("+JSON.stringify(i)+")")})}},function(e,t,n){(e.exports=n(3)(!1)).push([e.i,'.upload-helper .text-label {\n  height: 30px;\n  line-height: 30px;\n}\n.upload-helper .tc-15-msg {\n  margin-bottom: 10px;\n}\n.tc-15-rich-dialog .tc-15-table-panel {\n  border-top: 0;\n}\n.upload-btn-wrap {\n  background: #006eff;\n  border: 1px solid #006eff;\n  color: #fff;\n}\n.upload-btn-wrap:hover {\n  border-color: #0063e5;\n  background: #0063e5;\n  text-decoration: none;\n  color: #fff;\n}\n.upload-btn-wrap.disabled {\n  cursor: not-allowed !important;\n}\n.upload-btn-wrap.disabled:hover {\n  background: transparent;\n}\n.upload-btn-wrap.disabled input[type="file"],\n.upload-btn-wrap.disabled span {\n  cursor: not-allowed;\n}\n.file-upload-dialog {\n  min-height: 300px;\n}\n.file-upload-dialog .tc-15-table-panel .tc-15-table-fixed-body {\n  max-height: 255px;\n  overflow-y: scroll;\n  overflow-x: hidden;\n}\n',""])},function(e,t,n){var r=n(50);"string"==typeof r&&(r=[[e.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};n(2)(r,i);r.locals&&(e.exports=r.locals)}])});