/*
  <PERSON><PERSON> is pleased to support the open source community by making Metis available.
  Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
  Licensed under the BSD 3-Clause License (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at
  https://opensource.org/licenses/BSD-3-Clause
  Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
*/

const path = require('path');
const fs = require('fs');
const nunjucks = require('nunjucks');

const pageNunjucks = fs.readFileSync(path.join(__dirname, '../template/uw.njk')).toString();
nunjucks.configure('views', {
  autoescape: false,
});

module.exports = function uxLoader(source, ...extra) {
  if (this.cacheable) {
    this.cacheable();
  }

  const uwString = nunjucks.renderString(pageNunjucks, {
    uw: source,
  });
  this.callback(null, uwString, ...extra);
};