/*
  Tencent is pleased to support the open source community by making Metis available.
  Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
  Licensed under the BSD 3-Clause License (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at
  https://opensource.org/licenses/BSD-3-Clause
  Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
*/

@max-width: 1360px;

// 配色
@color-default: #FFFFFF; // 背景/控件/按钮
@color-primary: #006EFF; // 品牌主色
@color-success: #0ABF5B; // 状态正常
@color-warning: #FF9D00; // 状态警告/提示
@color-error: #E54545;   // 状态危险/错误

@color-bg: #EDEDED; // 背景
@color-header-bg: #262626; // 顶部导航背景色

@color-border: #DDDDDD; // 控件边框/间隔线
@color-border-hover: #BBBBBB; // 控件边框Hover态
@color-border-hover-bg: darken(#000, 5%); // 控件边框Hover态
@color-border-graph: #E5E5E5; // 图标内间隔线

@color-text-title: #000000; // 标题/控件
@color-text-form: #444444; // 表单文字/表格/图标
@color-text-desc: #888888; // 表单标签/说明
@color-text-disable: @color-border-hover; // 禁用
@color-text-dark: @color-default; // 深色或蓝色背景
@color-text-link: @color-primary; // 链接

@size-text-xl: 36px; // 特殊大字体
@size-text-h1: 16px; // 一级标题
@size-text-h2: 14px; // 二级标题/Tab
@size-text-default: 12px; // 三级标题/导航/正文

// 间距
@size-gap-xs: 5px;  // 文字和图标之间间距 | 优先级相同的按钮之间间距
@size-gap-s: 10px;  // 模块/卡片内部类元素间隔 | 不同优先级按钮之间的间距 | 二级页内容区标题间隔
@size-gap-m: 20px;  // 模块/卡片之间的间隔 | 卡片内容区与边框的间距 | 顶部导航各元素间距
@size-gap-l: 25px;  // 内容区标题与下文间距
@size-gap-xl: 30px; // 左侧导航上下选项间距