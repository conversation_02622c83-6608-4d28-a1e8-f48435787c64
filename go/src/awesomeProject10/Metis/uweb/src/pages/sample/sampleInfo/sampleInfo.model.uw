/*
  <PERSON><PERSON> is pleased to support the open source community by making Metis available.
  Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
  Licensed under the BSD 3-Clause License (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at
  https://opensource.org/licenses/BSD-3-Clause
  Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
*/

const moment = require('moment');
const CONFIG = require('../../constant.json');
const advanceSearchDefault = {
  positiveOrNegative: { label: '正/负样本', value: undefined, valueText: '' },
  trainOrTest: { label: '测试/训练集', value: undefined, valueText: '' },
  window: { label: '时间窗口', value: undefined, valueText: '' },
  source: { label: '样本来源', value: undefined, valueText: '' },
  viewName: { label: '指标集名称', value: undefined, valueText: '' },
  attrName: { label: '指标名称', value: undefined, valueText: '' },
};

Model('sampleInfo', {
  data: {
    dateTime: [moment().year(2018).month(9).date(1).hour(0).minute(0).second(0), moment().year(2018).month(9).date(7).hour(23).minute(59).second(59)],
    advanceSearch: _.cloneDeep(advanceSearchDefault),
    advanceSearchTemp: _.cloneDeep(advanceSearchDefault),
    dataSource: [],
    expand: false,
    loading: false,
    currentPage: CONFIG.currentPage,
    pageSize: CONFIG.pageSize,
    pageSizeOptions: CONFIG.pageSizeOptions,
    searchText: '',
    selectedRowKeys: [],
    selectedRows: [],
    total: 0,
    extendVisible: false,
    extendTitle: '',
    chartOption: {},
    tableLoading: false,
    sourceList: [],
    tableErrorMsg: '',
    onlyTimeQuery: false,
    positiveOrNegativeOption: [
      { value: 'positive', label: '正样本' },
      { value: 'negative', label: '负样本' },
    ],
    trainOrTestOption: [
      { value: 'test', label: '测试集' },
      { value: 'train', label: '训练集' },
    ],
    windowOption: [
      { value: '180', label: '180' },
      { value: '720', label: '720' },
    ],
  },

  setData(info) {
    Object.assign(this.data, info);
  },

  // 列表
  queryTrainSource() {
    uw.request({
      url: '/QueryTrainSource',
      method: 'POST',
      data: {},
      success: ({ code, data, msg }) => {
        let sourceList = [];
        for (let row of data.source) {
          sourceList.push({
            value: row,
            label: row,
          });
        }
        this.setData({ sourceList });
      },
    });
  },

  // 查询样本请求
  querySample() {
    this.setData({
      tableLoading: true,
      tableErrorMsg: '',
      onlyTimeQuery: false,
    });
    let dateTime = _.cloneDeep(this.data.dateTime);
    uw.request({
      url: '/QuerySample',
      method: 'POST',
      data: {
        requestPage: this.data.currentPage,
        itemPerPage: this.data.pageSize,
        beginTime: dateTime[0].unix(),
        endTime: dateTime[1].unix(),
        window: this.data.advanceSearch.window.value || '',
        positiveOrNegative: this.data.advanceSearch.positiveOrNegative.value || '',
        trainOrTest: this.data.advanceSearch.trainOrTest.value || '',
        source: this.data.advanceSearch.source.value || '',
        attrId: this.data.searchText || '',
        viewId: this.data.advanceSearch.viewName.value || '',
      },
      success: ({ code, data, msg }) => {
        let sample = data.sampleList;
        if (code === 0) {
          for (let row of sample) {
            const abnormalTime = moment.unix(row.time);
            row.time = abnormalTime.format('YYYY-MM-DD HH:mm');
            row.dataA = row.dataA.split(',');
            row.dataB = row.dataB.split(',');
            row.dataC = row.dataC.split(',');
            row.xAxisList = this.getXAsixList(abnormalTime, row.dataA.length - 1);
          }
          this.setData({
            dataSource: sample,
            tableLoading: false,
            total: data.totalTotal,
          });
          if (this.data.searchText === '' && $controller.sampleInfo.getAdvanceFilterResult().length === 0 && sample.length === 0) {
            this.setData({ onlyTimeQuery: true });
          }
          else {
            this.setData({ onlyTimeQuery: false });
          }
        }
        else {
          this.setData({
            tableLoading: false,
            tableErrorMsg: msg,
          });
        }
      },
    });
  },

  resetAdvanceFilter() {
    this.setData({
      advanceSearch: _.cloneDeep(advanceSearchDefault),
      advanceSearchTemp: _.cloneDeep(advanceSearchDefault),
      currentPage: 1,
    });
  },

  getXAsixList(abnormalTime, length) {
    const xAxisList = [];
    abnormalTime.subtract(length + 1, 'm');
    for (let i = 1; i <= length * 2 + 1; i++) {
      xAxisList.push(abnormalTime.add(1, 'm').format('HH:mm'));
    }
    return xAxisList;
  },

  // 重置选择
  resetSelected() {
    this.setData({
      selectedRows: [],
      selectedRowKeys: [],
    });
  },
})