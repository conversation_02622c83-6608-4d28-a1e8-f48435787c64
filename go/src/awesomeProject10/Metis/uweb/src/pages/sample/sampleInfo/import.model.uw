/*
  <PERSON><PERSON> is pleased to support the open source community by making Metis available.
  Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
  Licensed under the BSD 3-Clause License (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at
  https://opensource.org/licenses/BSD-3-Clause
  Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
*/

Model('sampleInfoImport', {
  data: {
    importSampleVisible: false,
    importSuccess: false,
    importErrorMsg: '',
    positiveCount: 0,
    negativeCount: 0,
    allSampleCount: 0,
    sampleUploaded: false,
    fileOpened: false,
    fileName: null,
  },

  setData(info) {
    Object.assign(this.data, info);
  },

  // 导入样本
  importSample(file) {
    uw.upload({
      file,
      url: '/ImportSample',
      onProgress: (status, response) => {
        switch (status) {
          case 'done':
            const { code, data, msg } = response;
            if (code === 0) {
              this.setData({
                importSuccess: true,
                positiveCount: data.positiveCount,
                negativeCount: data.negativeCount,
                allSampleCount: data.totalCount,
              });
            }
            else {
              this.setData({
                importSuccess: false,
                importErrorMsg: msg,
              });
            }
            break;
        }
      },
    }, false, 'sample_file');
  },
});