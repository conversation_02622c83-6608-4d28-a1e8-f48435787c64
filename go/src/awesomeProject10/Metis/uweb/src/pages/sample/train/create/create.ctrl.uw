/*
  <PERSON><PERSON> is pleased to support the open source community by making Metis available.
  Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
  Licensed under the BSD 3-Clause License (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at
  https://opensource.org/licenses/BSD-3-Clause
  Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
*/

const CONFIG = require('../../../constant.json');

Controller('create', {
  onLoad() {
    $model.create.queryTrainSource();
  },

  // 样本来源change回调
  onSourceChange(value) {
    $model.create.setData({
      source: value,
      sourceErrorMsg: '',
    });
  },

  onSourceBlur() {
    if ($model.create.data.source.length === 0) {
      $model.create.setData({ sourceErrorMsg: '请选择样本来源' });
    }
  },

  // 测试/训练集change回调
  onTrainOrTestChange(value) {
    $model.create.setData({
      trainOrTest: value,
      trainOrTestErrorMsg: '',
    });
  },

  onTrainOrTestBlur() {
    if ($model.create.data.trainOrTest.length === 0) {
      $model.create.setData({ trainOrTestErrorMsg: '请选择测试/训练集' });
    }
  },

  // 时间范围change回调
  onDateTimeChange(value) {
    $model.create.setData({ dateTime: value });
  },

  // 下一步事件
  onNextStep() {
    if ($model.create.data.source.length === 0) {
      $model.create.setData({ sourceErrorMsg: '请选择样本来源' });
    }
    if ($model.create.data.trainOrTest.length === 0) {
      $model.create.setData({ trainOrTestErrorMsg: '请选择测试/训练集' });
    }
    if ($model.create.data.trainOrTest.length === 0 || $model.create.data.trainOrTest.length === 0) {
      return;
    }
    if ($model.create.data.source.length > 5) {
      $model.create.setData({ sourceErrorMsg: '样本来源数量请勿超过5个' });
      return;
    }
    $model.create.querySampleCount();
    $model.create.setData({ currentStep: $model.create.data.currentStep + 1 });
  },

  getShowTrainOrTest(){
    let trainOrTest = _.cloneDeep($model.create.data.trainOrTest);
    let shows = trainOrTest.map((row) => {
      return CONFIG.sampleTrans[row];
    });
    return shows.join(', ');
  },

  // 上一步事件
  onPreStep() {
    $model.create.setData({ currentStep: $model.create.data.currentStep - 1 });
  },

  // 开始训练事件
  onTrain() {
    $model.create.trainSample();
  },

})