{"name": "metis-web", "version": "1.0.0", "description": "Front-end code of Metis", "scripts": {"start": "node lib/uw-frame/lib/cli/start", "build": "node lib/uw-frame/lib/cli/build"}, "author": "", "license": "ISC", "devDependencies": {"@types/lodash": "^4.14.104", "@types/react": "^16.3.12", "@types/react-dom": "^16.0.5", "@types/react-router-dom": "^4.2.6", "@types/webpack-env": "^1.13.5", "awesome-typescript-loader": "^5.0.0", "babel-core": "^6.26.0", "babel-loader": "^7.1.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-dynamic-import-webpack": "^1.0.2", "babel-plugin-syntax-dynamic-import": "^6.18.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "babel-preset-stage-0": "^6.24.1", "babel-runtime": "^6.26.0", "clean-webpack-plugin": "^0.1.19", "colors": "^1.3.0", "css-loader": "^0.28.10", "file-loader": "^1.1.11", "fs-extra": "^5.0.0", "html-webpack-plugin": "^3.0.6", "immutable": "3.8.2", "less": "^2.7.3", "less-loader": "^4.0.6", "nprogress": "^0.2.0", "nunjucks": "^3.1.2", "open-browser-webpack-plugin": "0.0.5", "react": "16.4.1", "react-dom": "^16.3.2", "react-router": "4.3.1", "react-router-dom": "4.2.0", "react-transition-group": "^1.2.1", "style-loader": "^0.20.2", "tslint": "^5.9.1", "tslint-react": "^3.5.1", "typescript": "^2.7.2", "webpack": "^4.1.1", "webpack-dev-server": "^3.1.0"}, "dependencies": {"classnames": "^2.2.5", "copy-to-clipboard": "^3.0.8", "isomorphic-fetch": "^2.2.1", "js-cookie": "^2.2.0", "lodash": "4.17.5", "moment": "2.17.1", "numeral": "2.0.6", "qs": "^6.5.2", "sortablejs": "^1.7.0"}}