#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
<PERSON><PERSON> is pleased to support the open source community by making Metis available.
Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
Licensed under the BSD 3-Clause License (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at
https://opensource.org/licenses/BSD-3-Clause
Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
"""

import json
from app.dao.time_series_detector.train_op import *
from app.common.errorcode import *
from app.common.common import *


class TrainService(object):

    def __init__(self):
        self.__train_op = TrainOperation()

    @exce_service
    def query_train(self, body):
        return self.__train_op.query_train(json.loads(body))

    @exce_service
    def delete_train(self, body):
        return self.__train_op.delete_train(json.loads(body))
